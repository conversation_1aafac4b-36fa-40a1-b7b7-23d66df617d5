# 🔧 Fix Lỗi Tạo <PERSON>c

## ❌ Các lỗi thường gặp:
- `Permission denied` khi tạo thư mục
- `Access denied` khi write file
- `FileNotFoundError` khi tạo thư mục con
- `OSError: [WinError 5]` trên Windows

## 🚀 Cách fix nhanh:

### ⚡ QUICK TEST:
```bash
python test_directory_creation.py
```
Script này sẽ test tất cả các vấn đề về tạo thư mục.

### Bước 1: Ch<PERSON><PERSON> với quyền Administrator
```bash
# Windows: Click chuột phải Command Prompt → "Run as Administrator"
# Sau đó chạy:
python main.py
```

### Bước 2: Kiểm tra quyền truy cập
```bash
# Test quyền tạo thư mục
python -c "
import os, tempfile
test_dir = os.path.join(tempfile.gettempdir(), 'test')
os.makedirs(test_dir, exist_ok=True)
print('✓ Có quyền tạo thư mục')
"
```

### Bước 3: Thay đổi thư mục làm việc
```bash
# Di chuyển tool vào thư mục có quyền write
# Ví dụ: Desktop, Documents, hoặc D:\
```

## 🔧 Các fix đã tích hợp trong tool:

### 1. Safe Profile Path
- Tool tự động loại bỏ ký tự không hợp lệ
- Fallback về temp directory nếu lỗi
- Test quyền write trước khi sử dụng

### 2. Extension Directory
- Retry 3 lần với tên khác nhau
- Fallback về thư mục hiện tại
- Auto cleanup khi có lỗi

### 3. Chrome Profiles
- Tạo trong temp directory an toàn
- Giới hạn độ dài tên thư mục
- Xử lý ký tự đặc biệt

## 📋 Troubleshooting:

### Lỗi "Permission denied":
1. Chạy với Administrator
2. Tắt antivirus tạm thời
3. Kiểm tra disk space
4. Thay đổi thư mục tool

### Lỗi "Access denied":
1. Kiểm tra Windows Defender
2. Thêm thư mục vào exception
3. Chạy từ thư mục khác (Desktop, D:\)

### Lỗi "Path too long":
1. Tool tự động cắt ngắn tên
2. Di chuyển tool gần root (C:\tool)
3. Sử dụng tên thư mục ngắn

## ✅ Sau khi fix:
- Tool tạo thư mục Chrome profiles thành công
- Proxy extensions hoạt động bình thường
- Không còn lỗi permission denied

## 💡 Tips:
1. **Thư mục tốt nhất**: Desktop, Documents, D:\
2. **Tránh**: Program Files, System32, Windows
3. **Luôn chạy**: Với quyền Administrator
4. **Kiểm tra**: Antivirus settings

## 📞 Nếu vẫn lỗi:
1. Chạy `python test_directory_creation.py`
2. Gửi kết quả test
3. Thử di chuyển tool sang ổ đĩa khác
4. Kiểm tra Group Policy settings
