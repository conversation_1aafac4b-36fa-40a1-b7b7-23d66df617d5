#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> để fix lỗi ChromeDriver không tương thích
"""

import os
import sys
import shutil
import requests
import zipfile
import platform
import subprocess
import logging

def setup_logging():
    """Cấu hình logging"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s'
    )
    return logging.getLogger(__name__)

def get_chrome_version():
    """Lấy version Chrome đã cài đặt"""
    logger = logging.getLogger(__name__)

    try:
        if platform.system() == "Windows":
            # Thử command line trước
            try:
                result = subprocess.run(["chrome", "--version"], capture_output=True, text=True, timeout=10)
                if result.returncode == 0 and result.stdout.strip():
                    # Parse version từ output như "Google Chrome 137.0.7151.56"
                    output = result.stdout.strip()
                    if "Google Chrome" in output:
                        version = output.split("Google Chrome")[-1].strip()
                        logger.info(f"Chrome version: {version}")
                        return version
            except:
                pass

            # Thử các đường dẫn Chrome trên Windows
            chrome_paths = [
                r"C:\Program Files\Google\Chrome\Application\chrome.exe",
                r"C:\Program Files (x86)\Google\Chrome\Application\chrome.exe",
                r"C:\Users\<USER>\AppData\Local\Google\Chrome\Application\chrome.exe".format(os.getenv('USERNAME'))
            ]

            for chrome_path in chrome_paths:
                if os.path.exists(chrome_path):
                    try:
                        result = subprocess.run([chrome_path, "--version"], capture_output=True, text=True, timeout=10)
                        if result.returncode == 0 and result.stdout.strip():
                            output = result.stdout.strip()
                            # Parse version từ output
                            if "Google Chrome" in output:
                                version = output.split("Google Chrome")[-1].strip()
                            else:
                                # Fallback: lấy từ cuối
                                parts = output.split()
                                version = parts[-1] if parts else None

                            if version and version != "session.":
                                logger.info(f"Chrome version: {version}")
                                return version
                    except Exception as e:
                        logger.debug(f"Lỗi kiểm tra {chrome_path}: {e}")
                        continue

            # Thử lấy từ registry Windows
            try:
                import winreg
                key_path = r"SOFTWARE\Google\Chrome\BLBeacon"
                with winreg.OpenKey(winreg.HKEY_CURRENT_USER, key_path) as key:
                    version, _ = winreg.QueryValueEx(key, "version")
                    logger.info(f"Chrome version từ registry: {version}")
                    return version
            except:
                pass

        else:
            # Linux/Mac
            commands = ["google-chrome", "chrome", "chromium"]
            for cmd in commands:
                try:
                    result = subprocess.run([cmd, "--version"], capture_output=True, text=True, timeout=10)
                    if result.returncode == 0 and result.stdout.strip():
                        output = result.stdout.strip()
                        if "Google Chrome" in output or "Chromium" in output:
                            version = output.split()[-1]
                            logger.info(f"Chrome version: {version}")
                            return version
                except:
                    continue

    except Exception as e:
        logger.error(f"Không thể lấy Chrome version: {e}")

    logger.warning("Không thể xác định Chrome version, sử dụng version mặc định")
    return "137.0.7151.56"  # Fallback version

def clear_webdriver_cache():
    """Xóa cache WebDriver"""
    logger = logging.getLogger(__name__)

    try:
        cache_dirs = [
            os.path.expanduser("~/.wdm"),
            os.path.expanduser("~/.cache/selenium"),
            os.path.join(os.getenv('APPDATA', ''), '.wdm') if os.name == 'nt' else None
        ]

        for cache_dir in cache_dirs:
            if cache_dir and os.path.exists(cache_dir):
                logger.info(f"Xóa cache: {cache_dir}")
                shutil.rmtree(cache_dir, ignore_errors=True)

        logger.info("✓ Đã xóa cache WebDriver")
        return True

    except Exception as e:
        logger.error(f"Lỗi xóa cache: {e}")
        return False

def download_chromedriver_manual(version=None):
    """Tải ChromeDriver thủ công với version phù hợp"""
    logger = logging.getLogger(__name__)

    try:
        # Xác định platform
        system = platform.system().lower()
        if system == "windows":
            platform_name = "win32"
            executable_name = "chromedriver.exe"
        elif system == "darwin":
            platform_name = "mac64"
            executable_name = "chromedriver"
        else:
            platform_name = "linux64"
            executable_name = "chromedriver"

        # Xác định version ChromeDriver cần tải
        if version:
            chrome_version = version.split('.')[0]  # Lấy major version
        else:
            chrome_version = "137"  # Default cho Chrome mới

        logger.info(f"Chrome version: {version}, cần ChromeDriver cho version {chrome_version}")

        # Tạo thư mục tải về
        download_dir = os.path.join(os.getcwd(), "chromedriver_manual")
        os.makedirs(download_dir, exist_ok=True)

        logger.info(f"Đang tải ChromeDriver cho {platform_name}...")

        # Thử tải ChromeDriver với version tương ứng
        download_urls = []

        if version:
            # Thử URL mới (Chrome for Testing)
            download_urls.append(f"https://storage.googleapis.com/chrome-for-testing-public/{version}/win32/chromedriver-win32.zip")
            download_urls.append(f"https://storage.googleapis.com/chrome-for-testing-public/{version}/win64/chromedriver-win64.zip")

            # Thử URL cũ
            download_urls.append(f"https://chromedriver.storage.googleapis.com/{version}/chromedriver_{platform_name}.zip")

        # Fallback URLs
        download_urls.extend([
            f"https://storage.googleapis.com/chrome-for-testing-public/137.0.7151.55/win32/chromedriver-win32.zip",
            f"https://chromedriver.storage.googleapis.com/114.0.5735.90/chromedriver_{platform_name}.zip",
            f"https://chromedriver.storage.googleapis.com/113.0.5672.63/chromedriver_{platform_name}.zip"
        ])

        zip_path = os.path.join(download_dir, "chromedriver.zip")

        # Thử tải từ các URL
        for i, download_url in enumerate(download_urls):
            try:
                logger.info(f"Thử URL {i+1}/{len(download_urls)}: {download_url}")
                response = requests.get(download_url, timeout=30)
                response.raise_for_status()

                with open(zip_path, 'wb') as f:
                    f.write(response.content)

                logger.info(f"✓ Đã tải thành công từ URL {i+1}")
                break

            except Exception as e:
                logger.warning(f"✗ URL {i+1} thất bại: {e}")
                if i == len(download_urls) - 1:
                    raise Exception("Không thể tải ChromeDriver từ bất kỳ URL nào")
                continue

        # Giải nén
        with zipfile.ZipFile(zip_path, 'r') as zip_ref:
            zip_ref.extractall(download_dir)

        # Tìm file executable
        chromedriver_path = None

        # Tìm trong thư mục gốc
        direct_path = os.path.join(download_dir, executable_name)
        if os.path.exists(direct_path):
            chromedriver_path = direct_path
        else:
            # Tìm trong các thư mục con
            for root, _, files in os.walk(download_dir):
                for file in files:
                    if file == executable_name:
                        chromedriver_path = os.path.join(root, file)
                        break
                if chromedriver_path:
                    break

        if chromedriver_path and os.path.exists(chromedriver_path):
            # Cấp quyền thực thi
            os.chmod(chromedriver_path, 0o755)
            logger.info(f"✓ Đã tải ChromeDriver: {chromedriver_path}")
            return chromedriver_path
        else:
            logger.error("Không tìm thấy ChromeDriver sau khi giải nén")
            return None

    except Exception as e:
        logger.error(f"Lỗi tải ChromeDriver thủ công: {e}")
        return None

def test_chromedriver(driver_path):
    """Test ChromeDriver có hoạt động không"""
    logger = logging.getLogger(__name__)

    try:
        from selenium import webdriver
        from selenium.webdriver.chrome.service import Service
        from selenium.webdriver.chrome.options import Options

        # Tạo options
        options = Options()
        options.add_argument("--headless")
        options.add_argument("--no-sandbox")
        options.add_argument("--disable-dev-shm-usage")

        # Tạo service
        service = Service(driver_path)

        # Test tạo driver
        driver = webdriver.Chrome(service=service, options=options)
        driver.get("https://www.google.com")
        title = driver.title
        driver.quit()

        logger.info(f"✓ ChromeDriver hoạt động bình thường. Title: {title}")
        return True

    except Exception as e:
        logger.error(f"✗ ChromeDriver không hoạt động: {e}")
        return False

def fix_chromedriver():
    """Fix ChromeDriver issue"""
    logger = setup_logging()

    print("🔧 CHROME DRIVER FIX TOOL")
    print("=" * 40)

    # Bước 1: Kiểm tra Chrome version
    print("\n1. Kiểm tra Chrome version...")
    chrome_version = get_chrome_version()
    if not chrome_version:
        print("❌ Không tìm thấy Chrome browser!")
        print("Vui lòng cài đặt Chrome từ: https://www.google.com/chrome/")
        return False

    # Bước 2: Xóa cache cũ
    print("\n2. Xóa cache WebDriver cũ...")
    clear_webdriver_cache()

    # Bước 3: Thử tải ChromeDriver tự động
    print("\n3. Thử tải ChromeDriver tự động...")
    try:
        from webdriver_manager.chrome import ChromeDriverManager
        driver_path = ChromeDriverManager().install()

        if test_chromedriver(driver_path):
            print("✅ ChromeDriver tự động hoạt động!")
            return True
        else:
            print("❌ ChromeDriver tự động không hoạt động")
    except Exception as e:
        print(f"❌ Lỗi tải ChromeDriver tự động: {e}")

    # Bước 4: Tải ChromeDriver thủ công
    print("\n4. Tải ChromeDriver thủ công...")
    manual_driver_path = download_chromedriver_manual(chrome_version)

    if manual_driver_path and test_chromedriver(manual_driver_path):
        print("✅ ChromeDriver thủ công hoạt động!")
        print(f"Đường dẫn: {manual_driver_path}")

        # Hướng dẫn sử dụng
        print("\n📋 Hướng dẫn sử dụng:")
        print("1. Copy ChromeDriver vào thư mục tool")
        print("2. Hoặc thêm vào PATH environment variable")
        print("3. Hoặc sửa code để sử dụng đường dẫn này")

        return True
    else:
        print("❌ Không thể fix ChromeDriver")

        # Gợi ý khắc phục
        print("\n🔧 Gợi ý khắc phục:")
        print("1. Cập nhật Chrome browser lên version mới nhất")
        print("2. Tắt antivirus tạm thời")
        print("3. Chạy với quyền Administrator")
        print("4. Tải ChromeDriver thủ công từ:")
        print("   https://chromedriver.chromium.org/downloads")
        print("5. Kiểm tra kiến trúc hệ thống (32-bit vs 64-bit)")

        return False

def main():
    """Main function"""
    try:
        success = fix_chromedriver()

        if success:
            print("\n🎉 FIX THÀNH CÔNG!")
            print("Bây giờ bạn có thể chạy tool đăng ký.")
        else:
            print("\n❌ FIX THẤT BẠI!")
            print("Vui lòng làm theo gợi ý khắc phục ở trên.")

        input("\nNhấn Enter để thoát...")

    except KeyboardInterrupt:
        print("\n\nĐã hủy bởi người dùng.")
    except Exception as e:
        print(f"\nLỗi không mong muốn: {e}")

if __name__ == "__main__":
    main()
