#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> để fix lỗi ChromeDriver không tương thích
"""

import os
import sys
import shutil
import requests
import zipfile
import platform
import subprocess
import logging

def setup_logging():
    """Cấu hình logging"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s'
    )
    return logging.getLogger(__name__)

def get_chrome_version():
    """Lấy version Chrome đã cài đặt"""
    logger = logging.getLogger(__name__)
    
    try:
        if platform.system() == "Windows":
            # Thử các đường dẫn Chrome trên Windows
            chrome_paths = [
                r"C:\Program Files\Google\Chrome\Application\chrome.exe",
                r"C:\Program Files (x86)\Google\Chrome\Application\chrome.exe",
                r"C:\Users\<USER>\AppData\Local\Google\Chrome\Application\chrome.exe".format(os.getenv('USERNAME'))
            ]
            
            for chrome_path in chrome_paths:
                if os.path.exists(chrome_path):
                    result = subprocess.run([chrome_path, "--version"], capture_output=True, text=True)
                    if result.returncode == 0:
                        version = result.stdout.strip().split()[-1]
                        logger.info(f"Chrome version: {version}")
                        return version
        else:
            # Linux/Mac
            result = subprocess.run(["google-chrome", "--version"], capture_output=True, text=True)
            if result.returncode == 0:
                version = result.stdout.strip().split()[-1]
                logger.info(f"Chrome version: {version}")
                return version
    except Exception as e:
        logger.error(f"Không thể lấy Chrome version: {e}")
    
    return None

def clear_webdriver_cache():
    """Xóa cache WebDriver"""
    logger = logging.getLogger(__name__)
    
    try:
        cache_dirs = [
            os.path.expanduser("~/.wdm"),
            os.path.expanduser("~/.cache/selenium"),
            os.path.join(os.getenv('APPDATA', ''), '.wdm') if os.name == 'nt' else None
        ]
        
        for cache_dir in cache_dirs:
            if cache_dir and os.path.exists(cache_dir):
                logger.info(f"Xóa cache: {cache_dir}")
                shutil.rmtree(cache_dir, ignore_errors=True)
        
        logger.info("✓ Đã xóa cache WebDriver")
        return True
        
    except Exception as e:
        logger.error(f"Lỗi xóa cache: {e}")
        return False

def download_chromedriver_manual(version=None):
    """Tải ChromeDriver thủ công"""
    logger = logging.getLogger(__name__)
    
    try:
        # Xác định platform
        system = platform.system().lower()
        if system == "windows":
            platform_name = "win32"
            executable_name = "chromedriver.exe"
        elif system == "darwin":
            platform_name = "mac64"
            executable_name = "chromedriver"
        else:
            platform_name = "linux64"
            executable_name = "chromedriver"
        
        # URL tải ChromeDriver
        if version:
            major_version = version.split('.')[0]
        else:
            major_version = "114"  # Fallback version
        
        # Thử các URL khác nhau
        urls = [
            f"https://chromedriver.storage.googleapis.com/114.0.5735.90/chromedriver_{platform_name}.zip",
            f"https://chromedriver.storage.googleapis.com/LATEST_RELEASE_{major_version}",
        ]
        
        # Tạo thư mục tải về
        download_dir = os.path.join(os.getcwd(), "chromedriver_manual")
        os.makedirs(download_dir, exist_ok=True)
        
        logger.info(f"Đang tải ChromeDriver cho {platform_name}...")
        
        # Tải file
        zip_path = os.path.join(download_dir, "chromedriver.zip")
        download_url = f"https://chromedriver.storage.googleapis.com/114.0.5735.90/chromedriver_{platform_name}.zip"
        
        response = requests.get(download_url, timeout=30)
        response.raise_for_status()
        
        with open(zip_path, 'wb') as f:
            f.write(response.content)
        
        # Giải nén
        with zipfile.ZipFile(zip_path, 'r') as zip_ref:
            zip_ref.extractall(download_dir)
        
        # Tìm file executable
        chromedriver_path = os.path.join(download_dir, executable_name)
        if not os.path.exists(chromedriver_path):
            # Tìm trong các thư mục con
            for root, dirs, files in os.walk(download_dir):
                for file in files:
                    if file == executable_name:
                        chromedriver_path = os.path.join(root, file)
                        break
        
        if os.path.exists(chromedriver_path):
            # Cấp quyền thực thi
            os.chmod(chromedriver_path, 0o755)
            logger.info(f"✓ Đã tải ChromeDriver: {chromedriver_path}")
            return chromedriver_path
        else:
            logger.error("Không tìm thấy ChromeDriver sau khi giải nén")
            return None
            
    except Exception as e:
        logger.error(f"Lỗi tải ChromeDriver thủ công: {e}")
        return None

def test_chromedriver(driver_path):
    """Test ChromeDriver có hoạt động không"""
    logger = logging.getLogger(__name__)
    
    try:
        from selenium import webdriver
        from selenium.webdriver.chrome.service import Service
        from selenium.webdriver.chrome.options import Options
        
        # Tạo options
        options = Options()
        options.add_argument("--headless")
        options.add_argument("--no-sandbox")
        options.add_argument("--disable-dev-shm-usage")
        
        # Tạo service
        service = Service(driver_path)
        
        # Test tạo driver
        driver = webdriver.Chrome(service=service, options=options)
        driver.get("https://www.google.com")
        title = driver.title
        driver.quit()
        
        logger.info(f"✓ ChromeDriver hoạt động bình thường. Title: {title}")
        return True
        
    except Exception as e:
        logger.error(f"✗ ChromeDriver không hoạt động: {e}")
        return False

def fix_chromedriver():
    """Fix ChromeDriver issue"""
    logger = setup_logging()
    
    print("🔧 CHROME DRIVER FIX TOOL")
    print("=" * 40)
    
    # Bước 1: Kiểm tra Chrome version
    print("\n1. Kiểm tra Chrome version...")
    chrome_version = get_chrome_version()
    if not chrome_version:
        print("❌ Không tìm thấy Chrome browser!")
        print("Vui lòng cài đặt Chrome từ: https://www.google.com/chrome/")
        return False
    
    # Bước 2: Xóa cache cũ
    print("\n2. Xóa cache WebDriver cũ...")
    clear_webdriver_cache()
    
    # Bước 3: Thử tải ChromeDriver tự động
    print("\n3. Thử tải ChromeDriver tự động...")
    try:
        from webdriver_manager.chrome import ChromeDriverManager
        driver_path = ChromeDriverManager().install()
        
        if test_chromedriver(driver_path):
            print("✅ ChromeDriver tự động hoạt động!")
            return True
        else:
            print("❌ ChromeDriver tự động không hoạt động")
    except Exception as e:
        print(f"❌ Lỗi tải ChromeDriver tự động: {e}")
    
    # Bước 4: Tải ChromeDriver thủ công
    print("\n4. Tải ChromeDriver thủ công...")
    manual_driver_path = download_chromedriver_manual(chrome_version)
    
    if manual_driver_path and test_chromedriver(manual_driver_path):
        print("✅ ChromeDriver thủ công hoạt động!")
        print(f"Đường dẫn: {manual_driver_path}")
        
        # Hướng dẫn sử dụng
        print("\n📋 Hướng dẫn sử dụng:")
        print("1. Copy ChromeDriver vào thư mục tool")
        print("2. Hoặc thêm vào PATH environment variable")
        print("3. Hoặc sửa code để sử dụng đường dẫn này")
        
        return True
    else:
        print("❌ Không thể fix ChromeDriver")
        
        # Gợi ý khắc phục
        print("\n🔧 Gợi ý khắc phục:")
        print("1. Cập nhật Chrome browser lên version mới nhất")
        print("2. Tắt antivirus tạm thời")
        print("3. Chạy với quyền Administrator")
        print("4. Tải ChromeDriver thủ công từ:")
        print("   https://chromedriver.chromium.org/downloads")
        print("5. Kiểm tra kiến trúc hệ thống (32-bit vs 64-bit)")
        
        return False

def main():
    """Main function"""
    try:
        success = fix_chromedriver()
        
        if success:
            print("\n🎉 FIX THÀNH CÔNG!")
            print("Bây giờ bạn có thể chạy tool đăng ký.")
        else:
            print("\n❌ FIX THẤT BẠI!")
            print("Vui lòng làm theo gợi ý khắc phục ở trên.")
        
        input("\nNhấn Enter để thoát...")
        
    except KeyboardInterrupt:
        print("\n\nĐã hủy bởi người dùng.")
    except Exception as e:
        print(f"\nLỗi không mong muốn: {e}")

if __name__ == "__main__":
    main()
