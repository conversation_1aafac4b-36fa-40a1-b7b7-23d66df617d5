# Khắc phục lỗi Chrome - 13Win16 Auto Registration Tool

## 🔧 Các lỗi thường gặp và cách khắc phục

### 1. Lỗi "Không thể tạo Chrome instance"

**Nguyên nhân:**
- Chrome browser chưa cài đặt
- ChromeDriver không tương thích
- Antivirus block Chrome
- Thiếu quyền truy cập

**Cách khắc phục:**

#### Bước 1: Kiểm tra Chrome browser
```bash
# Kiểm tra Chrome đã cài đặt chưa
chrome --version
# hoặc
google-chrome --version
```

Nếu chưa có Chrome:
- Tải và cài đặt từ: https://www.google.com/chrome/
- Khởi động lại máy tính sau khi cài đặt

#### Bước 2: Test Chrome cơ bản
```bash
python test_chrome.py
```

#### Bước 3: Kiểm tra antivirus
- Tạm tắt antivirus/Windows Defender
- Thêm thư mục tool vào whitelist
- Thêm Chrome vào exception list

#### Bước 4: <PERSON><PERSON><PERSON> vớ<PERSON> quyền Administrator
- Click chuột phải vào Command Prompt
- Chọn "Run as Administrator"
- Chạy lại tool

### 2. Lỗi "ChromeDriver not found"

**Cách khắc phục:**
```bash
# Xóa cache ChromeDriver cũ
rm -rf ~/.wdm  # Linux/Mac
rmdir /s %USERPROFILE%\.wdm  # Windows

# Cài đặt lại webdriver-manager
pip uninstall webdriver-manager
pip install webdriver-manager==4.0.1
```

### 3. Lỗi "Chrome crashed" hoặc "Chrome not reachable"

**Cách khắc phục:**

#### Option 1: Thêm Chrome arguments
Trong file `src/win13_registration_service.py`, thêm:
```python
options.add_argument("--disable-extensions")
options.add_argument("--disable-plugins")
options.add_argument("--disable-images")
options.add_argument("--disable-javascript")  # Nếu không cần JS
```

#### Option 2: Tăng timeout
```python
driver.set_page_load_timeout(60)  # Tăng từ 30 lên 60 giây
```

#### Option 3: Chạy headless mode
```python
options.add_argument("--headless")  # Chạy ẩn
```

### 4. Lỗi "Permission denied" hoặc "Access denied"

**Cách khắc phục:**
```bash
# Windows: Chạy với quyền Administrator
# Linux/Mac: Sử dụng sudo hoặc thay đổi permissions
chmod +x chrome
chmod +x chromedriver
```

### 5. Lỗi "Port already in use"

**Cách khắc phục:**
```bash
# Tìm và kill process Chrome đang chạy
# Windows:
taskkill /f /im chrome.exe
taskkill /f /im chromedriver.exe

# Linux/Mac:
pkill chrome
pkill chromedriver
```

### 6. Lỗi proxy authentication

**Cách khắc phục:**
- Kiểm tra username/password proxy đúng
- Thử proxy không có authentication trước
- Sử dụng proxy HTTP thay vì SOCKS

### 7. Lỗi "Element not found" khi điền form

**Cách khắc phục:**
- Kiểm tra URL đăng ký đúng: https://13win16.com/register
- Website có thể thay đổi cấu trúc
- Tăng thời gian chờ load trang

## 🛠️ Tools debug

### Test Chrome cơ bản:
```bash
python test_chrome.py
```

### Test với log chi tiết:
```bash
python -c "
import logging
logging.basicConfig(level=logging.DEBUG)
from src.win13_registration_service import Win13RegistrationService
service = Win13RegistrationService()
driver = service.create_chrome_instance()
if driver:
    print('SUCCESS')
    driver.quit()
else:
    print('FAILED')
"
```

### Kiểm tra ChromeDriver version:
```bash
python -c "
from webdriver_manager.chrome import ChromeDriverManager
print(ChromeDriverManager().install())
"
```

## 📋 Checklist khắc phục

- [ ] Chrome browser đã cài đặt và cập nhật
- [ ] Antivirus đã tắt hoặc whitelist
- [ ] Chạy với quyền Administrator
- [ ] Không có Chrome process nào đang chạy
- [ ] Kết nối internet ổn định
- [ ] Dependencies đã cài đặt đầy đủ
- [ ] Thư mục tool có quyền write

## 🔍 Log files để debug

- `chrome_test.log` - Log test Chrome
- `registration.log` - Log chính của tool
- Chrome crash logs (Windows): `%LOCALAPPDATA%\Google\Chrome\User Data\Crashpad\reports\`

## 📞 Hỗ trợ thêm

Nếu vẫn gặp lỗi:
1. Chạy `python test_chrome.py` và gửi kết quả
2. Gửi file log `chrome_test.log`
3. Ghi rõ hệ điều hành và version Chrome
4. Mô tả chi tiết lỗi và bước tái hiện

## ⚡ Quick fixes

### Fix nhanh nhất:
```bash
# 1. Tắt antivirus
# 2. Chạy với Administrator
# 3. Restart máy tính
# 4. Chạy lại tool
```

### Nếu vẫn lỗi:
```bash
# Cài đặt lại Chrome
# Xóa thư mục .wdm
# Cài đặt lại dependencies
pip install -r requirements.txt --force-reinstall
```
