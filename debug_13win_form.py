#!/usr/bin/env python3
"""
Debug script để kiểm tra form đăng ký 13win
"""

import sys
import os
import time

# Thêm src vào path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service

def debug_13win_form():
    """Debug form đăng ký 13win"""
    print("🔍 DEBUG 13WIN FORM")
    print("=" * 50)
    
    driver = None
    try:
        # Setup Chrome
        options = Options()
        options.add_argument("--no-sandbox")
        options.add_argument("--disable-dev-shm-usage")
        
        # Sử dụng ChromeDriver local nếu có
        chromedriver_path = "chromedriver.exe"
        if os.path.exists(chromedriver_path):
            service = Service(chromedriver_path)
            print(f"✓ Sử dụng ChromeDriver local: {chromedriver_path}")
        else:
            from webdriver_manager.chrome import ChromeDriverManager
            service = Service(ChromeDriverManager().install())
            print("✓ Sử dụng ChromeDriver từ WebDriverManager")
        
        driver = webdriver.Chrome(service=service, options=options)
        print("✓ Đã khởi tạo Chrome")
        
        # Điều hướng đến 13win
        url = "https://13win16.com"  # Cập nhật URL chính xác
        print(f"📍 Điều hướng đến: {url}")
        
        driver.get(url)
        time.sleep(5)
        
        # Log thông tin trang
        current_url = driver.current_url
        page_title = driver.title
        print(f"Current URL: {current_url}")
        print(f"Page title: {page_title}")
        
        # Tìm link đăng ký
        print("\n🔍 Tìm link đăng ký...")
        register_links = []
        
        # Thử các selector khác nhau
        link_selectors = [
            'a[href*="register"]',
            'a[href*="signup"]', 
            'a[href*="dang-ky"]',
            'a:contains("Đăng ký")',
            'a:contains("Register")',
            'a:contains("Sign up")',
            '.register',
            '.signup',
            '#register',
            '#signup'
        ]
        
        for selector in link_selectors:
            try:
                if 'contains' in selector:
                    # XPath cho text content
                    xpath = f"//a[contains(text(), '{selector.split('\"')[1]}')]"
                    links = driver.find_elements(By.XPATH, xpath)
                else:
                    links = driver.find_elements(By.CSS_SELECTOR, selector)
                
                if links:
                    for link in links:
                        href = link.get_attribute('href')
                        text = link.text.strip()
                        print(f"  ✓ Tìm thấy: '{text}' -> {href}")
                        register_links.append((link, href, text))
            except Exception as e:
                print(f"  ✗ Lỗi selector '{selector}': {e}")
        
        if not register_links:
            print("❌ Không tìm thấy link đăng ký")
            print("\n📋 Tất cả links trên trang:")
            try:
                all_links = driver.find_elements(By.TAG_NAME, "a")
                for i, link in enumerate(all_links[:10]):  # First 10 links
                    href = link.get_attribute('href') or 'no-href'
                    text = link.text.strip() or 'no-text'
                    print(f"  {i+1}. '{text}' -> {href}")
            except:
                pass
            return False
        
        # Click vào link đăng ký đầu tiên
        print(f"\n🖱️ Click vào link đăng ký: {register_links[0][2]}")
        register_links[0][0].click()
        time.sleep(5)
        
        # Kiểm tra trang đăng ký
        current_url = driver.current_url
        page_title = driver.title
        print(f"Register page URL: {current_url}")
        print(f"Register page title: {page_title}")
        
        # Tìm form đăng ký
        print("\n📝 Phân tích form đăng ký...")
        
        # Tìm tất cả forms
        forms = driver.find_elements(By.TAG_NAME, "form")
        print(f"Tìm thấy {len(forms)} form(s)")
        
        # Tìm tất cả inputs
        inputs = driver.find_elements(By.TAG_NAME, "input")
        print(f"Tìm thấy {len(inputs)} input(s):")
        
        for i, inp in enumerate(inputs):
            try:
                name = inp.get_attribute("name") or "no-name"
                id_attr = inp.get_attribute("id") or "no-id"
                type_attr = inp.get_attribute("type") or "text"
                placeholder = inp.get_attribute("placeholder") or "no-placeholder"
                class_attr = inp.get_attribute("class") or "no-class"
                
                print(f"  {i+1}. name='{name}', id='{id_attr}', type='{type_attr}'")
                print(f"      placeholder='{placeholder}', class='{class_attr}'")
                
            except Exception as e:
                print(f"  {i+1}. Error reading input: {e}")
        
        # Tìm buttons
        buttons = driver.find_elements(By.TAG_NAME, "button")
        print(f"\nTìm thấy {len(buttons)} button(s):")
        
        for i, btn in enumerate(buttons):
            try:
                text = btn.text.strip() or "no-text"
                type_attr = btn.get_attribute("type") or "button"
                class_attr = btn.get_attribute("class") or "no-class"
                
                print(f"  {i+1}. text='{text}', type='{type_attr}', class='{class_attr}'")
                
            except Exception as e:
                print(f"  {i+1}. Error reading button: {e}")
        
        # Test điền form
        print("\n✏️ Test điền form...")
        
        # Thử điền username
        username_selectors = [
            'input[name="username"]',
            'input[id="username"]', 
            'input[placeholder*="username"]',
            'input[placeholder*="tên"]'
        ]
        
        username_filled = False
        for selector in username_selectors:
            try:
                username_input = driver.find_element(By.CSS_SELECTOR, selector)
                username_input.clear()
                username_input.send_keys("testuser123")
                print(f"✓ Đã điền username với selector: {selector}")
                username_filled = True
                break
            except:
                continue
        
        if not username_filled:
            print("❌ Không thể điền username")
        
        # Chờ để user có thể xem
        print("\n⏳ Chờ 10 giây để bạn có thể xem form...")
        time.sleep(10)
        
        return True
        
    except Exception as e:
        print(f"❌ Lỗi: {e}")
        return False
        
    finally:
        if driver:
            try:
                driver.quit()
                print("✓ Đã đóng browser")
            except:
                pass

def main():
    """Main function"""
    try:
        success = debug_13win_form()
        
        if success:
            print("\n🎉 DEBUG THÀNH CÔNG!")
            print("Kiểm tra log để xem cấu trúc form.")
        else:
            print("\n❌ DEBUG THẤT BẠI!")
            print("Kiểm tra URL và cấu trúc website.")
            
    except KeyboardInterrupt:
        print("\n\nĐã hủy bởi người dùng.")
    except Exception as e:
        print(f"\nLỗi không mong muốn: {e}")

if __name__ == "__main__":
    main()
