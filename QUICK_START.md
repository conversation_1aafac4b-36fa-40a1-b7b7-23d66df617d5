# Hướng dẫn nhanh - 13Win16 Auto Registration Tool

## 🚀 Khởi động nhanh

### Bước 1: <PERSON><PERSON>i đặt
```bash
# Cài đặt Python dependencies
pip install -r requirements.txt
```

### Bước 2: Chạy tool
```bash
# Chạy trực tiếp
python main.py

# Hoặc sử dụng file batch (Windows)
run_tool.bat
```

### Bước 3: Sử dụng giao diện

1. **Tab "Đăng ký tài khoản"**:
   - Nhập số lượng tài khoản (1-100)
   - Chọn số Chrome đồng thời (1-10)
   - Click "Bắt đầu đăng ký"

2. **Tab "Proxy"** (tù<PERSON> chọn):
   - Click "Fetch proxy miễn phí" để lấy proxy tự động
   - Hoặc "Load từ file" để tải proxy từ file .txt
   - Click "Test tất cả" để kiểm tra proxy

3. **<PERSON> dõ<PERSON> kết quả**:
   - Xem progress bar và thống kê real-time
   - <PERSON><PERSON><PERSON> tra logs chi tiết trong tab "Logs":
     - Ti<PERSON><PERSON> trình fetch proxy: "[1/3] Fetching proxy từ nguồn..."
     - Tiến trình test proxy: "Test proxy: 50/100 (50.0%) - Hoạt động: 15"
     - Kết quả từng proxy: "✓ Proxy hoạt động: ***********:8080 (250ms)"
   - Lưu kết quả bằng nút "Lưu kết quả"

## 📋 Format file proxy

Tạo file `proxies.txt` với format:
```
***********:8080
proxy.example.com:3128:username:password
```

## ⚙️ Cài đặt nhanh

- **URL đăng ký**: https://13win16.com/register (có thể thay đổi)
- **Delay**: 2 giây (khuyến nghị)
- **Chrome đồng thời**: 3 instances (khuyến nghị)

## 🔧 Xử lý lỗi thường gặp

1. **Lỗi ChromeDriver**: Tool tự động tải, đảm bảo Chrome đã cài đặt
2. **Lỗi proxy**: Sử dụng "Fetch proxy miễn phí" hoặc không dùng proxy
3. **Lỗi form**: Kiểm tra URL đăng ký trong tab "Cài đặt"

## 📊 Kết quả

- Tài khoản thành công hiển thị trong bảng
- Có thể xuất Excel/CSV từ tab "Tài khoản"
- File log tự động lưu: `registration.log`

## ⚠️ Lưu ý

- Chỉ sử dụng cho mục đích hợp pháp
- Không spam quá nhiều tài khoản
- Sử dụng proxy để tránh bị block IP
