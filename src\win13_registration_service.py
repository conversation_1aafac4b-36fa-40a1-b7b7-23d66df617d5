"""
Service đăng ký tự động tài khoản 13win16
"""

import time
import random
import logging
import os
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service
from selenium.common.exceptions import TimeoutException, NoSuchElementException
from webdriver_manager.chrome import ChromeDriverManager
from account_generator import AccountGenerator
from chrome_stability_manager import ChromeStabilityManager
from bonus_claimer import BonusClaimer
import threading

class Win13RegistrationService:
    def __init__(self, proxy_manager=None):
        self.proxy_manager = proxy_manager
        self.account_generator = AccountGenerator()
        self.chrome_manager = ChromeStabilityManager()  # Add Chrome stability manager
        self.logger = logging.getLogger(__name__)
        self.registration_url = "https://13win16.com"  # URL sẽ auto-redirect đến /home/<USER>
        self.active_browsers = []
        self.registration_results = []
        self.validate_proxy = False  # Disable validation để ưu tiên tốc độ và sử dụng proxy
        self.auto_claim_bonus = True  # Enable auto bonus claiming by default
        self.bonus_claimer = BonusClaimer()  # Bonus claimer instance
        self.fast_mode = True  # Enable fast mode for quicker operations
        self.no_proxy_mode = False  # Enable no-proxy mode for faster but single account registration
        self.vietnam_proxy_priority = True  # Prioritize Vietnam proxies to avoid IP blocking

    def create_chrome_instance(self, proxy=None, profile_path=None):
        """Tạo Chrome instance với ultra stability và proxy fallback"""
        self.logger.info("🚀 Creating ultra-stable Chrome instance...")

        # Check no-proxy mode first
        if self.no_proxy_mode:
            self.logger.info("🚫 No-proxy mode enabled - tạo Chrome không proxy")
            driver = self.chrome_manager.create_stable_driver(force_no_proxy=True)
            if driver:
                self.logger.info("✅ Ultra-stable Chrome without proxy created successfully!")
                return driver
            else:
                self.logger.error("❌ Failed to create Chrome without proxy")
                return None

        # Thử với proxy trước (nếu không phải no-proxy mode)
        if proxy:
            self.logger.info(f"🔄 Trying with proxy: {proxy['host']}:{proxy['port']}")
            driver = self.chrome_manager.create_stable_driver(proxy)

            if driver:
                # Test navigation với proxy
                try:
                    driver.get("data:text/html,<html><body><h1>Test</h1></body></html>")
                    self.logger.info("✅ Ultra-stable Chrome with proxy created successfully!")
                    return driver
                except Exception as nav_error:
                    self.logger.warning(f"⚠️ Proxy navigation failed: {nav_error}")
                    # Cleanup failed driver
                    try:
                        self.chrome_manager.cleanup_driver(driver)
                    except:
                        pass
                    driver = None

        # Fallback: Thử không proxy
        self.logger.info("🔄 Fallback: Trying without proxy...")
        driver = self.chrome_manager.create_stable_driver(force_no_proxy=True)

        if driver:
            self.logger.info("✅ Ultra-stable Chrome without proxy created successfully!")
            return driver
        else:
            self.logger.error("❌ Failed to create ultra-stable Chrome (both with and without proxy)")
            return None



    def _create_safe_profile_path(self, profile_path):
        """Tạo đường dẫn profile an toàn"""
        import re
        import tempfile

        try:
            # Loại bỏ ký tự không hợp lệ
            safe_name = re.sub(r'[<>:"/\\|?*]', '_', profile_path)
            safe_name = safe_name.replace(' ', '_')

            # Giới hạn độ dài
            if len(safe_name) > 50:
                safe_name = safe_name[:50]

            # Tạo đường dẫn trong temp directory
            temp_dir = tempfile.gettempdir()
            safe_path = os.path.join(temp_dir, "chrome_profiles", safe_name)

            return safe_path

        except Exception as e:
            self.logger.warning(f"Lỗi tạo safe profile path: {e}")
            # Fallback
            return os.path.join(tempfile.gettempdir(), f"chrome_profile_{random.randint(1000, 9999)}")

    def _create_proxy_auth_extension(self, proxy, options):
        """Tạo extension cho proxy authentication"""
        import os
        import tempfile

        try:
            # Tạo thư mục temp cho extension với error handling
            max_attempts = 3
            extension_dir = None

            for attempt in range(max_attempts):
                try:
                    extension_name = f"proxy_ext_{random.randint(1000, 9999)}_{attempt}"
                    extension_dir = os.path.join(tempfile.gettempdir(), extension_name)

                    # Tạo thư mục
                    os.makedirs(extension_dir, exist_ok=True)

                    # Test quyền write
                    test_file = os.path.join(extension_dir, "test.tmp")
                    with open(test_file, 'w') as f:
                        f.write("test")
                    os.remove(test_file)

                    self.logger.info(f"Đã tạo extension directory: {extension_dir}")
                    break

                except Exception as dir_error:
                    self.logger.warning(f"Lần thử {attempt + 1} tạo extension dir thất bại: {dir_error}")
                    if attempt == max_attempts - 1:
                        # Fallback về thư mục hiện tại
                        extension_dir = os.path.join(os.getcwd(), f"temp_proxy_ext_{random.randint(1000, 9999)}")
                        os.makedirs(extension_dir, exist_ok=True)
                        self.logger.info(f"Fallback extension directory: {extension_dir}")

            if not extension_dir:
                raise Exception("Không thể tạo extension directory")

            manifest_json = {
                "version": "1.0.0",
                "manifest_version": 2,
                "name": "Chrome Proxy Auth",
                "permissions": [
                    "proxy",
                    "tabs",
                    "unlimitedStorage",
                    "storage",
                    "<all_urls>",
                    "webRequest",
                    "webRequestBlocking"
                ],
                "background": {
                    "scripts": ["background.js"]
                },
                "minimum_chrome_version": "22.0.0"
            }

            background_js = f"""
var config = {{
    mode: "fixed_servers",
    rules: {{
        singleProxy: {{
            scheme: "{proxy.get('type', 'http')}",
            host: "{proxy['host']}",
            port: parseInt({proxy['port']})
        }},
        bypassList: ["localhost"]
    }}
}};

chrome.proxy.settings.set({{value: config, scope: "regular"}}, function() {{}});

function callbackFn(details) {{
    return {{
        authCredentials: {{
            username: "{proxy['username']}",
            password: "{proxy['password']}"
        }}
    }};
}}

chrome.webRequest.onAuthRequired.addListener(
    callbackFn,
    {{urls: ["<all_urls>"]}},
    ['blocking']
);
"""

            # Tạo files
            import json
            with open(os.path.join(extension_dir, "manifest.json"), "w", encoding='utf-8') as f:
                json.dump(manifest_json, f, indent=2)

            with open(os.path.join(extension_dir, "background.js"), "w", encoding='utf-8') as f:
                f.write(background_js)

            # Thêm extension vào Chrome
            options.add_argument(f"--load-extension={extension_dir}")

            return extension_dir

        except Exception as e:
            self.logger.error(f"Lỗi tạo proxy auth extension: {str(e)}")
            raise e

    def quick_validate_proxy(self, proxy_info):
        """Quick validation proxy trước khi sử dụng"""
        try:
            import requests

            proxy_dict = {
                'http': f"http://{proxy_info.host}:{proxy_info.port}",
                'https': f"http://{proxy_info.host}:{proxy_info.port}"
            }

            # Nếu có auth
            if proxy_info.username and proxy_info.password:
                proxy_dict = {
                    'http': f"http://{proxy_info.username}:{proxy_info.password}@{proxy_info.host}:{proxy_info.port}",
                    'https': f"http://{proxy_info.username}:{proxy_info.password}@{proxy_info.host}:{proxy_info.port}"
                }

            # Quick test với timeout rất ngắn để ưu tiên tốc độ
            response = requests.get(
                'http://httpbin.org/ip',
                proxies=proxy_dict,
                timeout=3,  # Timeout rất ngắn - ưu tiên tốc độ
                headers={'User-Agent': 'Mozilla/5.0'}
            )

            return response.status_code == 200

        except Exception as e:
            self.logger.debug(f"Quick proxy validation failed: {e}")
            return False

    def fill_registration_form(self, driver, account_data):
        """Điền form đăng ký tự động cho 13win"""
        try:
            self.logger.info("Bắt đầu điền form đăng ký...")

            # Chờ trang load và tìm form với retry
            self.logger.info("Chờ trang load...")

            # Chờ document ready
            WebDriverWait(driver, 15).until(
                lambda d: d.execute_script("return document.readyState") == "complete"
            )

            # Fast mode - Giảm delay
            if self.fast_mode:
                self.logger.info("⚡ Fast mode: Chờ JavaScript load nhanh...")
                time.sleep(2)  # Giảm từ 5s xuống 2s
            else:
                # Chờ thêm để JS load xong và render form
                self.logger.info("Chờ JavaScript load form...")
                time.sleep(5)

            # Retry mechanism để chờ form xuất hiện
            max_form_wait = 10
            form_appeared = False

            for wait_attempt in range(max_form_wait):
                try:
                    inputs = driver.find_elements(By.TAG_NAME, "input")
                    if len(inputs) > 0:
                        self.logger.info(f"✓ Form đã xuất hiện với {len(inputs)} inputs")
                        form_appeared = True
                        break
                    else:
                        self.logger.info(f"Chờ form xuất hiện... ({wait_attempt + 1}/{max_form_wait})")
                        time.sleep(1)
                except:
                    time.sleep(1)

            if not form_appeared:
                self.logger.warning("Form không xuất hiện sau khi chờ")

            # Log current URL để debug
            current_url = driver.current_url
            self.logger.info(f"Current URL: {current_url}")

            # Tìm form đăng ký - thử nhiều selector
            form_selectors = [
                "form",
                ".register-form",
                "#register-form",
                ".signup-form",
                "#signup-form",
                "[class*='register']",
                "[class*='signup']"
            ]

            form_found = False
            for selector in form_selectors:
                try:
                    forms = driver.find_elements(By.CSS_SELECTOR, selector)
                    if forms:
                        self.logger.info(f"Tìm thấy {len(forms)} form với selector: {selector}")
                        form_found = True
                        break
                except:
                    continue

            if not form_found:
                self.logger.warning("Không tìm thấy form đăng ký")
                # Log page source để debug
                page_source = driver.page_source[:1000]  # First 1000 chars
                self.logger.debug(f"Page source preview: {page_source}")

            # Mapping các field cho 13win (dựa trên placeholder thực tế đã test)
            field_mappings = {
                'username': [
                    'input[placeholder="Nhập Số điện thoại/Tên Đăng Nhập"]',
                    'input[placeholder*="điện thoại"]',
                    'input[placeholder*="Tên Đăng Nhập"]',
                    'input[name="username"]', 'input[id="username"]', '#username'
                ],
                'password': [
                    'input[placeholder="Mật khẩu"]',
                    'input[placeholder*="Mật khẩu"]',
                    'input[name="password"]', 'input[id="password"]', '#password',
                    'input[type="password"]'
                ],
                'confirm_password': [
                    'input[placeholder="Vui lòng xác nhận lại mật khẩu !"]',
                    'input[placeholder*="Vui lòng xác nhận lại mật khẩu"]',
                    'input[placeholder*="xác nhận lại mật khẩu"]',
                    'input[placeholder*="xác nhận"]',
                    'input[name="confirm_password"]', 'input[id="confirm_password"]'
                ],
                'full_name': [
                    'input[placeholder="Họ Tên Thật"]',
                    'input[placeholder*="Họ Tên"]',
                    'input[placeholder*="tên thật"]',
                    'input[name="full_name"]', 'input[id="full_name"]', '#full_name'
                ],
                'email': [
                    'input[name="email"]', 'input[id="email"]', '#email',
                    'input[type="email"]', 'input[placeholder*="email"]'
                ],
                'phone': [
                    'input[name="phone"]', 'input[id="phone"]', '#phone',
                    'input[type="tel"]', 'input[placeholder*="phone"]'
                ]
            }

            # Điền từng field với improved error handling - ưu tiên theo thứ tự
            filled_fields = 0

            # Thứ tự điền form cho 13win
            field_order = ['username', 'password', 'confirm_password', 'full_name']

            for field_name in field_order:
                if field_name in field_mappings:
                    # Xác định value cho field
                    if field_name == 'confirm_password':
                        value = account_data['password']  # Dùng password cho confirm
                    elif field_name in account_data:
                        value = account_data[field_name]
                    else:
                        self.logger.warning(f"⚠️ Không có data cho field: {field_name}")
                        continue

                    self.logger.info(f"🔍 Đang tìm field: {field_name} (value: {value})")
                    selectors = field_mappings[field_name]
                    element = self._find_element_by_selectors(driver, selectors)

                    if element:
                        try:
                            # Scroll đến element
                            driver.execute_script("arguments[0].scrollIntoView(true);", element)
                            time.sleep(0.5)

                            # Highlight element với màu khác nhau
                            colors = {
                                'username': 'red',
                                'password': 'blue',
                                'confirm_password': 'green',
                                'full_name': 'orange'
                            }
                            color = colors.get(field_name, 'purple')
                            driver.execute_script(f"arguments[0].style.border='3px solid {color}'", element)

                            if element.tag_name == 'select':
                                self._select_option(element, value)
                            else:
                                # Clear và điền
                                element.clear()
                                time.sleep(0.3)
                                self._type_slowly(element, str(value))

                            filled_fields += 1
                            self.logger.info(f"✅ Đã điền {field_name}: {value}")

                            # Fast mode - Giảm delay giữa fields
                            if self.fast_mode:
                                time.sleep(random.uniform(0.3, 0.7))  # Giảm từ 1-2s xuống 0.3-0.7s
                            else:
                                time.sleep(random.uniform(1, 2))

                        except Exception as field_error:
                            self.logger.error(f"❌ Lỗi điền field {field_name}: {field_error}")
                    else:
                        self.logger.warning(f"⚠️ Không tìm thấy field: {field_name}")

                        # Debug: log tất cả inputs để tìm field bị thiếu
                        try:
                            inputs = driver.find_elements(By.TAG_NAME, "input")
                            self.logger.debug(f"📋 Available inputs: {len(inputs)}")
                            for i, inp in enumerate(inputs):
                                placeholder = inp.get_attribute("placeholder") or "no-placeholder"
                                type_attr = inp.get_attribute("type") or "no-type"
                                self.logger.debug(f"  Input {i+1}: type='{type_attr}', placeholder='{placeholder}'")
                        except:
                            pass

            self.logger.info(f"📊 Đã điền {filled_fields}/{len(field_order)} fields cần thiết")

            # Xử lý checkbox/terms nếu có
            self._handle_terms_checkbox(driver)

            # Xử lý captcha nếu có
            self._handle_captcha(driver)

            # Chờ một chút trước khi submit
            time.sleep(2)

            # Auto submit nếu điền đủ fields
            if filled_fields >= 3:  # Cần ít nhất username, password, full_name
                self.logger.info("🚀 Đã điền đủ fields, chuẩn bị submit...")
                submit_success = self.submit_registration(driver)
                if submit_success:
                    self.logger.info("✅ Submit form thành công!")
                    return True  # Return True ngay khi submit thành công
                else:
                    self.logger.warning("⚠️ Submit form thất bại")
                    return False  # Return False nếu submit thất bại
            else:
                self.logger.warning(f"⚠️ Chỉ điền được {filled_fields}/4 fields, không đủ để submit")

            return filled_fields >= 3  # Return True nếu điền được ít nhất 3 fields

        except Exception as e:
            self.logger.error(f"Lỗi điền form: {str(e)}")
            # Log page source để debug
            try:
                page_title = driver.title
                self.logger.error(f"Page title: {page_title}")
            except:
                pass
            return False

    def _find_element_by_selectors(self, driver, selectors):
        """Tìm element bằng nhiều selector (hỗ trợ CSS và XPath)"""
        for selector in selectors:
            try:
                if ':contains(' in selector:
                    # Convert CSS :contains() to XPath
                    if selector.startswith('button:contains('):
                        text = selector.split('"')[1]
                        xpath = f"//button[contains(text(), '{text}')]"
                        element = driver.find_element(By.XPATH, xpath)
                    else:
                        # Fallback CSS selector
                        element = driver.find_element(By.CSS_SELECTOR, selector.split(':contains(')[0])
                elif selector.startswith('#') or selector.startswith('.'):
                    element = driver.find_element(By.CSS_SELECTOR, selector)
                elif '[' in selector:
                    element = driver.find_element(By.CSS_SELECTOR, selector)
                else:
                    element = driver.find_element(By.NAME, selector)
                return element
            except NoSuchElementException:
                continue
            except Exception as e:
                self.logger.debug(f"Lỗi selector '{selector}': {e}")
                continue
        return None

    def _type_slowly(self, element, text):
        """Gõ với tốc độ tùy chỉnh"""
        element.clear()

        if self.fast_mode:
            # Fast mode - Gõ nhanh hơn
            for char in text:
                element.send_keys(char)
                time.sleep(random.uniform(0.02, 0.05))  # Giảm từ 0.05-0.15s xuống 0.02-0.05s
        else:
            # Normal mode - Gõ chậm để giống người thật
            for char in text:
                element.send_keys(char)
                time.sleep(random.uniform(0.05, 0.15))

    def _select_option(self, select_element, value):
        """Chọn option trong select"""
        from selenium.webdriver.support.ui import Select
        select = Select(select_element)
        try:
            select.select_by_value(str(value))
        except:
            try:
                select.select_by_visible_text(str(value))
            except:
                # Chọn option đầu tiên nếu không tìm thấy
                if len(select.options) > 1:
                    select.select_by_index(1)

    def _handle_terms_checkbox(self, driver):
        """Xử lý checkbox điều khoản cho 13win"""
        try:
            checkboxes = driver.find_elements(By.CSS_SELECTOR, 'input[type="checkbox"]')
            self.logger.info(f"Tìm thấy {len(checkboxes)} checkbox(es)")

            for i, checkbox in enumerate(checkboxes):
                try:
                    if not checkbox.is_selected():
                        # Scroll đến checkbox
                        driver.execute_script("arguments[0].scrollIntoView(true);", checkbox)
                        time.sleep(0.5)

                        # Highlight để debug
                        driver.execute_script("arguments[0].style.border='3px solid purple'", checkbox)

                        # Click checkbox
                        driver.execute_script("arguments[0].click();", checkbox)
                        self.logger.info(f"✓ Đã tick checkbox {i+1}")
                        time.sleep(0.5)
                    else:
                        self.logger.info(f"✓ Checkbox {i+1} đã được tick")
                except Exception as cb_error:
                    self.logger.warning(f"Lỗi xử lý checkbox {i+1}: {cb_error}")

        except Exception as e:
            self.logger.warning(f"Không thể xử lý checkbox: {str(e)}")

    def _handle_captcha(self, driver):
        """Xử lý captcha (cần cập nhật theo captcha thực tế)"""
        try:
            # Tìm captcha image hoặc reCAPTCHA
            captcha_elements = driver.find_elements(By.CSS_SELECTOR,
                'img[src*="captcha"], .g-recaptcha, .h-captcha')

            if captcha_elements:
                self.logger.warning("Phát hiện captcha - cần xử lý thủ công")
                # Có thể tích hợp service giải captcha ở đây
                time.sleep(10)  # Chờ user giải captcha thủ công

        except Exception as e:
            self.logger.warning(f"Lỗi xử lý captcha: {str(e)}")

    def submit_registration(self, driver):
        """Submit form đăng ký với improved detection"""
        try:
            self.logger.info("🔍 Tìm nút submit...")

            # Chờ form load hoàn toàn
            time.sleep(2)

            # Tìm tất cả buttons trước
            all_buttons = driver.find_elements(By.TAG_NAME, "button")
            self.logger.info(f"📋 Tìm thấy {len(all_buttons)} buttons")

            # Log chi tiết các buttons
            for i, btn in enumerate(all_buttons):
                try:
                    text = btn.text.strip()
                    classes = btn.get_attribute("class") or ""
                    btn_type = btn.get_attribute("type") or ""
                    self.logger.info(f"  Button {i+1}: text='{text}', class='{classes}', type='{btn_type}'")
                except:
                    pass

            # Tìm nút submit cho 13win với nhiều cách
            submit_button = None

            # Cách 1: Tìm theo text chính xác
            submit_texts = ["ĐĂNG KÝ", "Đăng ký", "Register", "REGISTER", "Submit", "SUBMIT"]
            for text in submit_texts:
                try:
                    xpath = f"//button[contains(text(), '{text}')]"
                    buttons = driver.find_elements(By.XPATH, xpath)
                    if buttons:
                        submit_button = buttons[0]
                        self.logger.info(f"✓ Tìm thấy submit button bằng text: '{text}'")
                        break
                except:
                    continue

            # Cách 2: Tìm theo class nếu chưa tìm thấy
            if not submit_button:
                class_selectors = [
                    '.ui-button--primary',
                    'button.ui-button--primary',
                    '.btn-primary',
                    '.btn-register',
                    '.register-btn'
                ]

                for selector in class_selectors:
                    try:
                        elements = driver.find_elements(By.CSS_SELECTOR, selector)
                        if elements:
                            submit_button = elements[0]
                            self.logger.info(f"✓ Tìm thấy submit button bằng class: '{selector}'")
                            break
                    except:
                        continue

            # Cách 3: Tìm theo type nếu vẫn chưa có
            if not submit_button:
                try:
                    submit_inputs = driver.find_elements(By.CSS_SELECTOR, 'input[type="submit"], button[type="submit"]')
                    if submit_inputs:
                        submit_button = submit_inputs[0]
                        self.logger.info("✓ Tìm thấy submit button bằng type='submit'")
                except:
                    pass

            # Cách 4: Fallback - button cuối cùng (thường là submit)
            if not submit_button and all_buttons:
                submit_button = all_buttons[-1]
                self.logger.info("⚠️ Fallback: Sử dụng button cuối cùng làm submit")

            if submit_button:
                try:
                    # Highlight submit button
                    driver.execute_script("arguments[0].style.border='5px solid red'", submit_button)
                    driver.execute_script("arguments[0].style.backgroundColor='yellow'", submit_button)

                    # Scroll đến button
                    driver.execute_script("arguments[0].scrollIntoView(true);", submit_button)
                    time.sleep(1)

                    # Kiểm tra button có clickable không
                    if submit_button.is_enabled() and submit_button.is_displayed():
                        self.logger.info("✓ Submit button sẵn sàng click")

                        # Thử click bằng nhiều cách với verification
                        click_success = False

                        # Lưu URL trước khi click để so sánh
                        url_before = driver.current_url
                        self.logger.info(f"📍 URL trước submit: {url_before}")

                        # Cách 1: JavaScript click với force
                        try:
                            # Remove any overlays that might block click
                            driver.execute_script("""
                                arguments[0].style.zIndex = '9999';
                                arguments[0].style.position = 'relative';
                            """, submit_button)

                            # Force click
                            driver.execute_script("arguments[0].click();", submit_button)
                            self.logger.info("🖱️ Đã thực hiện JavaScript click")

                            # Chờ một chút để xem có phản ứng không
                            time.sleep(2)

                            # Kiểm tra URL có thay đổi không
                            url_after = driver.current_url
                            if url_after != url_before:
                                self.logger.info("✅ URL đã thay đổi - Submit thành công!")
                                click_success = True
                            else:
                                self.logger.warning("⚠️ URL chưa thay đổi sau JavaScript click")

                        except Exception as js_error:
                            self.logger.warning(f"JavaScript click failed: {js_error}")

                        # Cách 2: Selenium click nếu JS failed
                        if not click_success:
                            try:
                                # Scroll to center of button
                                driver.execute_script("""
                                    arguments[0].scrollIntoView({
                                        behavior: 'smooth',
                                        block: 'center',
                                        inline: 'center'
                                    });
                                """, submit_button)
                                time.sleep(1)

                                submit_button.click()
                                self.logger.info("🖱️ Đã thực hiện Selenium click")

                                # Chờ và kiểm tra
                                time.sleep(2)
                                url_after = driver.current_url
                                if url_after != url_before:
                                    self.logger.info("✅ URL đã thay đổi - Submit thành công!")
                                    click_success = True
                                else:
                                    self.logger.warning("⚠️ URL chưa thay đổi sau Selenium click")

                            except Exception as sel_error:
                                self.logger.warning(f"Selenium click failed: {sel_error}")

                        # Cách 3: Submit form trực tiếp nếu click button failed
                        if not click_success:
                            try:
                                self.logger.info("🔄 Thử submit form trực tiếp...")
                                # Tìm form chứa button
                                form = submit_button.find_element(By.XPATH, "./ancestor::form[1]")
                                driver.execute_script("arguments[0].submit();", form)
                                self.logger.info("🖱️ Đã submit form trực tiếp")

                                time.sleep(2)
                                url_after = driver.current_url
                                if url_after != url_before:
                                    self.logger.info("✅ URL đã thay đổi - Form submit thành công!")
                                    click_success = True

                            except Exception as form_error:
                                self.logger.warning(f"Form submit failed: {form_error}")

                        if click_success:
                            # Chờ thêm để xử lý hoàn tất
                            self.logger.info("⏳ Chờ xử lý hoàn tất...")
                            time.sleep(5)

                            # Kiểm tra kết quả cuối cùng
                            final_url = driver.current_url
                            self.logger.info(f"📍 URL cuối cùng: {final_url}")

                            return True
                        else:
                            self.logger.error("❌ Tất cả phương thức click đều thất bại")
                            return False
                    else:
                        self.logger.error("❌ Submit button không clickable")
                        return False

                except Exception as click_error:
                    self.logger.error(f"❌ Lỗi click submit button: {click_error}")
                    return False
            else:
                self.logger.error("❌ Không tìm thấy submit button")

                # Debug: Log page source để tìm submit button
                try:
                    page_source = driver.page_source
                    if "submit" in page_source.lower() or "đăng ký" in page_source.lower():
                        self.logger.debug("🔍 Page có chứa submit/đăng ký text")
                except:
                    pass

                return False

        except Exception as e:
            self.logger.error(f"❌ Lỗi submit form: {str(e)}")
            return False

    def check_registration_result(self, driver):
        """Kiểm tra kết quả đăng ký"""
        try:
            # Kiểm tra URL hiện tại
            current_url = driver.current_url

            # Kiểm tra thông báo thành công
            success_indicators = [
                "success", "thành công", "welcome", "dashboard", "profile"
            ]

            error_indicators = [
                "error", "lỗi", "failed", "exists", "đã tồn tại"
            ]

            page_source = driver.page_source.lower()

            for indicator in success_indicators:
                if indicator in current_url.lower() or indicator in page_source:
                    return "success"

            for indicator in error_indicators:
                if indicator in page_source:
                    return "failed"

            return "unknown"

        except Exception as e:
            self.logger.error(f"Lỗi kiểm tra kết quả: {str(e)}")
            return "error"

    def register_single_account(self, proxy=None, profile_path=None):
        """Đăng ký một tài khoản"""
        driver = None
        account_data = None

        try:
            # Tạo tài khoản ngẫu nhiên
            account_data = self.account_generator.generate_complete_account()

            # Tạo Chrome instance
            driver = self.create_chrome_instance(proxy, profile_path)
            if not driver:
                return {
                    'success': False,
                    'account': account_data,
                    'error': 'Không thể tạo Chrome instance'
                }

            # Điều hướng đến trang đăng ký với retry và proxy fallback
            self.logger.info(f"Điều hướng đến: {self.registration_url}")

            max_nav_retries = 2  # Giảm từ 3 xuống 2 để fallback nhanh hơn
            for nav_attempt in range(max_nav_retries):
                try:
                    # Fast mode - Giảm timeout để phát hiện proxy lỗi nhanh hơn
                    timeout = 5 if self.fast_mode else 10  # Giảm timeout để fallback nhanh hơn

                    driver.get(self.registration_url)

                    # Chờ trang load với timeout ngắn hơn để phát hiện proxy lỗi nhanh
                    try:
                        WebDriverWait(driver, timeout).until(
                            lambda d: d.execute_script("return document.readyState") == "complete"
                        )
                    except Exception as timeout_error:
                        self.logger.warning(f"⚠️ Timeout loading page (proxy issue?): {timeout_error}")
                        raise timeout_error

                    # Kiểm tra URL có đúng không
                    current_url = driver.current_url
                    self.logger.info(f"Current URL: {current_url}")

                    # Kiểm tra proxy có hoạt động không (nếu URL không load được)
                    if "about:blank" in current_url or "chrome-error" in current_url or "data:" in current_url:
                        self.logger.warning(f"⚠️ Proxy lỗi - URL: {current_url}")
                        raise Exception(f"Proxy connection failed - URL: {current_url}")

                    # Kiểm tra có phải trang đăng ký không
                    try:
                        page_source = driver.page_source.lower()
                        if any(keyword in page_source for keyword in ['register', 'signup', 'đăng ký']):
                            self.logger.info("✓ Đã vào trang đăng ký thành công")
                            break
                        else:
                            self.logger.warning(f"⚠️ Không phải trang đăng ký (attempt {nav_attempt + 1})")
                            if nav_attempt < max_nav_retries - 1:
                                time.sleep(1 if self.fast_mode else 2)
                                continue
                    except Exception as page_check_error:
                        self.logger.warning(f"⚠️ Không thể kiểm tra page source: {page_check_error}")
                        raise page_check_error

                except Exception as nav_error:
                    self.logger.warning(f"❌ Navigation attempt {nav_attempt + 1} failed: {nav_error}")

                    # Immediate fallback to no-proxy if proxy fails
                    if nav_attempt == max_nav_retries - 1:
                        self.logger.info("🔄 IMMEDIATE FALLBACK: Tạo Chrome không proxy...")
                        try:
                            if driver:
                                driver.quit()
                        except:
                            pass

                        # Create new Chrome without proxy
                        driver = self.chrome_manager.create_chrome_instance(proxy=None)
                        if driver:
                            try:
                                self.logger.info("🌐 Thử vào trang đăng ký không proxy...")
                                driver.get(self.registration_url)
                                WebDriverWait(driver, 10).until(
                                    lambda d: d.execute_script("return document.readyState") == "complete"
                                )

                                # Check if registration page
                                current_url = driver.current_url
                                page_source = driver.page_source.lower()
                                if any(keyword in page_source for keyword in ['register', 'signup', 'đăng ký']):
                                    self.logger.info("✅ Fallback không proxy THÀNH CÔNG!")
                                    break
                                else:
                                    self.logger.error(f"❌ Fallback failed - not registration page: {current_url}")
                                    raise nav_error
                            except Exception as fallback_error:
                                self.logger.error(f"❌ Fallback không proxy failed: {fallback_error}")
                                raise nav_error
                        else:
                            self.logger.error("❌ Không thể tạo Chrome không proxy")
                            raise nav_error
                    else:
                        time.sleep(1 if self.fast_mode else 2)

            # Fast mode - Giảm delay page load
            if self.fast_mode:
                time.sleep(random.uniform(1, 2))  # Giảm từ 3-5s xuống 1-2s
            else:
                # Chờ thêm để trang load hoàn toàn
                time.sleep(random.uniform(3, 5))

            # Điền form
            if not self.fill_registration_form(driver, account_data):
                return {
                    'success': False,
                    'account': account_data,
                    'error': 'Không thể điền form'
                }

            # Submit form
            if not self.submit_registration(driver):
                return {
                    'success': False,
                    'account': account_data,
                    'error': 'Không thể submit form'
                }

            # Kiểm tra kết quả
            result = self.check_registration_result(driver)

            if result == "success":
                self.logger.info(f"🎉 Đăng ký thành công: {account_data['username']}")

                # Tự động claim bonus đăng ký (nếu enabled)
                bonus_claimed = False
                if self.auto_claim_bonus:
                    try:
                        self.logger.info("🎁 Bắt đầu claim bonus đăng ký 14K...")
                        bonus_claimed = self.bonus_claimer.claim_registration_bonus(driver)
                        if bonus_claimed:
                            self.logger.info("💰 Đã nhận thưởng đăng ký 14K thành công!")
                        else:
                            self.logger.warning("⚠️ Không thể nhận thưởng tự động, cần kiểm tra thủ công")
                    except Exception as bonus_error:
                        self.logger.error(f"❌ Lỗi claim bonus: {bonus_error}")
                else:
                    self.logger.info("ℹ️ Auto claim bonus disabled - bỏ qua nhận thưởng tự động")

                # Thông báo kết quả và giữ Chrome mở
                if bonus_claimed:
                    self.logger.info("🎉 HOÀN THÀNH: Đăng ký + Nhận thưởng 14K thành công!")
                    self.logger.info("💰 Chrome sẽ được giữ mở để bạn kiểm tra thưởng")
                else:
                    self.logger.info("🎉 HOÀN THÀNH: Đăng ký thành công!")
                    self.logger.info("🌐 Chrome sẽ được giữ mở để bạn kiểm tra kết quả")

                self.logger.info("⚠️ LƯU Ý: KHÔNG TẮT CHROME để kiểm tra kết quả!")

                # Không cleanup driver khi thành công - để user kiểm tra
                return {
                    'success': True,
                    'account': account_data,
                    'message': f'Đăng ký thành công{" + Nhận thưởng 14K" if bonus_claimed else ""}',
                    'bonus_claimed': bonus_claimed,
                    'driver': driver,  # Trả về driver để không bị cleanup
                    'keep_open': True
                }
            else:
                return {
                    'success': False,
                    'account': account_data,
                    'error': f'Đăng ký thất bại: {result}'
                }

        except Exception as e:
            error_msg = str(e)
            self.logger.error(f"Lỗi đăng ký tài khoản: {error_msg}")

            # Nếu lỗi proxy, cảnh báo nhưng không fallback ngay
            if "ERR_TUNNEL_CONNECTION_FAILED" in error_msg or "ERR_PROXY" in error_msg:
                self.logger.error("❌ Proxy connection failed!")
                self.logger.warning("⚠️ LƯU Ý: Mỗi browser chỉ đăng ký được 1 account trên 13win")
                self.logger.warning("⚠️ Không có proxy = không thể đăng ký nhiều account để nhận thưởng")
                self.logger.info("💡 Khuyến nghị: Sử dụng proxy khác hoặc kiểm tra proxy settings")

                # Chỉ fallback nếu không có proxy nào khả dụng
                if not self.proxy_manager or self.proxy_manager.get_working_proxy_count() == 0:
                    self.logger.warning("🔄 Không có proxy khả dụng, thử direct connection...")
                    try:
                        # Release proxy hiện tại
                        if proxy:
                            self.proxy_manager.release_proxy(proxy)

                        # Thử lại không proxy
                        driver = self.create_chrome_instance(proxy=None)
                        if driver:
                            self.logger.info("🌐 Điều hướng đến trang đăng ký (không proxy)...")
                            driver.get(self.registration_url)

                            # Tiếp tục đăng ký
                            if self.fill_registration_form(driver, account_data):
                                result = self.check_registration_result(driver)
                                if result == "success":
                                    self.logger.info(f"🎉 Đăng ký thành công (không proxy): {account_data['username']}")
                                    self.logger.warning("⚠️ Chỉ có thể đăng ký 1 account với browser này!")

                                    # Tự động claim bonus đăng ký (nếu enabled)
                                    bonus_claimed = False
                                    if self.auto_claim_bonus:
                                        try:
                                            self.logger.info("🎁 Bắt đầu claim bonus đăng ký 14K...")
                                            bonus_claimed = self.bonus_claimer.claim_registration_bonus(driver)
                                            if bonus_claimed:
                                                self.logger.info("💰 Đã nhận thưởng đăng ký 14K thành công!")
                                        except Exception as bonus_error:
                                            self.logger.error(f"❌ Lỗi claim bonus: {bonus_error}")
                                    else:
                                        self.logger.info("ℹ️ Auto claim bonus disabled - bỏ qua nhận thưởng tự động")

                                    return {
                                        'success': True,
                                        'account': account_data,
                                        'message': f'Đăng ký thành công (không proxy - chỉ 1 account){" + Nhận thưởng 14K" if bonus_claimed else ""}',
                                        'bonus_claimed': bonus_claimed,
                                        'driver': driver,
                                        'keep_open': True
                                    }

                            # Cleanup nếu thất bại
                            try:
                                self.chrome_manager.cleanup_driver(driver)
                            except:
                                pass

                    except Exception as retry_error:
                        self.logger.error(f"Lỗi retry không proxy: {retry_error}")
                else:
                    self.logger.info("💡 Vẫn còn proxy khác, sẽ thử proxy khác ở lần đăng ký tiếp theo")

            return {
                'success': False,
                'account': account_data if account_data else {},
                'error': error_msg
            }
        finally:
            # Chỉ cleanup driver nếu không phải success với keep_open
            if driver and not ('result' in locals() and isinstance(result, dict) and result.get('keep_open')):
                try:
                    self.chrome_manager.cleanup_driver(driver)
                except:
                    pass

    def register_multiple_accounts(self, num_accounts, max_concurrent=3, callback=None):
        """Đăng ký nhiều tài khoản đồng thời"""
        self.registration_results = []
        threads = []

        def worker(account_index):
            try:
                # Lấy proxy nếu có (với validation)
                proxy = None
                proxy_info = None
                if self.proxy_manager:
                    proxy_info = self.proxy_manager.get_proxy()
                    if proxy_info:
                        # Quick validation trước khi sử dụng (nếu enabled)
                        if not self.validate_proxy or self.quick_validate_proxy(proxy_info):
                            proxy = {
                                'type': 'http',
                                'host': proxy_info.host,
                                'port': proxy_info.port,
                                'username': proxy_info.username,
                                'password': proxy_info.password
                            }
                            self.logger.info(f"✅ Proxy validated: {proxy_info.host}:{proxy_info.port}")
                        else:
                            self.logger.warning(f"⚠️ Proxy validation failed: {proxy_info.host}:{proxy_info.port}")
                            self.logger.info("🔄 Thử sử dụng proxy này anyway vì cần bypass giới hạn 1 account/browser")
                            # Vẫn sử dụng proxy dù validation fail vì cần bypass giới hạn
                            proxy = {
                                'type': 'http',
                                'host': proxy_info.host,
                                'port': proxy_info.port,
                                'username': proxy_info.username,
                                'password': proxy_info.password
                            }

                # Tạo profile path riêng an toàn
                profile_name = f"profile_{account_index}_{random.randint(1000, 9999)}"
                profile_path = self._create_safe_profile_path(profile_name)

                # Thêm delay giữa các Chrome instances để tránh crash
                if account_index > 0:  # Không delay cho instance đầu tiên
                    delay_time = random.uniform(3, 6)  # 3-6 giây delay
                    self.logger.info(f"⏳ Delay {delay_time:.1f}s trước khi tạo Chrome instance {account_index + 1}")
                    time.sleep(delay_time)

                # Đăng ký tài khoản
                result = self.register_single_account(proxy, profile_path)
                result['account_index'] = account_index

                # Trả lại proxy
                if self.proxy_manager and proxy:
                    self.proxy_manager.release_proxy(proxy_info)

                # Callback để cập nhật UI
                if callback:
                    callback(result)

                self.registration_results.append(result)

            except Exception as e:
                error_result = {
                    'success': False,
                    'account_index': account_index,
                    'error': str(e)
                }
                if callback:
                    callback(error_result)
                self.registration_results.append(error_result)

        # Tạo và chạy threads
        for i in range(num_accounts):
            if len(threads) >= max_concurrent:
                # Chờ thread hoàn thành
                for thread in threads:
                    thread.join()
                threads = []

            thread = threading.Thread(target=worker, args=(i,))
            thread.start()
            threads.append(thread)

            # Delay giữa các lần tạo thread
            time.sleep(random.uniform(1, 3))

        # Chờ tất cả threads hoàn thành
        for thread in threads:
            thread.join()

        return self.registration_results

    def get_registration_stats(self):
        """Lấy thống kê đăng ký"""
        if not self.registration_results:
            return {
                'total': 0,
                'success': 0,
                'failed': 0,
                'success_rate': 0
            }

        total = len(self.registration_results)
        success = len([r for r in self.registration_results if r.get('success', False)])
        failed = total - success
        success_rate = (success / total * 100) if total > 0 else 0

        return {
            'total': total,
            'success': success,
            'failed': failed,
            'success_rate': round(success_rate, 2)
        }

    def save_results_to_file(self, filename="registration_results.txt"):
        """Lưu kết quả vào file"""
        try:
            with open(filename, 'w', encoding='utf-8') as f:
                f.write("=== KẾT QUẢ ĐĂNG KÝ TÀI KHOẢN 13WIN16 ===\n\n")

                stats = self.get_registration_stats()
                f.write(f"Tổng số tài khoản: {stats['total']}\n")
                f.write(f"Thành công: {stats['success']}\n")
                f.write(f"Thất bại: {stats['failed']}\n")
                f.write(f"Tỷ lệ thành công: {stats['success_rate']}%\n\n")

                f.write("=== CHI TIẾT TÀI KHOẢN THÀNH CÔNG ===\n")
                for result in self.registration_results:
                    if result.get('success', False):
                        account = result.get('account', {})
                        f.write(f"Username: {account.get('username', 'N/A')}\n")
                        f.write(f"Password: {account.get('password', 'N/A')}\n")
                        f.write(f"Email: {account.get('email', 'N/A')}\n")
                        f.write(f"Họ tên: {account.get('full_name', 'N/A')}\n")
                        f.write(f"Điện thoại: {account.get('phone', 'N/A')}\n")
                        f.write("-" * 50 + "\n")

                f.write("\n=== CHI TIẾT TÀI KHOẢN THẤT BẠI ===\n")
                for result in self.registration_results:
                    if not result.get('success', False):
                        account = result.get('account', {})
                        f.write(f"Username: {account.get('username', 'N/A')}\n")
                        f.write(f"Lỗi: {result.get('error', 'N/A')}\n")
                        f.write("-" * 50 + "\n")

            self.logger.info(f"Đã lưu kết quả vào file: {filename}")
            return True

        except Exception as e:
            self.logger.error(f"Lỗi lưu file: {str(e)}")
            return False
