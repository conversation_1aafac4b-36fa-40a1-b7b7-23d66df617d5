"""
Service đăng ký tự động tài khoản 13win16
"""

import time
import random
import logging
import os
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service
from selenium.common.exceptions import TimeoutException, NoSuchElementException
from webdriver_manager.chrome import ChromeDriverManager
from account_generator import AccountGenerator
import threading

class Win13RegistrationService:
    def __init__(self, proxy_manager=None):
        self.proxy_manager = proxy_manager
        self.account_generator = AccountGenerator()
        self.logger = logging.getLogger(__name__)
        self.registration_url = "https://13win16.com/register"  # URL đăng ký 13win16
        self.active_browsers = []
        self.registration_results = []

    def create_chrome_instance(self, proxy=None, profile_path=None):
        """Tạo Chrome instance với proxy và profile riêng"""
        driver = None
        temp_extension_dir = None

        try:
            self.logger.info("Bắt đầu tạo Chrome instance...")

            options = Options()

            # Cấu hình cơ bản để tránh lỗi
            options.add_argument("--no-sandbox")
            options.add_argument("--disable-dev-shm-usage")
            options.add_argument("--disable-gpu")
            options.add_argument("--disable-web-security")
            options.add_argument("--allow-running-insecure-content")
            options.add_argument("--disable-blink-features=AutomationControlled")
            options.add_experimental_option("excludeSwitches", ["enable-automation"])
            options.add_experimental_option('useAutomationExtension', False)

            # Tắt thông báo và popup
            options.add_argument("--disable-notifications")
            options.add_argument("--disable-popup-blocking")
            options.add_argument("--disable-infobars")

            # Tắt logging để giảm noise
            options.add_argument("--log-level=3")
            options.add_argument("--silent")
            options.add_experimental_option('excludeSwitches', ['enable-logging'])
            options.add_experimental_option('useAutomationExtension', False)

            # User agent ngẫu nhiên
            user_agents = [
                "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
                "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
                "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
            ]
            options.add_argument(f"--user-agent={random.choice(user_agents)}")

            # Cấu hình proxy nếu có
            if proxy:
                try:
                    proxy_url = f"{proxy['type']}://{proxy['host']}:{proxy['port']}"
                    options.add_argument(f"--proxy-server={proxy_url}")
                    self.logger.info(f"Đã cấu hình proxy: {proxy_url}")

                    # Chỉ tạo extension nếu có auth và không có lỗi
                    if proxy.get('username') and proxy.get('password'):
                        try:
                            temp_extension_dir = self._create_proxy_auth_extension(proxy, options)
                            self.logger.info("Đã tạo proxy auth extension")
                        except Exception as ext_error:
                            self.logger.warning(f"Không thể tạo proxy auth extension: {ext_error}")
                            # Tiếp tục mà không có auth extension

                except Exception as proxy_error:
                    self.logger.warning(f"Lỗi cấu hình proxy: {proxy_error}")

            # Profile riêng cho mỗi instance
            if profile_path:
                import os
                # Tạo thư mục profile nếu chưa có
                os.makedirs(profile_path, exist_ok=True)
                options.add_argument(f"--user-data-dir={profile_path}")
                self.logger.info(f"Đã cấu hình profile: {profile_path}")

            # Tạo service với error handling và fix ChromeDriver
            try:
                self.logger.info("Đang tải ChromeDriver...")

                # Fix lỗi ChromeDriver không tương thích
                import platform
                import shutil

                # Xóa cache ChromeDriver cũ nếu có lỗi
                try:
                    cache_dir = os.path.expanduser("~/.wdm")
                    if os.path.exists(cache_dir):
                        self.logger.info("Xóa cache ChromeDriver cũ...")
                        shutil.rmtree(cache_dir, ignore_errors=True)
                except:
                    pass

                # Tải ChromeDriver với ưu tiên local file
                driver_path = None

                # Bước 1: Thử ChromeDriver local trong thư mục tool
                local_chromedriver = os.path.join(os.getcwd(), "chromedriver.exe")
                if os.path.exists(local_chromedriver):
                    try:
                        # Test ChromeDriver local
                        import subprocess
                        result = subprocess.run([local_chromedriver, "--version"],
                                              capture_output=True, text=True, timeout=5)
                        if result.returncode == 0:
                            driver_path = local_chromedriver
                            self.logger.info(f"✓ Sử dụng ChromeDriver local: {driver_path}")
                            self.logger.info(f"Version: {result.stdout.strip()}")
                        else:
                            self.logger.warning("ChromeDriver local không hoạt động")
                    except Exception as e:
                        self.logger.warning(f"Lỗi test ChromeDriver local: {e}")

                # Bước 2: Nếu không có local, thử WebDriverManager
                if not driver_path:
                    try:
                        self.logger.info("Tải ChromeDriver từ WebDriverManager...")
                        driver_path = ChromeDriverManager().install()
                        self.logger.info(f"ChromeDriver path: {driver_path}")

                        # Kiểm tra file có hợp lệ không
                        if not os.path.exists(driver_path):
                            raise Exception("ChromeDriver file không tồn tại")

                        # Kiểm tra file có thể thực thi không
                        if not os.access(driver_path, os.X_OK):
                            self.logger.warning("ChromeDriver không có quyền thực thi, đang cấp quyền...")
                            os.chmod(driver_path, 0o755)

                    except Exception as download_error:
                        self.logger.error(f"Lỗi tải ChromeDriver: {download_error}")

                        # Bước 3: Thử tìm ChromeDriver trong system PATH
                        self.logger.info("Tìm ChromeDriver trong system PATH...")
                        driver_path = shutil.which("chromedriver")
                        if driver_path:
                            self.logger.info(f"Sử dụng ChromeDriver từ system: {driver_path}")
                        else:
                            self.logger.error("Không thể tìm thấy ChromeDriver hợp lệ")
                            self.logger.error("Gợi ý: Chạy 'python quick_fix_chrome.py' để fix")
                            raise Exception("Không thể tìm thấy ChromeDriver hợp lệ")

                # Tạo service
                service = Service(driver_path)

                # Tắt log để giảm noise
                try:
                    if os.name == 'nt':  # Windows
                        service.log_path = "NUL"
                    else:  # Linux/Mac
                        service.log_path = "/dev/null"
                except:
                    pass  # Ignore nếu không set được log path

                self.logger.info("ChromeDriver service đã sẵn sàng")

            except Exception as service_error:
                self.logger.error(f"Lỗi tạo ChromeDriver service: {service_error}")
                self.logger.error("Gợi ý khắc phục:")
                self.logger.error("1. Cài đặt Chrome browser mới nhất")
                self.logger.error("2. Xóa thư mục ~/.wdm và thử lại")
                self.logger.error("3. Tải ChromeDriver thủ công từ https://chromedriver.chromium.org/")
                self.logger.error("4. Kiểm tra antivirus có block không")
                raise service_error

            # Tạo driver với timeout và retry
            self.logger.info("Đang khởi tạo Chrome browser...")
            max_retries = 3
            for attempt in range(max_retries):
                try:
                    driver = webdriver.Chrome(service=service, options=options)
                    break
                except Exception as driver_error:
                    self.logger.warning(f"Lần thử {attempt + 1}/{max_retries} thất bại: {driver_error}")
                    if attempt == max_retries - 1:
                        raise driver_error
                    time.sleep(2)  # Chờ 2 giây trước khi thử lại

            # Cấu hình timeout
            driver.set_page_load_timeout(30)
            driver.implicitly_wait(10)

            # Ẩn automation flags
            try:
                driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
                driver.execute_cdp_cmd('Network.setUserAgentOverride', {
                    "userAgent": driver.execute_script("return navigator.userAgent").replace("HeadlessChrome", "Chrome")
                })
            except Exception as script_error:
                self.logger.warning(f"Không thể ẩn automation flags: {script_error}")

            proxy_info = f"proxy: {proxy['host']}:{proxy['port']}" if proxy else "no proxy"
            self.logger.info(f"✓ Đã tạo Chrome instance thành công với {proxy_info}")
            return driver

        except Exception as e:
            self.logger.error(f"✗ Lỗi tạo Chrome instance: {str(e)}")

            # Cleanup nếu có lỗi
            if driver:
                try:
                    driver.quit()
                except:
                    pass

            # Cleanup temp extension directory
            if temp_extension_dir:
                try:
                    import shutil
                    shutil.rmtree(temp_extension_dir, ignore_errors=True)
                except:
                    pass

            return None

    def _create_proxy_auth_extension(self, proxy, options):
        """Tạo extension cho proxy authentication"""
        import os
        import tempfile

        try:
            # Tạo thư mục temp cho extension
            extension_dir = os.path.join(tempfile.gettempdir(), f"proxy_ext_{random.randint(1000, 9999)}")
            os.makedirs(extension_dir, exist_ok=True)

            manifest_json = {
                "version": "1.0.0",
                "manifest_version": 2,
                "name": "Chrome Proxy Auth",
                "permissions": [
                    "proxy",
                    "tabs",
                    "unlimitedStorage",
                    "storage",
                    "<all_urls>",
                    "webRequest",
                    "webRequestBlocking"
                ],
                "background": {
                    "scripts": ["background.js"]
                },
                "minimum_chrome_version": "22.0.0"
            }

            background_js = f"""
var config = {{
    mode: "fixed_servers",
    rules: {{
        singleProxy: {{
            scheme: "{proxy.get('type', 'http')}",
            host: "{proxy['host']}",
            port: parseInt({proxy['port']})
        }},
        bypassList: ["localhost"]
    }}
}};

chrome.proxy.settings.set({{value: config, scope: "regular"}}, function() {{}});

function callbackFn(details) {{
    return {{
        authCredentials: {{
            username: "{proxy['username']}",
            password: "{proxy['password']}"
        }}
    }};
}}

chrome.webRequest.onAuthRequired.addListener(
    callbackFn,
    {{urls: ["<all_urls>"]}},
    ['blocking']
);
"""

            # Tạo files
            import json
            with open(os.path.join(extension_dir, "manifest.json"), "w", encoding='utf-8') as f:
                json.dump(manifest_json, f, indent=2)

            with open(os.path.join(extension_dir, "background.js"), "w", encoding='utf-8') as f:
                f.write(background_js)

            # Thêm extension vào Chrome
            options.add_argument(f"--load-extension={extension_dir}")

            return extension_dir

        except Exception as e:
            self.logger.error(f"Lỗi tạo proxy auth extension: {str(e)}")
            raise e

    def fill_registration_form(self, driver, account_data):
        """Điền form đăng ký tự động"""
        try:
            # Chờ trang load
            WebDriverWait(driver, 10).until(
                EC.presence_of_element_located((By.TAG_NAME, "form"))
            )

            # Mapping các field (cần cập nhật theo form thực tế của 13win16)
            field_mappings = {
                'username': ['input[name="username"]', 'input[id="username"]', '#username'],
                'email': ['input[name="email"]', 'input[id="email"]', '#email'],
                'password': ['input[name="password"]', 'input[id="password"]', '#password'],
                'confirm_password': ['input[name="confirm_password"]', 'input[id="confirm_password"]', '#confirm_password'],
                'full_name': ['input[name="full_name"]', 'input[id="full_name"]', '#full_name'],
                'phone': ['input[name="phone"]', 'input[id="phone"]', '#phone'],
                'birth_day': ['select[name="birth_day"]', 'select[id="birth_day"]', '#birth_day'],
                'birth_month': ['select[name="birth_month"]', 'select[id="birth_month"]', '#birth_month'],
                'birth_year': ['select[name="birth_year"]', 'select[id="birth_year"]', '#birth_year']
            }

            # Điền từng field
            for field_name, selectors in field_mappings.items():
                if field_name in account_data:
                    value = account_data[field_name]
                    if field_name == 'confirm_password':
                        value = account_data['password']

                    element = self._find_element_by_selectors(driver, selectors)
                    if element:
                        if element.tag_name == 'select':
                            self._select_option(element, value)
                        else:
                            self._type_slowly(element, str(value))

                        self.logger.info(f"Đã điền {field_name}: {value}")
                        time.sleep(random.uniform(0.5, 1.5))

            # Xử lý checkbox/terms nếu có
            self._handle_terms_checkbox(driver)

            # Xử lý captcha nếu có
            self._handle_captcha(driver)

            return True

        except Exception as e:
            self.logger.error(f"Lỗi điền form: {str(e)}")
            return False

    def _find_element_by_selectors(self, driver, selectors):
        """Tìm element bằng nhiều selector"""
        for selector in selectors:
            try:
                if selector.startswith('#') or selector.startswith('.'):
                    element = driver.find_element(By.CSS_SELECTOR, selector)
                elif '[' in selector:
                    element = driver.find_element(By.CSS_SELECTOR, selector)
                else:
                    element = driver.find_element(By.NAME, selector)
                return element
            except NoSuchElementException:
                continue
        return None

    def _type_slowly(self, element, text):
        """Gõ chậm để giống người thật"""
        element.clear()
        for char in text:
            element.send_keys(char)
            time.sleep(random.uniform(0.05, 0.15))

    def _select_option(self, select_element, value):
        """Chọn option trong select"""
        from selenium.webdriver.support.ui import Select
        select = Select(select_element)
        try:
            select.select_by_value(str(value))
        except:
            try:
                select.select_by_visible_text(str(value))
            except:
                # Chọn option đầu tiên nếu không tìm thấy
                if len(select.options) > 1:
                    select.select_by_index(1)

    def _handle_terms_checkbox(self, driver):
        """Xử lý checkbox điều khoản"""
        try:
            checkboxes = driver.find_elements(By.CSS_SELECTOR, 'input[type="checkbox"]')
            for checkbox in checkboxes:
                if not checkbox.is_selected():
                    driver.execute_script("arguments[0].click();", checkbox)
                    time.sleep(0.5)
        except Exception as e:
            self.logger.warning(f"Không thể xử lý checkbox: {str(e)}")

    def _handle_captcha(self, driver):
        """Xử lý captcha (cần cập nhật theo captcha thực tế)"""
        try:
            # Tìm captcha image hoặc reCAPTCHA
            captcha_elements = driver.find_elements(By.CSS_SELECTOR,
                'img[src*="captcha"], .g-recaptcha, .h-captcha')

            if captcha_elements:
                self.logger.warning("Phát hiện captcha - cần xử lý thủ công")
                # Có thể tích hợp service giải captcha ở đây
                time.sleep(10)  # Chờ user giải captcha thủ công

        except Exception as e:
            self.logger.warning(f"Lỗi xử lý captcha: {str(e)}")

    def submit_registration(self, driver):
        """Submit form đăng ký"""
        try:
            # Tìm nút submit
            submit_selectors = [
                'input[type="submit"]',
                'button[type="submit"]',
                'button:contains("Đăng ký")',
                'button:contains("Register")',
                '.btn-register',
                '#register-btn'
            ]

            submit_button = self._find_element_by_selectors(driver, submit_selectors)
            if submit_button:
                # Scroll đến button
                driver.execute_script("arguments[0].scrollIntoView();", submit_button)
                time.sleep(1)

                # Click submit
                driver.execute_script("arguments[0].click();", submit_button)
                self.logger.info("Đã click nút đăng ký")

                # Chờ kết quả
                time.sleep(5)
                return True
            else:
                self.logger.error("Không tìm thấy nút đăng ký")
                return False

        except Exception as e:
            self.logger.error(f"Lỗi submit form: {str(e)}")
            return False

    def check_registration_result(self, driver):
        """Kiểm tra kết quả đăng ký"""
        try:
            # Kiểm tra URL hiện tại
            current_url = driver.current_url

            # Kiểm tra thông báo thành công
            success_indicators = [
                "success", "thành công", "welcome", "dashboard", "profile"
            ]

            error_indicators = [
                "error", "lỗi", "failed", "exists", "đã tồn tại"
            ]

            page_source = driver.page_source.lower()

            for indicator in success_indicators:
                if indicator in current_url.lower() or indicator in page_source:
                    return "success"

            for indicator in error_indicators:
                if indicator in page_source:
                    return "failed"

            return "unknown"

        except Exception as e:
            self.logger.error(f"Lỗi kiểm tra kết quả: {str(e)}")
            return "error"

    def register_single_account(self, proxy=None, profile_path=None):
        """Đăng ký một tài khoản"""
        driver = None
        try:
            # Tạo tài khoản ngẫu nhiên
            account_data = self.account_generator.generate_complete_account()

            # Tạo Chrome instance
            driver = self.create_chrome_instance(proxy, profile_path)
            if not driver:
                return {
                    'success': False,
                    'account': account_data,
                    'error': 'Không thể tạo Chrome instance'
                }

            # Điều hướng đến trang đăng ký
            driver.get(self.registration_url)
            time.sleep(random.uniform(2, 4))

            # Điền form
            if not self.fill_registration_form(driver, account_data):
                return {
                    'success': False,
                    'account': account_data,
                    'error': 'Không thể điền form'
                }

            # Submit form
            if not self.submit_registration(driver):
                return {
                    'success': False,
                    'account': account_data,
                    'error': 'Không thể submit form'
                }

            # Kiểm tra kết quả
            result = self.check_registration_result(driver)

            if result == "success":
                self.logger.info(f"Đăng ký thành công: {account_data['username']}")
                return {
                    'success': True,
                    'account': account_data,
                    'message': 'Đăng ký thành công'
                }
            else:
                return {
                    'success': False,
                    'account': account_data,
                    'error': f'Đăng ký thất bại: {result}'
                }

        except Exception as e:
            self.logger.error(f"Lỗi đăng ký tài khoản: {str(e)}")
            return {
                'success': False,
                'account': account_data if 'account_data' in locals() else {},
                'error': str(e)
            }
        finally:
            if driver:
                try:
                    driver.quit()
                except:
                    pass

    def register_multiple_accounts(self, num_accounts, max_concurrent=3, callback=None):
        """Đăng ký nhiều tài khoản đồng thời"""
        self.registration_results = []
        threads = []

        def worker(account_index):
            try:
                # Lấy proxy nếu có
                proxy = None
                if self.proxy_manager:
                    proxy_info = self.proxy_manager.get_proxy()
                    if proxy_info:
                        proxy = {
                            'type': 'http',
                            'host': proxy_info.host,
                            'port': proxy_info.port,
                            'username': proxy_info.username,
                            'password': proxy_info.password
                        }

                # Tạo profile path riêng
                profile_path = f"temp_profile_{account_index}_{random.randint(1000, 9999)}"

                # Đăng ký tài khoản
                result = self.register_single_account(proxy, profile_path)
                result['account_index'] = account_index

                # Trả lại proxy
                if self.proxy_manager and proxy:
                    self.proxy_manager.release_proxy(proxy_info)

                # Callback để cập nhật UI
                if callback:
                    callback(result)

                self.registration_results.append(result)

            except Exception as e:
                error_result = {
                    'success': False,
                    'account_index': account_index,
                    'error': str(e)
                }
                if callback:
                    callback(error_result)
                self.registration_results.append(error_result)

        # Tạo và chạy threads
        for i in range(num_accounts):
            if len(threads) >= max_concurrent:
                # Chờ thread hoàn thành
                for thread in threads:
                    thread.join()
                threads = []

            thread = threading.Thread(target=worker, args=(i,))
            thread.start()
            threads.append(thread)

            # Delay giữa các lần tạo thread
            time.sleep(random.uniform(1, 3))

        # Chờ tất cả threads hoàn thành
        for thread in threads:
            thread.join()

        return self.registration_results

    def get_registration_stats(self):
        """Lấy thống kê đăng ký"""
        if not self.registration_results:
            return {
                'total': 0,
                'success': 0,
                'failed': 0,
                'success_rate': 0
            }

        total = len(self.registration_results)
        success = len([r for r in self.registration_results if r.get('success', False)])
        failed = total - success
        success_rate = (success / total * 100) if total > 0 else 0

        return {
            'total': total,
            'success': success,
            'failed': failed,
            'success_rate': round(success_rate, 2)
        }

    def save_results_to_file(self, filename="registration_results.txt"):
        """Lưu kết quả vào file"""
        try:
            with open(filename, 'w', encoding='utf-8') as f:
                f.write("=== KẾT QUẢ ĐĂNG KÝ TÀI KHOẢN 13WIN16 ===\n\n")

                stats = self.get_registration_stats()
                f.write(f"Tổng số tài khoản: {stats['total']}\n")
                f.write(f"Thành công: {stats['success']}\n")
                f.write(f"Thất bại: {stats['failed']}\n")
                f.write(f"Tỷ lệ thành công: {stats['success_rate']}%\n\n")

                f.write("=== CHI TIẾT TÀI KHOẢN THÀNH CÔNG ===\n")
                for result in self.registration_results:
                    if result.get('success', False):
                        account = result.get('account', {})
                        f.write(f"Username: {account.get('username', 'N/A')}\n")
                        f.write(f"Password: {account.get('password', 'N/A')}\n")
                        f.write(f"Email: {account.get('email', 'N/A')}\n")
                        f.write(f"Họ tên: {account.get('full_name', 'N/A')}\n")
                        f.write(f"Điện thoại: {account.get('phone', 'N/A')}\n")
                        f.write("-" * 50 + "\n")

                f.write("\n=== CHI TIẾT TÀI KHOẢN THẤT BẠI ===\n")
                for result in self.registration_results:
                    if not result.get('success', False):
                        account = result.get('account', {})
                        f.write(f"Username: {account.get('username', 'N/A')}\n")
                        f.write(f"Lỗi: {result.get('error', 'N/A')}\n")
                        f.write("-" * 50 + "\n")

            self.logger.info(f"Đã lưu kết quả vào file: {filename}")
            return True

        except Exception as e:
            self.logger.error(f"Lỗi lưu file: {str(e)}")
            return False
