#!/usr/bin/env python3
"""
Test script để kiểm tra việc lưu và load proxy
"""

import sys
import os

# Thêm src vào path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from src.proxy_manager import ProxyManager, ProxyInfo

def test_save_load_cycle():
    """Test chu trình lưu và load proxy"""
    print("=== TEST CHU TRÌNH LƯU VÀ LOAD PROXY ===")
    
    # Bước 1: Tạo proxy manager và thêm proxy
    print("1. Tạo proxy manager và thêm proxy...")
    manager = ProxyManager()
    
    # Thêm một số proxy test
    test_proxies = [
        ("127.0.0.1", 8080),
        ("***********", 3128),
        ("proxy.example.com", 8080, "user1", "pass1"),
        ("proxy2.example.com", 3128, "user2", "pass2")
    ]
    
    for proxy_data in test_proxies:
        if len(proxy_data) == 2:
            host, port = proxy_data
            manager.add_proxy(host, port)
        else:
            host, port, username, password = proxy_data
            manager.add_proxy(host, port, username, password)
    
    print(f"✓ Đã thêm {len(test_proxies)} proxy")
    
    # Bước 2: Giả lập test một số proxy
    print("2. Giả lập test proxy...")
    for i, proxy in enumerate(manager.proxies):
        if i % 2 == 0:  # Giả lập 50% proxy hoạt động
            proxy.is_working = True
            proxy.response_time = 200 + i * 50
            if proxy not in manager.working_proxies:
                manager.working_proxies.append(proxy)
        else:
            proxy.is_working = False
    
    working_count = len([p for p in manager.proxies if p.is_working])
    print(f"✓ Giả lập {working_count} proxy hoạt động")
    
    # Bước 3: Lưu tất cả proxy
    print("3. Lưu tất cả proxy...")
    all_file = "test_all_proxies.txt"
    if manager.save_all_proxies(all_file):
        print(f"✓ Đã lưu tất cả proxy vào {all_file}")
    else:
        print("✗ Lỗi lưu tất cả proxy")
        return False
    
    # Bước 4: Lưu proxy hoạt động
    print("4. Lưu proxy hoạt động...")
    working_file = "test_working_proxies.txt"
    if manager.save_working_proxies(working_file):
        print(f"✓ Đã lưu proxy hoạt động vào {working_file}")
    else:
        print("✗ Lỗi lưu proxy hoạt động")
        return False
    
    # Bước 5: Tạo manager mới và load
    print("5. Tạo manager mới và load proxy...")
    new_manager = ProxyManager()
    
    # Load từ file all proxies
    loaded_count = new_manager.load_proxies_from_file(all_file)
    print(f"✓ Load được {loaded_count} proxy từ file all")
    
    # Kiểm tra stats
    stats = new_manager.get_stats()
    print(f"Stats sau khi load: {stats}")
    
    # Bước 6: Kiểm tra proxy đã load có giữ được trạng thái không
    print("6. Kiểm tra trạng thái proxy...")
    loaded_working = [p for p in new_manager.proxies if hasattr(p, 'is_working') and p.is_working]
    print(f"✓ Proxy working sau khi load: {len(loaded_working)}")
    
    for proxy in loaded_working:
        print(f"  - {proxy.full_address}: {proxy.response_time}ms")
    
    # Bước 7: Test load proxy working
    print("7. Test load proxy working...")
    working_manager = ProxyManager()
    working_loaded = working_manager.load_proxies_from_file(working_file)
    working_stats = working_manager.get_stats()
    
    print(f"✓ Load được {working_loaded} proxy working")
    print(f"Working stats: {working_stats}")
    
    # Cleanup
    try:
        os.remove(all_file)
        os.remove(working_file)
        print("✓ Đã cleanup test files")
    except:
        pass
    
    return True

def test_file_formats():
    """Test các format file khác nhau"""
    print("\n=== TEST CÁC FORMAT FILE ===")
    
    # Tạo file test với các format khác nhau
    test_content = """# Test proxy file
# Format: host:port or host:port:username:password

# Proxy không auth
127.0.0.1:8080
***********:3128  # Comment

# Proxy có auth
proxy.example.com:8080:user1:pass1
proxy2.example.com:3128:user2:pass2  # Auth proxy

# Proxy đã test
working.proxy.com:8080  # Working - 250ms
notworking.proxy.com:3128  # Not working

# Dòng trống và comment

# Dòng không hợp lệ
invalid:line:too:many:parts:here
invalid-no-port
"""
    
    test_file = "test_formats.txt"
    with open(test_file, 'w', encoding='utf-8') as f:
        f.write(test_content)
    
    print(f"✓ Đã tạo file test: {test_file}")
    
    # Load và kiểm tra
    manager = ProxyManager()
    loaded_count = manager.load_proxies_from_file(test_file)
    
    print(f"✓ Load được {loaded_count} proxy")
    
    # Kiểm tra từng proxy
    for proxy in manager.proxies:
        status = ""
        if hasattr(proxy, 'is_working'):
            if proxy.is_working:
                status = f" (Working - {proxy.response_time}ms)"
            else:
                status = " (Not working)"
        
        auth = ""
        if proxy.username:
            auth = f" [Auth: {proxy.username}]"
        
        print(f"  - {proxy.full_address}{auth}{status}")
    
    # Cleanup
    try:
        os.remove(test_file)
        print("✓ Đã cleanup test file")
    except:
        pass
    
    return True

def main():
    """Main function"""
    print("🔧 PROXY SAVE/LOAD TEST TOOL")
    print("=" * 50)
    
    try:
        # Test 1: Chu trình lưu và load
        success1 = test_save_load_cycle()
        
        # Test 2: Các format file
        success2 = test_file_formats()
        
        # Summary
        print("\n" + "=" * 50)
        print("📊 KẾT QUẢ TEST:")
        print(f"Chu trình lưu/load: {'✓ PASS' if success1 else '✗ FAIL'}")
        print(f"Format file: {'✓ PASS' if success2 else '✗ FAIL'}")
        
        if success1 and success2:
            print("\n🎉 TẤT CẢ TEST THÀNH CÔNG!")
            print("Việc lưu và load proxy hoạt động bình thường.")
            print("\n💡 Lưu ý:")
            print("- Proxy working sẽ được load với trạng thái đã test")
            print("- File 'all_proxies.txt' chứa tất cả proxy + trạng thái")
            print("- File 'working_proxies.txt' chỉ chứa proxy hoạt động")
        else:
            print("\n❌ CÓ TEST THẤT BẠI!")
            print("Kiểm tra lại logic lưu/load proxy.")
        
    except Exception as e:
        print(f"\n❌ LỖI KHÔNG MONG MUỐN: {e}")

if __name__ == "__main__":
    main()
