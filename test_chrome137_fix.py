
import sys
from pathlib import Path
sys.path.append(str(Path(__file__).parent / "src"))

def test_chrome137():
    try:
        from selenium import webdriver
        from selenium.webdriver.chrome.options import Options
        from selenium.webdriver.chrome.service import Service
        
        print("🧪 Test Chrome 137 với ChromeDriver mới...")
        
        chrome_options = Options()
        chrome_options.add_argument("--headless")
        chrome_options.add_argument("--no-sandbox")
        chrome_options.add_argument("--disable-dev-shm-usage")
        
        # Sử dụng ChromeDriver local
        chromedriver_path = Path(__file__).parent / "chromedriver.exe"
        if chromedriver_path.exists():
            service = Service(str(chromedriver_path))
            driver = webdriver.Chrome(service=service, options=chrome_options)
        else:
            driver = webdriver.Chrome(options=chrome_options)
        
        driver.get("https://www.google.com")
        title = driver.title
        driver.quit()
        
        print(f"✅ Test thành công! Title: {title}")
        return True
        
    except Exception as e:
        print(f"❌ Test thất bại: {e}")
        return False

if __name__ == "__main__":
    test_chrome137()
    input("Nhấn Enter để thoát...")
