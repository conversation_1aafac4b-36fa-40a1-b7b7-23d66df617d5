using System;

namespace ChromeAutoManager.Models
{
    /// <summary>
    /// Kết quả đăng ký tài khoản
    /// </summary>
    public class RegistrationResult
    {
        public bool Success { get; set; }
        public Account Account { get; set; } = new Account();
        public string ErrorMessage { get; set; } = string.Empty;
        public DateTime CompletedAt { get; set; } = DateTime.Now;
        public TimeSpan Duration { get; set; }
        public string ProxyUsed { get; set; } = string.Empty;
        public string ScreenshotPath { get; set; } = string.Empty;
        public RegistrationStep LastStep { get; set; } = RegistrationStep.None;

        public override string ToString()
        {
            var status = Success ? "THÀNH CÔNG" : "THẤT BẠI";
            var duration = Duration.TotalSeconds.ToString("F1");
            return $"{status} - {Account.Username} ({duration}s) - {ErrorMessage}";
        }
    }

    /// <summary>
    /// Các bước trong quá trình đăng ký
    /// </summary>
    public enum RegistrationStep
    {
        None,
        NavigatingToPage,
        AnalyzingForm,
        FillingUsername,
        FillingPassword,
        FillingConfirmPassword,
        FillingRealName,
        FillingEmail,
        FillingPhone,
        AcceptingTerms,
        SolvingCaptcha,
        SubmittingForm,
        WaitingForResult,
        Completed
    }

    /// <summary>
    /// Trạng thái tiến trình đăng ký
    /// </summary>
    public class RegistrationProgress
    {
        public int AccountIndex { get; set; }
        public RegistrationStep CurrentStep { get; set; }
        public string Message { get; set; } = string.Empty;
        public int ProgressPercentage { get; set; }
        public DateTime Timestamp { get; set; } = DateTime.Now;

        public override string ToString()
        {
            return $"Tài khoản #{AccountIndex}: {CurrentStep} - {Message} ({ProgressPercentage}%)";
        }
    }
}
