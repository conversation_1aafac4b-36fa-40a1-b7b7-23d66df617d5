using System;
using System.IO;
using Serilog;
using Serilog.Events;

namespace ChromeAutoManager.Core
{
    /// <summary>
    /// Logger wrapper cho ứng dụng
    /// </summary>
    public static class Logger
    {
        private static ILogger? _logger;

        static Logger()
        {
            InitializeLogger();
        }

        private static void InitializeLogger()
        {
            try
            {
                // T<PERSON><PERSON> thư mục logs nếu chưa có
                var logsDirectory = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "logs");
                if (!Directory.Exists(logsDirectory))
                {
                    Directory.CreateDirectory(logsDirectory);
                }

                var logFilePath = Path.Combine(logsDirectory, "app-{Date}.log");

                _logger = new LoggerConfiguration()
                    .MinimumLevel.Debug()
                    .MinimumLevel.Override("Microsoft", LogEventLevel.Warning)
                    .MinimumLevel.Override("System", LogEventLevel.Warning)
                    .Enrich.FromLogContext()
                    .WriteTo.Console(
                        outputTemplate: "[{Timestamp:HH:mm:ss} {Level:u3}] {Message:lj}{NewLine}{Exception}")
                    .WriteTo.File(
                        logFilePath,
                        rollingInterval: RollingInterval.Day,
                        retainedFileCountLimit: 7,
                        outputTemplate: "[{Timestamp:yyyy-MM-dd HH:mm:ss.fff zzz} {Level:u3}] {Message:lj}{NewLine}{Exception}")
                    .CreateLogger();

                _logger.Information("Logger initialized successfully");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Failed to initialize logger: {ex.Message}");
                // Fallback to console-only logger
                _logger = new LoggerConfiguration()
                    .WriteTo.Console()
                    .CreateLogger();
            }
        }

        public static void Debug(string message, params object[] args)
        {
            _logger?.Debug(message, args);
        }

        public static void Info(string message, params object[] args)
        {
            _logger?.Information(message, args);
        }

        public static void Warning(string message, params object[] args)
        {
            _logger?.Warning(message, args);
        }

        public static void Error(string message, params object[] args)
        {
            _logger?.Error(message, args);
        }

        public static void Error(Exception exception, string message, params object[] args)
        {
            _logger?.Error(exception, message, args);
        }

        public static void Fatal(string message, params object[] args)
        {
            _logger?.Fatal(message, args);
        }

        public static void Fatal(Exception exception, string message, params object[] args)
        {
            _logger?.Fatal(exception, message, args);
        }

        /// <summary>
        /// Log thông tin đăng ký tài khoản
        /// </summary>
        public static void LogRegistration(bool success, string username, string message)
        {
            var status = success ? "SUCCESS" : "FAILED";
            var logMessage = $"[REGISTRATION] {status} - {username}: {message}";
            
            if (success)
            {
                Info(logMessage);
            }
            else
            {
                Error(logMessage);
            }
        }

        /// <summary>
        /// Log thông tin proxy
        /// </summary>
        public static void LogProxy(string proxyAddress, bool working, int responseTime = 0)
        {
            var status = working ? "WORKING" : "FAILED";
            var message = working 
                ? $"[PROXY] {status} - {proxyAddress} ({responseTime}ms)"
                : $"[PROXY] {status} - {proxyAddress}";
            
            if (working)
            {
                Info(message);
            }
            else
            {
                Warning(message);
            }
        }

        /// <summary>
        /// Log thông tin browser
        /// </summary>
        public static void LogBrowser(string action, bool success, string details = "")
        {
            var status = success ? "SUCCESS" : "FAILED";
            var message = $"[BROWSER] {action} - {status}";
            if (!string.IsNullOrEmpty(details))
            {
                message += $": {details}";
            }

            if (success)
            {
                Info(message);
            }
            else
            {
                Error(message);
            }
        }

        /// <summary>
        /// Đóng logger
        /// </summary>
        public static void CloseAndFlush()
        {
            if (_logger is IDisposable disposableLogger)
            {
                disposableLogger.Dispose();
            }
        }
    }
}
