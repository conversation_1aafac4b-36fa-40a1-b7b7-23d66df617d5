#!/usr/bin/env python3
"""
Demo script để test log proxy chi tiết
"""

import sys
import os
import logging

# Thêm src vào path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from src.proxy_manager import ProxyManager, fetch_free_proxies

def setup_logging():
    """Cấu hình logging"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(),
            logging.FileHandler('proxy_test.log', encoding='utf-8')
        ]
    )

def test_fetch_proxies():
    """Test fetch proxy với log chi tiết"""
    print("=== TEST FETCH PROXY ===")
    
    def progress_callback(message):
        print(f"Progress: {message}")
    
    proxies = fetch_free_proxies(progress_callback)
    print(f"Fetch hoàn thành: {len(proxies)} proxy")
    return proxies

def test_proxy_manager(proxies):
    """Test proxy manager với log chi tiết"""
    print("\n=== TEST PROXY MANAGER ===")
    
    manager = ProxyManager()
    
    # Thêm proxy vào manager
    for proxy in proxies[:20]:  # Chỉ test 20 proxy đầu
        manager.proxies.append(proxy)
    
    print(f"Đã thêm {len(manager.proxies)} proxy vào manager")
    
    # Test proxy với callback
    def progress_callback(tested, total, working):
        progress = (tested / total) * 100
        print(f"Test progress: {tested}/{total} ({progress:.1f}%) - Working: {working}")
    
    working_count = manager.test_all_proxies(max_workers=10, progress_callback=progress_callback)
    print(f"Test hoàn thành: {working_count} proxy hoạt động")
    
    return manager

def main():
    """Main function"""
    setup_logging()
    
    try:
        # Test fetch proxy
        proxies = test_fetch_proxies()
        
        if proxies:
            # Test proxy manager
            manager = test_proxy_manager(proxies)
            
            # Hiển thị thống kê
            stats = manager.get_stats()
            print(f"\n=== THỐNG KÊ ===")
            print(f"Tổng: {stats['total']}")
            print(f"Hoạt động: {stats['working']}")
            print(f"Khả dụng: {stats['available']}")
            
            # Lưu proxy hoạt động
            if stats['working'] > 0:
                manager.save_working_proxies("working_proxies_demo.txt")
                print("Đã lưu proxy hoạt động vào working_proxies_demo.txt")
        else:
            print("Không fetch được proxy nào")
            
    except Exception as e:
        print(f"Lỗi: {e}")
        logging.error(f"Lỗi trong demo: {e}")

if __name__ == "__main__":
    main()
