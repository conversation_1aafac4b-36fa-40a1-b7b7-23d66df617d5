#!/usr/bin/env python3
"""
Test submit button detection và click
"""

import os
import sys
import time

# Thêm src vào path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from win13_registration_service import Win13RegistrationService
from proxy_manager import Proxy<PERSON>anager
from account_generator import Account<PERSON>enerator

def test_submit_button_detection():
    """Test submit button detection"""
    print("🔍 TEST SUBMIT BUTTON DETECTION")
    print("=" * 60)
    
    registration_service = None
    driver = None
    
    try:
        # Setup
        proxy_manager = ProxyManager()
        registration_service = Win13RegistrationService(proxy_manager)
        account_generator = AccountGenerator()
        
        # Generate test account
        account_data = account_generator.generate_complete_account()
        print(f"👤 Test account: {account_data['username']}")
        print(f"👨‍💼 Full name: {account_data['full_name']}")
        
        # Create Chrome
        print("\n🚀 Creating Chrome...")
        driver = registration_service.create_chrome_instance()
        
        if not driver:
            print("❌ Failed to create Chrome")
            return False
        
        # Navigate to 13win
        print("📍 Navigating to 13win...")
        driver.get("https://13win16.com")
        time.sleep(5)
        
        current_url = driver.current_url
        print(f"✓ Current URL: {current_url}")
        
        if "register" not in current_url.lower():
            print("⚠️ Not on registration page, but continuing...")
        
        # Fill form first
        print("\n✏️ Filling form...")
        fill_success = registration_service.fill_registration_form(driver, account_data)
        
        if fill_success:
            print("✅ Form filling successful!")
        else:
            print("⚠️ Form filling had issues")
        
        # Test submit button detection separately
        print("\n🔍 Testing submit button detection...")
        
        # Find all buttons
        from selenium.webdriver.common.by import By
        all_buttons = driver.find_elements(By.TAG_NAME, "button")
        print(f"📋 Found {len(all_buttons)} buttons:")
        
        for i, btn in enumerate(all_buttons):
            try:
                text = btn.text.strip()
                classes = btn.get_attribute("class") or ""
                btn_type = btn.get_attribute("type") or ""
                is_displayed = btn.is_displayed()
                is_enabled = btn.is_enabled()
                
                print(f"  Button {i+1}:")
                print(f"    Text: '{text}'")
                print(f"    Class: '{classes}'")
                print(f"    Type: '{btn_type}'")
                print(f"    Displayed: {is_displayed}")
                print(f"    Enabled: {is_enabled}")
                
                # Highlight button for visual inspection
                if is_displayed:
                    color = "red" if "đăng ký" in text.lower() or "register" in text.lower() else "blue"
                    driver.execute_script(f"arguments[0].style.border='3px solid {color}'", btn)
                
            except Exception as btn_error:
                print(f"  Button {i+1}: Error - {btn_error}")
        
        # Test submit method
        print("\n🚀 Testing submit method...")
        submit_success = registration_service.submit_registration(driver)
        
        if submit_success:
            print("✅ Submit method successful!")
        else:
            print("❌ Submit method failed!")
        
        # Keep browser open for manual inspection
        print("\n👀 Keeping browser open for 30 seconds for manual inspection...")
        print("You can manually check if the submit button was actually clicked.")
        time.sleep(30)
        
        return submit_success
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        return False
        
    finally:
        # Cleanup
        if driver and registration_service:
            try:
                registration_service.chrome_manager.cleanup_driver(driver)
                print("✓ Driver cleaned up")
            except:
                pass
        
        if registration_service:
            try:
                registration_service.chrome_manager.cleanup_all()
                print("✓ All resources cleaned up")
            except:
                pass

def test_manual_submit():
    """Test manual submit button click"""
    print("\n🖱️ TEST MANUAL SUBMIT BUTTON CLICK")
    print("=" * 60)
    
    registration_service = None
    driver = None
    
    try:
        # Setup
        proxy_manager = ProxyManager()
        registration_service = Win13RegistrationService(proxy_manager)
        
        # Create Chrome
        print("🚀 Creating Chrome...")
        driver = registration_service.create_chrome_instance()
        
        if not driver:
            print("❌ Failed to create Chrome")
            return False
        
        # Navigate to 13win
        print("📍 Navigating to 13win...")
        driver.get("https://13win16.com")
        time.sleep(5)
        
        # Manual button detection and click
        print("\n🔍 Manual button detection...")
        
        from selenium.webdriver.common.by import By
        
        # Try different selectors
        selectors_to_try = [
            ("XPath text ĐĂNG KÝ", "//button[contains(text(), 'ĐĂNG KÝ')]"),
            ("XPath text Đăng ký", "//button[contains(text(), 'Đăng ký')]"),
            ("CSS ui-button--primary", "button.ui-button--primary"),
            ("CSS .ui-button--primary", ".ui-button--primary"),
            ("CSS button type submit", "button[type='submit']"),
            ("CSS input type submit", "input[type='submit']")
        ]
        
        for desc, selector in selectors_to_try:
            try:
                print(f"\n🔍 Trying: {desc}")
                print(f"   Selector: {selector}")
                
                if selector.startswith("//"):
                    elements = driver.find_elements(By.XPATH, selector)
                else:
                    elements = driver.find_elements(By.CSS_SELECTOR, selector)
                
                print(f"   Found: {len(elements)} elements")
                
                if elements:
                    element = elements[0]
                    text = element.text.strip()
                    is_displayed = element.is_displayed()
                    is_enabled = element.is_enabled()
                    
                    print(f"   Text: '{text}'")
                    print(f"   Displayed: {is_displayed}")
                    print(f"   Enabled: {is_enabled}")
                    
                    if is_displayed and is_enabled:
                        # Highlight
                        driver.execute_script("arguments[0].style.border='5px solid lime'", element)
                        driver.execute_script("arguments[0].style.backgroundColor='yellow'", element)
                        
                        print(f"   ✅ Found working submit button: {desc}")
                        
                        # Test click
                        print("   🖱️ Testing click...")
                        try:
                            driver.execute_script("arguments[0].click();", element)
                            print("   ✅ Click successful!")
                            time.sleep(3)
                            
                            # Check URL change
                            new_url = driver.current_url
                            print(f"   📍 URL after click: {new_url}")
                            
                            return True
                        except Exception as click_error:
                            print(f"   ❌ Click failed: {click_error}")
                
            except Exception as selector_error:
                print(f"   ❌ Selector failed: {selector_error}")
        
        print("\n❌ No working submit button found")
        return False
        
    except Exception as e:
        print(f"❌ Manual test failed: {e}")
        return False
        
    finally:
        # Cleanup
        if driver and registration_service:
            try:
                registration_service.chrome_manager.cleanup_driver(driver)
                print("✓ Driver cleaned up")
            except:
                pass

def main():
    """Main function"""
    try:
        print("🧪 SUBMIT BUTTON TEST SUITE")
        print("=" * 60)
        
        # Test 1: Submit button detection
        test1_success = test_submit_button_detection()
        
        # Test 2: Manual submit
        test2_success = test_manual_submit()
        
        # Summary
        print("\n" + "=" * 60)
        print("📊 TEST SUMMARY:")
        print(f"Submit button detection: {'✅ PASS' if test1_success else '❌ FAIL'}")
        print(f"Manual submit test: {'✅ PASS' if test2_success else '❌ FAIL'}")
        
        if test1_success or test2_success:
            print("\n🎉 SUBMIT BUTTON WORKING!")
            print("Submit functionality is operational.")
        else:
            print("\n⚠️ SUBMIT BUTTON ISSUES!")
            print("Need to investigate submit button detection.")
            
    except KeyboardInterrupt:
        print("\n\nTest interrupted by user.")
    except Exception as e:
        print(f"\nTest suite failed: {e}")

if __name__ == "__main__":
    main()
