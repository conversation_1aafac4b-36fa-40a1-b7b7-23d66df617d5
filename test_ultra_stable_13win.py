#!/usr/bin/env python3
"""
Test tool 13win với ChromeStabilityManager
"""

import os
import sys
import time

# Thêm src vào path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from win13_registration_service import Win13RegistrationService
from proxy_manager import ProxyManager
from account_generator import AccountGenerator

def test_ultra_stable_registration():
    """Test đăng ký với ultra-stable Chrome"""
    print("🚀 TEST ULTRA-STABLE 13WIN REGISTRATION")
    print("=" * 60)
    
    try:
        # Setup services
        print("🔧 Setting up services...")
        proxy_manager = ProxyManager()
        registration_service = Win13RegistrationService(proxy_manager)
        account_generator = AccountGenerator()
        
        # Tạo test account
        print("👤 Generating test account...")
        account_data = account_generator.generate_complete_account()
        print(f"✓ Username: {account_data['username']}")
        print(f"✓ Password: {account_data['password']}")
        print(f"✓ Full Name: {account_data['full_name']}")
        print(f"✓ Email: {account_data['email']}")
        
        # Test Chrome creation
        print("\n🚀 Testing ultra-stable Chrome creation...")
        driver = registration_service.create_chrome_instance()
        
        if not driver:
            print("❌ Failed to create Chrome instance")
            return False
        
        print("✅ Ultra-stable Chrome created successfully!")
        
        # Test navigation to 13win
        print("\n📍 Testing navigation to 13win...")
        try:
            driver.get("https://13win16.com")
            
            # Wait for page load
            time.sleep(5)
            
            current_url = driver.current_url
            print(f"✓ Current URL: {current_url}")
            
            # Check if it's registration page
            if "register" in current_url.lower():
                print("✅ Successfully navigated to registration page")
            else:
                print("⚠️ Not on registration page, but navigation successful")
            
        except Exception as nav_error:
            print(f"❌ Navigation failed: {nav_error}")
            return False
        
        # Test form filling
        print("\n✏️ Testing form filling...")
        try:
            fill_success = registration_service.fill_registration_form(driver, account_data)
            
            if fill_success:
                print("✅ Form filling successful!")
            else:
                print("❌ Form filling failed")
                
        except Exception as form_error:
            print(f"❌ Form filling error: {form_error}")
        
        # Keep browser open for inspection
        print("\n👀 Keeping browser open for 30 seconds for inspection...")
        print("You can manually check the form filling results.")
        time.sleep(30)
        
        return True
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        return False
        
    finally:
        # Cleanup
        try:
            if 'driver' in locals() and driver:
                registration_service.chrome_manager.cleanup_driver(driver)
                print("✓ Driver cleaned up")
        except:
            pass
        
        try:
            registration_service.chrome_manager.cleanup_all()
            print("✓ All resources cleaned up")
        except:
            pass

def test_multiple_instances():
    """Test multiple Chrome instances"""
    print("\n🔄 TEST MULTIPLE ULTRA-STABLE INSTANCES")
    print("=" * 60)
    
    try:
        # Setup
        proxy_manager = ProxyManager()
        registration_service = Win13RegistrationService(proxy_manager)
        
        drivers = []
        success_count = 0
        
        # Create 2 instances with delay
        for i in range(2):
            print(f"\n🚀 Creating Chrome instance {i+1}...")
            
            # Delay between instances
            if i > 0:
                print("⏳ Delay 5 seconds...")
                time.sleep(5)
            
            driver = registration_service.create_chrome_instance()
            
            if driver:
                drivers.append(driver)
                success_count += 1
                print(f"✅ Instance {i+1} created successfully")
                
                # Test basic navigation
                try:
                    driver.get("https://www.google.com")
                    title = driver.title
                    print(f"✓ Instance {i+1} navigation test: {title}")
                except Exception as nav_error:
                    print(f"⚠️ Instance {i+1} navigation failed: {nav_error}")
            else:
                print(f"❌ Instance {i+1} creation failed")
        
        print(f"\n📊 Results: {success_count}/2 instances created successfully")
        
        if success_count == 2:
            print("🎉 Multiple instances test: PASS")
            return True
        else:
            print("❌ Multiple instances test: FAIL")
            return False
        
    except Exception as e:
        print(f"❌ Multiple instances test failed: {e}")
        return False
        
    finally:
        # Cleanup all drivers
        for i, driver in enumerate(drivers):
            try:
                registration_service.chrome_manager.cleanup_driver(driver)
                print(f"✓ Instance {i+1} cleaned up")
            except:
                pass
        
        try:
            registration_service.chrome_manager.cleanup_all()
            print("✓ All resources cleaned up")
        except:
            pass

def main():
    """Main function"""
    try:
        print("🧪 ULTRA-STABLE 13WIN REGISTRATION TEST SUITE")
        print("=" * 60)
        
        # Test 1: Single instance registration
        test1_success = test_ultra_stable_registration()
        
        # Test 2: Multiple instances
        test2_success = test_multiple_instances()
        
        # Summary
        print("\n" + "=" * 60)
        print("📊 TEST SUMMARY:")
        print(f"Single instance registration: {'✅ PASS' if test1_success else '❌ FAIL'}")
        print(f"Multiple instances: {'✅ PASS' if test2_success else '❌ FAIL'}")
        
        if test1_success and test2_success:
            print("\n🎉 ALL TESTS PASSED!")
            print("Ultra-stable Chrome is ready for production use.")
            print("\n💡 Ready to run:")
            print("python main.py")
        else:
            print("\n⚠️ SOME TESTS FAILED!")
            print("Please check the issues above.")
            
    except KeyboardInterrupt:
        print("\n\nTest interrupted by user.")
    except Exception as e:
        print(f"\nTest suite failed: {e}")

if __name__ == "__main__":
    main()
