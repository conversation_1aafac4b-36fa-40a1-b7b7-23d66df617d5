#!/usr/bin/env python3
"""
Simple test for proxy fix
"""

import os
import sys
import time

# Thêm src vào path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from win13_registration_service import Win13RegistrationService
from proxy_manager import Proxy<PERSON><PERSON><PERSON>

def test_simple_no_proxy():
    """Test simple Chrome without proxy"""
    print("🧪 SIMPLE NO-PROXY TEST")
    print("=" * 50)
    
    registration_service = None
    driver = None
    
    try:
        # Setup
        proxy_manager = ProxyManager()
        registration_service = Win13RegistrationService(proxy_manager)
        
        # Create Chrome without proxy
        print("🚀 Creating Chrome without proxy...")
        driver = registration_service.create_chrome_instance()
        
        if not driver:
            print("❌ Failed to create Chrome")
            return False
        
        print("✅ Chrome created successfully!")
        
        # Test Google
        print("📍 Testing Google navigation...")
        driver.get("https://www.google.com")
        time.sleep(2)
        title = driver.title
        print(f"✓ Google title: {title}")
        
        # Test 13win
        print("📍 Testing 13win navigation...")
        driver.get("https://13win16.com")
        time.sleep(5)
        current_url = driver.current_url
        print(f"✓ 13win URL: {current_url}")
        
        if "13win" in current_url:
            print("✅ Successfully accessed 13win!")
            return True
        else:
            print("⚠️ Could not access 13win")
            return False
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        return False
        
    finally:
        # Cleanup
        if driver and registration_service:
            try:
                registration_service.chrome_manager.cleanup_driver(driver)
                print("✓ Driver cleaned up")
            except:
                pass
        
        if registration_service:
            try:
                registration_service.chrome_manager.cleanup_all()
                print("✓ All resources cleaned up")
            except:
                pass

def test_bad_proxy_fallback():
    """Test bad proxy fallback"""
    print("\n🧪 BAD PROXY FALLBACK TEST")
    print("=" * 50)
    
    registration_service = None
    driver = None
    
    try:
        # Setup
        proxy_manager = ProxyManager()
        registration_service = Win13RegistrationService(proxy_manager)
        
        # Bad proxy
        bad_proxy = {
            'host': '192.168.1.999',  # Fake IP
            'port': 8080,
            'type': 'http'
        }
        
        # Create Chrome with bad proxy (should fallback)
        print("🚀 Creating Chrome with bad proxy (should fallback)...")
        driver = registration_service.create_chrome_instance(bad_proxy)
        
        if not driver:
            print("❌ Failed to create Chrome even with fallback")
            return False
        
        print("✅ Chrome created successfully (fallback worked)!")
        
        # Test navigation
        print("📍 Testing navigation after fallback...")
        driver.get("https://www.google.com")
        time.sleep(2)
        title = driver.title
        print(f"✓ Navigation successful: {title}")
        
        return True
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        return False
        
    finally:
        # Cleanup
        if driver and registration_service:
            try:
                registration_service.chrome_manager.cleanup_driver(driver)
                print("✓ Driver cleaned up")
            except:
                pass
        
        if registration_service:
            try:
                registration_service.chrome_manager.cleanup_all()
                print("✓ All resources cleaned up")
            except:
                pass

def test_13win_form_no_proxy():
    """Test 13win form filling without proxy"""
    print("\n🎯 13WIN FORM TEST (NO PROXY)")
    print("=" * 50)
    
    registration_service = None
    driver = None
    
    try:
        # Setup
        proxy_manager = ProxyManager()
        registration_service = Win13RegistrationService(proxy_manager)
        
        # Generate test account
        from account_generator import AccountGenerator
        account_generator = AccountGenerator()
        account_data = account_generator.generate_complete_account()
        
        print(f"👤 Test account: {account_data['username']}")
        print(f"👨‍💼 Full name: {account_data['full_name']}")
        
        # Create Chrome without proxy
        print("🚀 Creating Chrome for 13win test...")
        driver = registration_service.create_chrome_instance()
        
        if not driver:
            print("❌ Failed to create Chrome")
            return False
        
        # Navigate to 13win
        print("📍 Navigating to 13win...")
        driver.get("https://13win16.com")
        time.sleep(5)
        
        current_url = driver.current_url
        print(f"✓ Current URL: {current_url}")
        
        # Fill form if on registration page
        if "register" in current_url.lower():
            print("✏️ Filling registration form...")
            fill_success = registration_service.fill_registration_form(driver, account_data)
            
            if fill_success:
                print("✅ Form filling successful!")
            else:
                print("⚠️ Form filling had issues")
        else:
            print("⚠️ Not on registration page")
        
        # Keep open for inspection
        print("👀 Keeping browser open for 15 seconds for inspection...")
        time.sleep(15)
        
        return True
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        return False
        
    finally:
        # Cleanup
        if driver and registration_service:
            try:
                registration_service.chrome_manager.cleanup_driver(driver)
                print("✓ Driver cleaned up")
            except:
                pass
        
        if registration_service:
            try:
                registration_service.chrome_manager.cleanup_all()
                print("✓ All resources cleaned up")
            except:
                pass

def main():
    """Main function"""
    try:
        print("🔧 SIMPLE PROXY FIX TEST SUITE")
        print("=" * 60)
        
        # Test 1: No proxy
        test1_success = test_simple_no_proxy()
        
        # Test 2: Bad proxy fallback
        test2_success = test_bad_proxy_fallback()
        
        # Test 3: 13win form
        test3_success = test_13win_form_no_proxy()
        
        # Summary
        print("\n" + "=" * 60)
        print("📊 TEST SUMMARY:")
        print(f"No proxy test: {'✅ PASS' if test1_success else '❌ FAIL'}")
        print(f"Bad proxy fallback: {'✅ PASS' if test2_success else '❌ FAIL'}")
        print(f"13win form test: {'✅ PASS' if test3_success else '❌ FAIL'}")
        
        if test1_success and test2_success and test3_success:
            print("\n🎉 ALL TESTS PASSED!")
            print("Proxy fix is working correctly.")
            print("Tool can handle proxy issues and work without proxy.")
            print("\n💡 Ready to run main tool:")
            print("python main.py")
        else:
            print("\n⚠️ SOME TESTS FAILED!")
            print("Please check the issues above.")
            
    except KeyboardInterrupt:
        print("\n\nTest interrupted by user.")
    except Exception as e:
        print(f"\nTest suite failed: {e}")

if __name__ == "__main__":
    main()
