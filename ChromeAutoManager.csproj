<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <OutputType>WinExe</OutputType>
    <TargetFramework>net6.0-windows</TargetFramework>
    <Nullable>enable</Nullable>
    <UseWindowsForms>true</UseWindowsForms>
    <ImplicitUsings>enable</ImplicitUsings>
    <ApplicationIcon>icon.ico</ApplicationIcon>
    <AssemblyTitle>Chrome Auto Manager</AssemblyTitle>
    <AssemblyDescription>Tool tự động đăng ký tài khoản với GUI quản lý</AssemblyDescription>
    <AssemblyCompany>EderGhostVN</AssemblyCompany>
    <AssemblyProduct>Chrome Auto Manager</AssemblyProduct>
    <AssemblyCopyright>Copyright © 2024</AssemblyCopyright>
    <AssemblyVersion>*******</AssemblyVersion>
    <FileVersion>*******</FileVersion>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Selenium.WebDriver" Version="4.15.0" />
    <PackageReference Include="Selenium.WebDriver.ChromeDriver" Version="119.0.6045.10500" />
    <PackageReference Include="Selenium.Support" Version="4.15.0" />
    <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
    <PackageReference Include="Bogus" Version="34.0.2" />
    <PackageReference Include="Serilog" Version="3.0.1" />
    <PackageReference Include="Serilog.Sinks.File" Version="5.0.0" />
    <PackageReference Include="Serilog.Sinks.Console" Version="4.1.0" />
    <PackageReference Include="System.Net.Http" Version="4.3.4" />
  </ItemGroup>

  <ItemGroup>
    <Folder Include="Forms\" />
    <Folder Include="Models\" />
    <Folder Include="Services\" />
    <Folder Include="Core\" />
    <Folder Include="Resources\" />
  </ItemGroup>

</Project>
