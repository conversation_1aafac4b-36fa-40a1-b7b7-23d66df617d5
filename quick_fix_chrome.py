#!/usr/bin/env python3
"""
Quick fix cho lỗi ChromeDriver version mismatch
"""

import os
import shutil
import requests
import zipfile
import subprocess

def quick_fix():
    """Fix nhanh ChromeDriver cho Chrome 137"""
    print("🔧 QUICK FIX CHROMEDRIVER CHO CHROME 137")
    print("=" * 50)

    try:
        # Bước 1: Xóa cache cũ
        print("1. Xóa cache ChromeDriver cũ...")
        cache_dir = os.path.expanduser("~/.wdm")
        if os.path.exists(cache_dir):
            shutil.rmtree(cache_dir, ignore_errors=True)
            print("✓ Đã xóa cache")

        # Bước 2: Tải ChromeDriver 137
        print("2. Tải ChromeDriver 137...")

        # Tạ<PERSON> thư mục download an toàn
        download_dir = "chromedriver_137"
        try:
            os.makedirs(download_dir, exist_ok=True)

            # Test quyền write
            test_file = os.path.join(download_dir, "test.tmp")
            with open(test_file, 'w') as f:
                f.write("test")
            os.remove(test_file)

        except Exception as dir_error:
            print(f"⚠ <PERSON>hông thể tạo thư mục {download_dir}: {dir_error}")
            print("Thử sử dụng temp directory...")

            import tempfile
            download_dir = os.path.join(tempfile.gettempdir(), "chromedriver_137")
            os.makedirs(download_dir, exist_ok=True)

        # URL cho ChromeDriver 137
        url = "https://storage.googleapis.com/chrome-for-testing-public/137.0.7151.55/win32/chromedriver-win32.zip"

        print(f"Đang tải từ: {url}")
        response = requests.get(url, timeout=60)
        response.raise_for_status()

        zip_path = os.path.join(download_dir, "chromedriver.zip")
        with open(zip_path, 'wb') as f:
            f.write(response.content)

        print("✓ Đã tải xong")

        # Bước 3: Giải nén
        print("3. Giải nén ChromeDriver...")
        with zipfile.ZipFile(zip_path, 'r') as zip_ref:
            zip_ref.extractall(download_dir)

        # Tìm file chromedriver.exe
        chromedriver_path = None
        for root, dirs, files in os.walk(download_dir):
            for file in files:
                if file == "chromedriver.exe":
                    chromedriver_path = os.path.join(root, file)
                    break
            if chromedriver_path:
                break

        if not chromedriver_path:
            print("❌ Không tìm thấy chromedriver.exe")
            return False

        print(f"✓ Tìm thấy: {chromedriver_path}")

        # Bước 4: Test ChromeDriver
        print("4. Test ChromeDriver...")
        try:
            result = subprocess.run([chromedriver_path, "--version"],
                                  capture_output=True, text=True, timeout=10)
            if result.returncode == 0:
                print(f"✓ ChromeDriver version: {result.stdout.strip()}")
            else:
                print("❌ ChromeDriver không chạy được")
                return False
        except Exception as e:
            print(f"❌ Lỗi test ChromeDriver: {e}")
            return False

        # Bước 5: Copy vào thư mục tool
        print("5. Copy ChromeDriver vào thư mục tool...")
        tool_chromedriver = os.path.join(os.getcwd(), "chromedriver.exe")
        shutil.copy2(chromedriver_path, tool_chromedriver)
        print(f"✓ Đã copy vào: {tool_chromedriver}")

        # Bước 6: Test với Selenium
        print("6. Test với Selenium...")
        try:
            from selenium import webdriver
            from selenium.webdriver.chrome.service import Service
            from selenium.webdriver.chrome.options import Options

            options = Options()
            options.add_argument("--headless")
            options.add_argument("--no-sandbox")
            options.add_argument("--disable-dev-shm-usage")

            service = Service(tool_chromedriver)
            driver = webdriver.Chrome(service=service, options=options)
            driver.get("https://www.google.com")
            title = driver.title
            driver.quit()

            print(f"✓ Selenium test thành công! Title: {title}")

        except Exception as e:
            print(f"❌ Selenium test thất bại: {e}")
            return False

        print("\n🎉 FIX THÀNH CÔNG!")
        print("ChromeDriver 137 đã sẵn sàng sử dụng.")
        print("Bây giờ bạn có thể chạy tool đăng ký.")

        return True

    except Exception as e:
        print(f"❌ Lỗi: {e}")
        return False

def main():
    """Main function"""
    try:
        success = quick_fix()

        if not success:
            print("\n❌ FIX THẤT BẠI!")
            print("\n🔧 Thử các cách khác:")
            print("1. Cập nhật Chrome lên version mới nhất")
            print("2. Tải ChromeDriver thủ công từ:")
            print("   https://storage.googleapis.com/chrome-for-testing-public/137.0.7151.55/win32/chromedriver-win32.zip")
            print("3. Giải nén và đặt chromedriver.exe vào thư mục tool")

        input("\nNhấn Enter để thoát...")

    except KeyboardInterrupt:
        print("\n\nĐã hủy bởi người dùng.")
    except Exception as e:
        print(f"\nLỗi không mong muốn: {e}")

if __name__ == "__main__":
    main()
