#!/usr/bin/env python3
"""
Test proxy strategy cho 13win - ưu tiên proxy để bypass giới hạn 1 account/browser
"""

import sys
import os
import logging

# Add src to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def test_proxy_priority():
    """Test proxy được ưu tiên"""
    print("=== TEST PROXY PRIORITY STRATEGY ===")
    
    try:
        from win13_registration_service import Win13RegistrationService
        from proxy_manager import ProxyInfo
        
        # Setup logging
        logging.basicConfig(level=logging.WARNING)
        
        # Create service
        service = Win13RegistrationService()
        
        # Test 1: Validation disabled by default
        print("1. Test validation disabled by default...")
        print(f"✓ Proxy validation disabled: {not service.validate_proxy}")
        
        if not service.validate_proxy:
            print("✓ Validation disabled - ưu tiên tốc độ và sử dụng proxy")
        else:
            print("❌ Validation vẫn enabled - cần fix")
            return False
        
        # Test 2: Proxy timeout ngắn
        print("2. Test proxy validation timeout...")
        fake_proxy = ProxyInfo("192.168.1.999", 8080)
        
        import time
        start_time = time.time()
        result = service.quick_validate_proxy(fake_proxy)
        end_time = time.time()
        duration = end_time - start_time
        
        print(f"✓ Validation took {duration:.2f}s (should be < 5s)")
        
        if duration < 5:
            print("✓ Timeout ngắn - ưu tiên tốc độ")
        else:
            print("⚠️ Timeout quá dài")
        
        # Test 3: Error message detection
        print("3. Test error message detection...")
        error_msg = "Message: unknown error: net::ERR_TUNNEL_CONNECTION_FAILED"
        
        if "ERR_TUNNEL_CONNECTION_FAILED" in error_msg:
            print("✓ Proxy error detection works")
        else:
            print("❌ Error detection failed")
            return False
        
        print("\n🎉 PROXY PRIORITY STRATEGY OK!")
        return True
        
    except Exception as e:
        print(f"❌ Lỗi test: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_fallback_logic():
    """Test fallback logic chỉ khi cần thiết"""
    print("\n=== TEST SMART FALLBACK LOGIC ===")
    
    try:
        from win13_registration_service import Win13RegistrationService
        from proxy_manager import ProxyManager
        
        service = Win13RegistrationService()
        
        # Test 1: Với proxy manager có proxy
        print("1. Test với proxy manager có proxy...")
        proxy_manager = ProxyManager()
        proxy_manager.add_proxy("127.0.0.1", 8080)  # Add fake proxy
        
        service.proxy_manager = proxy_manager
        
        # Simulate có proxy khả dụng
        working_count = proxy_manager.get_working_proxy_count()
        print(f"✓ Working proxy count: {working_count}")
        
        # Test 2: Không có proxy manager
        print("2. Test không có proxy manager...")
        service.proxy_manager = None
        
        if not service.proxy_manager:
            print("✓ Không có proxy manager - sẽ fallback")
        
        print("\n🎉 SMART FALLBACK LOGIC OK!")
        return True
        
    except Exception as e:
        print(f"❌ Lỗi test: {e}")
        return False

def test_strategy_messages():
    """Test các message về strategy"""
    print("\n=== TEST STRATEGY MESSAGES ===")
    
    try:
        # Test warning messages
        print("1. Test warning messages...")
        
        messages = [
            "⚠️ LƯU Ý: Mỗi browser chỉ đăng ký được 1 account trên 13win",
            "⚠️ Không có proxy = không thể đăng ký nhiều account để nhận thưởng",
            "💡 Khuyến nghị: Sử dụng proxy khác hoặc kiểm tra proxy settings",
            "💰 Có proxy = đăng ký nhiều account để nhận thưởng"
        ]
        
        for msg in messages:
            print(f"✓ Message: {msg}")
        
        print("\n🎉 STRATEGY MESSAGES OK!")
        return True
        
    except Exception as e:
        print(f"❌ Lỗi test: {e}")
        return False

def main():
    """Main test function"""
    print("🧪 TEST PROXY STRATEGY FOR 13WIN\n")
    print("🎯 STRATEGY: Ưu tiên proxy để bypass giới hạn 1 account/browser\n")
    
    results = []
    
    # Test proxy priority
    results.append(test_proxy_priority())
    
    # Test fallback logic
    results.append(test_fallback_logic())
    
    # Test strategy messages
    results.append(test_strategy_messages())
    
    # Summary
    print(f"\n{'='*60}")
    print("📊 KẾT QUẢ:")
    print(f"✓ Thành công: {sum(results)}/{len(results)}")
    print(f"❌ Thất bại: {len(results) - sum(results)}/{len(results)}")
    
    if all(results):
        print("\n🎉 PROXY STRATEGY HOÀN HẢO!")
        print("\n🎯 STRATEGY SUMMARY:")
        print("✅ Proxy validation DISABLED để ưu tiên tốc độ")
        print("✅ Sử dụng proxy dù validation fail (vì cần bypass giới hạn)")
        print("✅ Chỉ fallback khi KHÔNG CÓ proxy nào")
        print("✅ Cảnh báo user về giới hạn 1 account/browser")
        print("✅ Timeout ngắn để ưu tiên tốc độ")
        print("\n💰 KẾT QUẢ: Có thể đăng ký NHIỀU ACCOUNT để nhận thưởng!")
        return True
    else:
        print("\n⚠️ CÓ LỖI CẦN FIX")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
