# 🔧 Fix Lỗi Chrome - WinError 193

## ❌ Lỗi gặp phải:
```
[WinError 193] %1 is not a valid Win32 application
```

## 🚀 Cách fix nhanh:

### Bước 1: Chạy tool fix tự động
```bash
python fix_chromedriver.py
```

### Bước 2: Nếu vẫn lỗi, xóa cache và thử lại
```bash
# Windows
rmdir /s %USERPROFILE%\.wdm

# Sau đó chạy lại tool
python main.py
```

### Bước 3: Nếu vẫn không được, cài đặt lại dependencies
```bash
pip uninstall webdriver-manager selenium
pip install webdriver-manager==4.0.1 selenium==4.15.2
```

## 🎯 Test xem đã fix chưa:
```bash
python test_chrome.py
```

## 💡 Nguyên nhân:
- ChromeDriver không tương thích với hệ thống
- Cache ChromeDriver bị corrupt
- Version conflict

## ✅ Sau khi fix:
- Tool sẽ tạo được Chrome instances
- <PERSON><PERSON> thể đăng ký tài khoản bình thường
- <PERSON>h<PERSON>ng còn lỗi WinError 193

## 📞 Nếu vẫn lỗi:
1. Cài đặt Chrome browser mới nhất
2. Chạy với quyền Administrator  
3. Tắt antivirus tạm thời
4. Xem file `CHROME_TROUBLESHOOTING.md` để biết thêm chi tiết
