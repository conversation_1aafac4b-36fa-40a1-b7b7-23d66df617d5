@echo off
echo ===================================
echo 13Win16 Auto Registration Tool
echo ===================================
echo.

REM Check if Python is installed
python --version >nul 2>&1
if errorlevel 1 (
    echo Python is not installed or not in PATH
    echo Please install Python 3.8+ from https://python.org
    pause
    exit /b 1
)

echo Python found. Starting tool...
echo.

REM Install requirements if needed
if not exist "venv\" (
    echo Creating virtual environment...
    python -m venv venv
)

echo Activating virtual environment...
call venv\Scripts\activate.bat

echo Installing/updating requirements...
pip install -r requirements.txt

echo.
echo Checking Chrome browser...
chrome --version >nul 2>&1
if errorlevel 1 (
    echo WARNING: Chrome browser not found!
    echo Please install Chrome from: https://www.google.com/chrome/
    echo.
    echo You can still run the tool, but it may not work properly.
    echo.
    pause
)

echo.
echo Testing Chrome compatibility...
python test_chrome.py >nul 2>&1
if errorlevel 1 (
    echo WARNING: Chrome test failed!
    echo.
    echo Do you want to run ChromeDriver fix tool? (y/n)
    set /p choice=
    if /i "%choice%"=="y" (
        echo Running ChromeDriver fix...
        python fix_chromedriver.py
        echo.
        pause
    )
)

echo.
echo Starting 13Win16 Auto Registration Tool...
echo TIP: Click "Test Chrome" button first to verify Chrome works
echo.

python main.py

echo.
echo Tool finished. Press any key to exit...
pause >nul
