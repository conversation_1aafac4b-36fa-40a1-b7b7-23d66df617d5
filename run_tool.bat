@echo off
echo ===================================
echo 13Win16 Auto Registration Tool
echo ===================================
echo.

REM Check if Python is installed
python --version >nul 2>&1
if errorlevel 1 (
    echo Python is not installed or not in PATH
    echo Please install Python 3.8+ from https://python.org
    pause
    exit /b 1
)

echo Python found. Starting tool...
echo.

REM Install requirements if needed
if not exist "venv\" (
    echo Creating virtual environment...
    python -m venv venv
)

echo Activating virtual environment...
call venv\Scripts\activate.bat

echo Installing/updating requirements...
pip install -r requirements.txt

echo.
echo Starting 13Win16 Auto Registration Tool...
echo.

python main.py

echo.
echo Tool finished. Press any key to exit...
pause >nul
