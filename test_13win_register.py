#!/usr/bin/env python3
"""
Test cụ thể trang đăng ký 13win
"""

import os
import sys
import time

# Thêm src vào path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.support.ui import WebDriverWait

def test_13win_register():
    """Test trang đăng ký 13win"""
    print("🔍 TEST 13WIN REGISTER PAGE")
    print("=" * 50)

    driver = None
    try:
        # Setup Chrome với config tối ưu
        options = Options()
        options.add_argument("--no-sandbox")
        options.add_argument("--disable-dev-shm-usage")
        options.add_argument("--disable-gpu")
        options.add_argument("--disable-images")  # Tăng tốc load

        # Sử dụng ChromeDriver local
        chromedriver_path = "chromedriver.exe"
        if os.path.exists(chromedriver_path):
            service = Service(chromedriver_path)
            print("✓ Sử dụng ChromeDriver local")
        else:
            print("❌ Không tìm thấy chromedriver.exe")
            return False

        print("🚀 Khởi tạo Chrome...")
        driver = webdriver.Chrome(service=service, options=options)
        print("✓ Chrome đã sẵn sàng")

        # Điều hướng đến 13win
        url = "https://13win16.com"
        print(f"\n📍 Truy cập: {url}")
        driver.get(url)

        # Chờ page load
        print("⏳ Chờ trang load...")
        WebDriverWait(driver, 15).until(
            lambda d: d.execute_script("return document.readyState") == "complete"
        )

        current_url = driver.current_url
        title = driver.title
        print(f"✓ Current URL: {current_url}")
        print(f"✓ Title: {title}")

        # Chờ thêm để JS load
        print("⏳ Chờ JavaScript load form...")
        time.sleep(5)

        # Tìm tất cả inputs với retry
        print("\n📝 Phân tích form inputs...")

        max_retries = 10
        inputs = []

        for attempt in range(max_retries):
            inputs = driver.find_elements(By.TAG_NAME, "input")
            if len(inputs) > 0:
                print(f"✓ Tìm thấy {len(inputs)} inputs sau {attempt + 1} lần thử")
                break
            else:
                print(f"  Chờ inputs xuất hiện... ({attempt + 1}/{max_retries})")
                time.sleep(1)

        if len(inputs) == 0:
            print("❌ Không tìm thấy inputs nào")

            # Debug: log page source
            page_source = driver.page_source
            print(f"\nPage source length: {len(page_source)}")

            # Tìm các elements khác
            divs = driver.find_elements(By.TAG_NAME, "div")
            forms = driver.find_elements(By.TAG_NAME, "form")
            buttons = driver.find_elements(By.TAG_NAME, "button")

            print(f"Divs: {len(divs)}, Forms: {len(forms)}, Buttons: {len(buttons)}")

            return False

        # Phân tích từng input
        print(f"\n📋 Chi tiết {len(inputs)} inputs:")

        input_data = []
        for i, inp in enumerate(inputs):
            try:
                name = inp.get_attribute("name") or ""
                id_attr = inp.get_attribute("id") or ""
                type_attr = inp.get_attribute("type") or "text"
                placeholder = inp.get_attribute("placeholder") or ""
                class_attr = inp.get_attribute("class") or ""
                value = inp.get_attribute("value") or ""

                data = {
                    'index': i + 1,
                    'name': name,
                    'id': id_attr,
                    'type': type_attr,
                    'placeholder': placeholder,
                    'class': class_attr,
                    'value': value,
                    'element': inp
                }
                input_data.append(data)

                print(f"  {i+1}. type='{type_attr}', name='{name}', id='{id_attr}'")
                if placeholder:
                    print(f"      placeholder='{placeholder}'")
                if class_attr:
                    print(f"      class='{class_attr}'")

            except Exception as e:
                print(f"  {i+1}. Error reading input: {e}")

        # Tìm buttons
        buttons = driver.find_elements(By.TAG_NAME, "button")
        print(f"\n🔘 Tìm thấy {len(buttons)} buttons:")

        for i, btn in enumerate(buttons):
            try:
                text = btn.text.strip()
                type_attr = btn.get_attribute("type") or ""
                class_attr = btn.get_attribute("class") or ""

                print(f"  {i+1}. text='{text}', type='{type_attr}', class='{class_attr}'")

            except Exception as e:
                print(f"  {i+1}. Error reading button: {e}")

        # Test điền form
        print("\n✏️ Test điền form...")

        # Tìm các field quan trọng
        username_input = None
        email_input = None
        password_input = None

        for data in input_data:
            inp = data['element']
            name = data['name'].lower()
            id_attr = data['id'].lower()
            placeholder = data['placeholder'].lower()
            type_attr = data['type'].lower()

            # Username field (13win: "Nhập Số điện thoại/Tên Đăng Nhập")
            if not username_input and any(keyword in placeholder for keyword in ['điện thoại', 'đăng nhập', 'tên đăng nhập']):
                username_input = inp
                print(f"✓ Username field: placeholder='{data['placeholder']}'")

            # Email field
            if not email_input and (type_attr == 'email' or any(keyword in name + id_attr + placeholder for keyword in ['email', 'mail'])):
                email_input = inp
                print(f"✓ Email field: {data['name'] or data['id'] or 'no-name'}")

            # Password field (13win: "Mật khẩu" - type='text' không phải 'password')
            if not password_input and any(keyword in placeholder for keyword in ['mật khẩu']) and 'xác nhận' not in placeholder:
                password_input = inp
                print(f"✓ Password field: placeholder='{data['placeholder']}'")

            # Full name field (13win: "Họ Tên Thật")
            if any(keyword in placeholder for keyword in ['họ tên', 'tên thật']):
                full_name_input = inp
                print(f"✓ Full name field: placeholder='{data['placeholder']}'")

        # Test điền data
        test_data = {
            'username': 'testuser123',
            'email': '<EMAIL>',
            'password': 'TestPass123!',
            'full_name': 'Nguyễn Văn Test'
        }

        filled_count = 0
        full_name_input = None  # Initialize variable

        if username_input:
            try:
                driver.execute_script("arguments[0].scrollIntoView(true);", username_input)
                time.sleep(0.5)
                driver.execute_script("arguments[0].style.border='3px solid red'", username_input)
                username_input.clear()
                username_input.send_keys(test_data['username'])
                print(f"✓ Đã điền username: {test_data['username']}")
                filled_count += 1
                time.sleep(1)
            except Exception as e:
                print(f"✗ Lỗi điền username: {e}")

        if email_input:
            try:
                driver.execute_script("arguments[0].scrollIntoView(true);", email_input)
                time.sleep(0.5)
                driver.execute_script("arguments[0].style.border='3px solid blue'", email_input)
                email_input.clear()
                email_input.send_keys(test_data['email'])
                print(f"✓ Đã điền email: {test_data['email']}")
                filled_count += 1
                time.sleep(1)
            except Exception as e:
                print(f"✗ Lỗi điền email: {e}")

        if password_input:
            try:
                driver.execute_script("arguments[0].scrollIntoView(true);", password_input)
                time.sleep(0.5)
                driver.execute_script("arguments[0].style.border='3px solid green'", password_input)
                password_input.clear()
                password_input.send_keys(test_data['password'])
                print(f"✓ Đã điền password: {test_data['password']}")
                filled_count += 1
                time.sleep(1)
            except Exception as e:
                print(f"✗ Lỗi điền password: {e}")

        if full_name_input:
            try:
                driver.execute_script("arguments[0].scrollIntoView(true);", full_name_input)
                time.sleep(0.5)
                driver.execute_script("arguments[0].style.border='3px solid orange'", full_name_input)
                full_name_input.clear()
                full_name_input.send_keys(test_data['full_name'])
                print(f"✓ Đã điền full name: {test_data['full_name']}")
                filled_count += 1
                time.sleep(1)
            except Exception as e:
                print(f"✗ Lỗi điền full name: {e}")

        print(f"\n📊 Kết quả: Đã điền {filled_count}/4 fields")

        # Chờ để user xem
        print("\n⏳ Chờ 15 giây để bạn xem kết quả...")
        time.sleep(15)

        return filled_count > 0

    except Exception as e:
        print(f"❌ Lỗi: {e}")
        return False

    finally:
        if driver:
            try:
                driver.quit()
                print("✓ Đã đóng browser")
            except:
                pass

def main():
    """Main function"""
    try:
        success = test_13win_register()

        if success:
            print("\n🎉 TEST THÀNH CÔNG!")
            print("Form đăng ký 13win có thể điền được.")
            print("Tool sẽ hoạt động bình thường.")
        else:
            print("\n❌ TEST THẤT BẠI!")
            print("Cần kiểm tra lại form structure.")

    except KeyboardInterrupt:
        print("\n\nĐã hủy bởi người dùng.")
    except Exception as e:
        print(f"\nLỗi: {e}")

if __name__ == "__main__":
    main()
