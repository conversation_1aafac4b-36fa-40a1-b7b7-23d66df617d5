#!/usr/bin/env python3
"""
Simple test để kiểm tra proxy cache có hoạt động không
"""

import sys
import os
import logging

# Add src to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def test_cache_basic():
    """Test basic cache functionality"""
    print("=== TEST PROXY CACHE ===")
    
    try:
        from proxy_cache import ProxyCache
        
        # Test 1: Tạo cache
        print("1. Tạo ProxyCache...")
        cache = ProxyCache("test_cache.json")
        print("✓ ProxyCache tạo thành công")
        
        # Test 2: Add working proxy
        print("2. Thêm working proxy...")
        test_proxy = {
            'host': '127.0.0.1',
            'port': 8080,
            'type': 'http',
            'response_time': 100
        }
        cache.add_working_proxy(test_proxy)
        print("✓ Thêm working proxy thành công")
        
        # Test 3: Get stats
        print("3. Lấy cache stats...")
        stats = cache.get_cache_stats()
        print(f"✓ Cache stats: {stats}")
        
        # Test 4: Get working proxies
        print("4. Lấy working proxies...")
        working = cache.get_working_proxies()
        print(f"✓ Working proxies: {len(working)}")
        
        # Test 5: Clear cache
        print("5. Clear cache...")
        cache.clear_cache()
        print("✓ Clear cache thành công")
        
        print("\n🎉 TẤT CẢ TEST CACHE THÀNH CÔNG!")
        return True
        
    except Exception as e:
        print(f"❌ Lỗi test cache: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_proxy_manager_basic():
    """Test basic proxy manager functionality"""
    print("\n=== TEST PROXY MANAGER ===")
    
    try:
        from proxy_manager import ProxyManager
        
        # Test 1: Tạo ProxyManager
        print("1. Tạo ProxyManager...")
        manager = ProxyManager()
        print("✓ ProxyManager tạo thành công")
        
        # Test 2: Get stats
        print("2. Lấy proxy stats...")
        stats = manager.get_stats()
        print(f"✓ Proxy stats: {stats}")
        
        # Test 3: Add proxy manually
        print("3. Thêm proxy thủ công...")
        manager.add_proxy("127.0.0.1", 8080)
        print("✓ Thêm proxy thành công")
        
        # Test 4: Get stats again
        print("4. Lấy stats sau khi thêm...")
        stats = manager.get_stats()
        print(f"✓ New stats: {stats}")
        
        print("\n🎉 TẤT CẢ TEST PROXY MANAGER THÀNH CÔNG!")
        return True
        
    except Exception as e:
        print(f"❌ Lỗi test proxy manager: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_ui_basic():
    """Test basic UI functionality"""
    print("\n=== TEST UI BASIC ===")
    
    try:
        # Import without creating UI
        from main_ui import Win13AutoRegistrationUI
        print("✓ Import UI thành công")
        
        print("\n🎉 UI IMPORT THÀNH CÔNG!")
        return True
        
    except Exception as e:
        print(f"❌ Lỗi test UI: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Main test function"""
    print("🧪 KIỂM TRA CÁC COMPONENT CƠ BẢN\n")
    
    # Setup logging
    logging.basicConfig(level=logging.WARNING)  # Reduce log noise
    
    results = []
    
    # Test cache
    results.append(test_cache_basic())
    
    # Test proxy manager
    results.append(test_proxy_manager_basic())
    
    # Test UI import
    results.append(test_ui_basic())
    
    # Summary
    print(f"\n{'='*50}")
    print("📊 KẾT QUẢ TỔNG QUAN:")
    print(f"✓ Thành công: {sum(results)}/{len(results)}")
    print(f"❌ Thất bại: {len(results) - sum(results)}/{len(results)}")
    
    if all(results):
        print("\n🎉 TẤT CẢ TEST THÀNH CÔNG - TOOL SẴN SÀNG!")
        return True
    else:
        print("\n⚠️ CÓ LỖI - CẦN FIX TRƯỚC KHI CHẠY TOOL")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
