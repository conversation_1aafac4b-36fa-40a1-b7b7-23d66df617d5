#!/usr/bin/env python3
"""
Test proxy fallback mechanism
"""

import os
import sys
import time

# Thêm src vào path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from win13_registration_service import Win13RegistrationService
from proxy_manager import ProxyManager
from account_generator import Account<PERSON>enerator

def test_proxy_fallback():
    """Test proxy fallback mechanism"""
    print("🔄 TEST PROXY FALLBACK MECHANISM")
    print("=" * 60)
    
    try:
        # Setup services
        print("🔧 Setting up services...")
        proxy_manager = ProxyManager()
        registration_service = Win13RegistrationService(proxy_manager)
        
        # Test 1: Với proxy hỏng (fake proxy)
        print("\n🧪 Test 1: With bad proxy (should fallback to no proxy)")
        bad_proxy = {
            'host': '192.168.1.999',  # Fake IP
            'port': 8080,
            'type': 'http'
        }
        
        driver1 = registration_service.create_chrome_instance(bad_proxy)
        
        if driver1:
            print("✅ Test 1 PASS: Fallback to no proxy successful")
            
            # Test navigation
            try:
                driver1.get("https://www.google.com")
                title = driver1.title
                print(f"✓ Navigation test: {title}")
            except Exception as nav_error:
                print(f"⚠️ Navigation failed: {nav_error}")
            
            # Cleanup
            registration_service.chrome_manager.cleanup_driver(driver1)
        else:
            print("❌ Test 1 FAIL: Could not create Chrome even without proxy")
        
        # Test 2: Không proxy từ đầu
        print("\n🧪 Test 2: No proxy from start")
        driver2 = registration_service.create_chrome_instance()
        
        if driver2:
            print("✅ Test 2 PASS: No proxy Chrome creation successful")
            
            # Test 13win navigation
            try:
                driver2.get("https://13win16.com")
                time.sleep(3)
                current_url = driver2.current_url
                print(f"✓ 13win navigation: {current_url}")
                
                if "13win" in current_url:
                    print("✅ Successfully accessed 13win without proxy")
                else:
                    print("⚠️ Redirected but not to 13win")
                    
            except Exception as nav_error:
                print(f"❌ 13win navigation failed: {nav_error}")
            
            # Cleanup
            registration_service.chrome_manager.cleanup_driver(driver2)
        else:
            print("❌ Test 2 FAIL: Could not create Chrome without proxy")
        
        # Test 3: Với proxy thật (nếu có)
        print("\n🧪 Test 3: With real proxy (if available)")
        
        if proxy_manager.get_working_proxy_count() > 0:
            real_proxy_info = proxy_manager.get_proxy()
            if real_proxy_info:
                real_proxy = {
                    'host': real_proxy_info.host,
                    'port': real_proxy_info.port,
                    'type': 'http'
                }
                
                driver3 = registration_service.create_chrome_instance(real_proxy)
                
                if driver3:
                    print("✅ Test 3 PASS: Real proxy Chrome creation successful")
                    
                    # Test navigation với proxy
                    try:
                        driver3.get("https://httpbin.org/ip")
                        time.sleep(2)
                        page_source = driver3.page_source
                        if real_proxy['host'] in page_source:
                            print("✅ Proxy is working correctly")
                        else:
                            print("⚠️ Proxy might not be working as expected")
                    except Exception as nav_error:
                        print(f"⚠️ Proxy navigation test failed: {nav_error}")
                    
                    # Cleanup
                    registration_service.chrome_manager.cleanup_driver(driver3)
                    proxy_manager.release_proxy(real_proxy_info)
                else:
                    print("❌ Test 3 FAIL: Could not create Chrome with real proxy")
                    proxy_manager.release_proxy(real_proxy_info)
        else:
            print("⚠️ Test 3 SKIP: No working proxies available")
        
        return True
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        return False
        
    finally:
        # Final cleanup
        try:
            registration_service.chrome_manager.cleanup_all()
            print("✓ All resources cleaned up")
        except:
            pass

def test_13win_with_fallback():
    """Test 13win registration với proxy fallback"""
    print("\n🎯 TEST 13WIN REGISTRATION WITH PROXY FALLBACK")
    print("=" * 60)
    
    try:
        # Setup
        proxy_manager = ProxyManager()
        registration_service = Win13RegistrationService(proxy_manager)
        account_generator = AccountGenerator()
        
        # Generate test account
        account_data = account_generator.generate_complete_account()
        print(f"👤 Test account: {account_data['username']}")
        print(f"📧 Email: {account_data['email']}")
        print(f"👨‍💼 Full name: {account_data['full_name']}")
        
        # Try with proxy first, fallback to no proxy
        proxy_info = None
        if proxy_manager.get_working_proxy_count() > 0:
            proxy_info = proxy_manager.get_proxy()
        
        proxy = None
        if proxy_info:
            proxy = {
                'host': proxy_info.host,
                'port': proxy_info.port,
                'type': 'http'
            }
        
        # Create Chrome with fallback
        driver = registration_service.create_chrome_instance(proxy)
        
        if not driver:
            print("❌ Could not create Chrome instance")
            return False
        
        # Test 13win registration
        print("\n📍 Testing 13win registration...")
        try:
            # Navigate to 13win
            driver.get("https://13win16.com")
            time.sleep(5)
            
            current_url = driver.current_url
            print(f"✓ Current URL: {current_url}")
            
            # Fill form
            if "register" in current_url.lower():
                print("✏️ Filling registration form...")
                fill_success = registration_service.fill_registration_form(driver, account_data)
                
                if fill_success:
                    print("✅ Form filling successful!")
                else:
                    print("⚠️ Form filling had issues")
            else:
                print("⚠️ Not on registration page")
            
            # Keep open for inspection
            print("\n👀 Keeping browser open for 20 seconds...")
            time.sleep(20)
            
        except Exception as reg_error:
            print(f"❌ Registration test failed: {reg_error}")
        
        # Cleanup
        registration_service.chrome_manager.cleanup_driver(driver)
        if proxy_info:
            proxy_manager.release_proxy(proxy_info)
        
        return True
        
    except Exception as e:
        print(f"❌ 13win test failed: {e}")
        return False
        
    finally:
        try:
            registration_service.chrome_manager.cleanup_all()
            print("✓ All resources cleaned up")
        except:
            pass

def main():
    """Main function"""
    try:
        print("🧪 PROXY FALLBACK TEST SUITE")
        print("=" * 60)
        
        # Test 1: Proxy fallback mechanism
        test1_success = test_proxy_fallback()
        
        # Test 2: 13win with fallback
        test2_success = test_13win_with_fallback()
        
        # Summary
        print("\n" + "=" * 60)
        print("📊 TEST SUMMARY:")
        print(f"Proxy fallback mechanism: {'✅ PASS' if test1_success else '❌ FAIL'}")
        print(f"13win with fallback: {'✅ PASS' if test2_success else '❌ FAIL'}")
        
        if test1_success and test2_success:
            print("\n🎉 ALL TESTS PASSED!")
            print("Proxy fallback mechanism is working correctly.")
            print("Tool can handle bad proxies and fallback to direct connection.")
        else:
            print("\n⚠️ SOME TESTS FAILED!")
            print("Please check the proxy configuration.")
            
    except KeyboardInterrupt:
        print("\n\nTest interrupted by user.")
    except Exception as e:
        print(f"\nTest suite failed: {e}")

if __name__ == "__main__":
    main()
