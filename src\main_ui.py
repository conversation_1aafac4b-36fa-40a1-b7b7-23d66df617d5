"""
Giao diện ch<PERSON>h cho tool đăng ký tự động 13win16
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog, scrolledtext
import threading
import logging
import os
from datetime import datetime
import json

from win13_registration_service import Win13RegistrationService
from proxy_manager import ProxyManager
from account_generator import AccountGenerator

class Win13AutoRegistrationUI:
    def __init__(self, root):
        self.root = root
        self.root.title("13Win16 Auto Registration Tool")
        self.root.geometry("1200x800")
        self.root.resizable(True, True)

        # Services
        self.proxy_manager = ProxyManager()
        self.registration_service = Win13RegistrationService(self.proxy_manager)
        self.account_generator = AccountGenerator()

        # Variables
        self.is_running = False
        self.registration_thread = None

        # Setup logging
        self.setup_logging()

        # Create UI
        self.create_widgets()

        # Load saved settings
        self.load_settings()

    def setup_logging(self):
        """Cấu hình logging"""
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler('registration.log', encoding='utf-8'),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger(__name__)

    def create_widgets(self):
        """Tạo giao diện"""
        # Main notebook
        self.notebook = ttk.Notebook(self.root)
        self.notebook.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # Tabs
        self.create_main_tab()
        self.create_proxy_tab()
        self.create_accounts_tab()
        self.create_settings_tab()
        self.create_logs_tab()

    def create_main_tab(self):
        """Tab chính - Đăng ký tài khoản"""
        main_frame = ttk.Frame(self.notebook)
        self.notebook.add(main_frame, text="Đăng ký tài khoản")

        # Control frame
        control_frame = ttk.LabelFrame(main_frame, text="Điều khiển", padding=10)
        control_frame.pack(fill=tk.X, padx=10, pady=5)

        # Số lượng tài khoản
        ttk.Label(control_frame, text="Số lượng tài khoản:").grid(row=0, column=0, sticky=tk.W, padx=5)
        self.num_accounts_var = tk.StringVar(value="5")
        num_accounts_spin = ttk.Spinbox(control_frame, from_=1, to=100, textvariable=self.num_accounts_var, width=10)
        num_accounts_spin.grid(row=0, column=1, padx=5)

        # Số Chrome đồng thời - Giảm để tránh crash
        ttk.Label(control_frame, text="Chrome đồng thời:").grid(row=0, column=2, sticky=tk.W, padx=5)
        self.concurrent_var = tk.StringVar(value="1")  # Giảm từ 3 xuống 1
        concurrent_spin = ttk.Spinbox(control_frame, from_=1, to=3, textvariable=self.concurrent_var, width=10)  # Max 3 thay vì 10
        concurrent_spin.grid(row=0, column=3, padx=5)

        # Buttons
        button_frame = ttk.Frame(control_frame)
        button_frame.grid(row=1, column=0, columnspan=4, pady=10)

        self.start_btn = ttk.Button(button_frame, text="Bắt đầu đăng ký", command=self.start_registration)
        self.start_btn.pack(side=tk.LEFT, padx=5)

        self.stop_btn = ttk.Button(button_frame, text="Dừng", command=self.stop_registration, state=tk.DISABLED)
        self.stop_btn.pack(side=tk.LEFT, padx=5)

        ttk.Button(button_frame, text="Tạo tài khoản mẫu", command=self.generate_sample_account).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="Test Chrome", command=self.test_chrome).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="Lưu kết quả", command=self.save_results).pack(side=tk.LEFT, padx=5)

        # Status frame
        status_frame = ttk.LabelFrame(main_frame, text="Trạng thái", padding=10)
        status_frame.pack(fill=tk.X, padx=10, pady=5)

        # Progress bar
        self.progress_var = tk.DoubleVar()
        self.progress_bar = ttk.Progressbar(status_frame, variable=self.progress_var, maximum=100)
        self.progress_bar.pack(fill=tk.X, pady=5)

        # Status labels
        self.status_label = ttk.Label(status_frame, text="Sẵn sàng")
        self.status_label.pack(anchor=tk.W)

        # Stats frame
        stats_frame = ttk.Frame(status_frame)
        stats_frame.pack(fill=tk.X, pady=5)

        self.total_label = ttk.Label(stats_frame, text="Tổng: 0")
        self.total_label.pack(side=tk.LEFT, padx=10)

        self.success_label = ttk.Label(stats_frame, text="Thành công: 0", foreground="green")
        self.success_label.pack(side=tk.LEFT, padx=10)

        self.failed_label = ttk.Label(stats_frame, text="Thất bại: 0", foreground="red")
        self.failed_label.pack(side=tk.LEFT, padx=10)

        self.rate_label = ttk.Label(stats_frame, text="Tỷ lệ: 0%")
        self.rate_label.pack(side=tk.LEFT, padx=10)

        # Results frame
        results_frame = ttk.LabelFrame(main_frame, text="Kết quả đăng ký", padding=10)
        results_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)

        # Results treeview
        columns = ("STT", "Username", "Password", "Email", "Họ tên", "Trạng thái")
        self.results_tree = ttk.Treeview(results_frame, columns=columns, show="headings", height=15)

        for col in columns:
            self.results_tree.heading(col, text=col)
            if col == "STT":
                self.results_tree.column(col, width=50)
            elif col == "Trạng thái":
                self.results_tree.column(col, width=100)
            else:
                self.results_tree.column(col, width=150)

        # Scrollbars
        v_scrollbar = ttk.Scrollbar(results_frame, orient=tk.VERTICAL, command=self.results_tree.yview)
        h_scrollbar = ttk.Scrollbar(results_frame, orient=tk.HORIZONTAL, command=self.results_tree.xview)
        self.results_tree.configure(yscrollcommand=v_scrollbar.set, xscrollcommand=h_scrollbar.set)

        # Pack treeview and scrollbars
        self.results_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        v_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        h_scrollbar.pack(side=tk.BOTTOM, fill=tk.X)

    def create_proxy_tab(self):
        """Tab quản lý proxy"""
        proxy_frame = ttk.Frame(self.notebook)
        self.notebook.add(proxy_frame, text="Proxy")

        # Control frame
        control_frame = ttk.LabelFrame(proxy_frame, text="Quản lý Proxy", padding=10)
        control_frame.pack(fill=tk.X, padx=10, pady=5)

        # Buttons
        button_frame = ttk.Frame(control_frame)
        button_frame.pack(fill=tk.X)

        ttk.Button(button_frame, text="Load từ file", command=self.load_proxies_from_file).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="Fetch proxy miễn phí", command=self.fetch_free_proxies).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="Test tất cả", command=self.test_all_proxies).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="Xóa tất cả", command=self.clear_all_proxies).pack(side=tk.LEFT, padx=5)

        # Second row of buttons
        button_frame2 = ttk.Frame(control_frame)
        button_frame2.pack(fill=tk.X, pady=5)

        ttk.Button(button_frame2, text="Lưu proxy hoạt động", command=self.save_working_proxies).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame2, text="Lưu tất cả proxy", command=self.save_all_proxies).pack(side=tk.LEFT, padx=5)

        # Add proxy manually
        add_frame = ttk.LabelFrame(proxy_frame, text="Thêm proxy thủ công", padding=10)
        add_frame.pack(fill=tk.X, padx=10, pady=5)

        # Input fields
        input_frame = ttk.Frame(add_frame)
        input_frame.pack(fill=tk.X)

        ttk.Label(input_frame, text="Host:").grid(row=0, column=0, sticky=tk.W, padx=5)
        self.proxy_host_var = tk.StringVar()
        ttk.Entry(input_frame, textvariable=self.proxy_host_var, width=15).grid(row=0, column=1, padx=5)

        ttk.Label(input_frame, text="Port:").grid(row=0, column=2, sticky=tk.W, padx=5)
        self.proxy_port_var = tk.StringVar()
        ttk.Entry(input_frame, textvariable=self.proxy_port_var, width=8).grid(row=0, column=3, padx=5)

        ttk.Label(input_frame, text="Username:").grid(row=0, column=4, sticky=tk.W, padx=5)
        self.proxy_user_var = tk.StringVar()
        ttk.Entry(input_frame, textvariable=self.proxy_user_var, width=12).grid(row=0, column=5, padx=5)

        ttk.Label(input_frame, text="Password:").grid(row=0, column=6, sticky=tk.W, padx=5)
        self.proxy_pass_var = tk.StringVar()
        ttk.Entry(input_frame, textvariable=self.proxy_pass_var, width=12, show="*").grid(row=0, column=7, padx=5)

        ttk.Button(input_frame, text="Thêm", command=self.add_proxy_manually).grid(row=0, column=8, padx=5)

        # Proxy stats
        stats_frame = ttk.LabelFrame(proxy_frame, text="📊 Thống kê Proxy", padding=10)
        stats_frame.pack(fill=tk.X, padx=10, pady=5)

        self.proxy_stats_label = ttk.Label(stats_frame, text="Tổng: 0 | Hoạt động: 0 | Đang dùng: 0 | Khả dụng: 0")
        self.proxy_stats_label.pack()

        # Progress bar cho test proxy
        self.proxy_progress_var = tk.DoubleVar()
        self.proxy_progress_bar = ttk.Progressbar(stats_frame, variable=self.proxy_progress_var, maximum=100)
        self.proxy_progress_bar.pack(fill=tk.X, pady=5)

        # Cache stats
        cache_frame = ttk.LabelFrame(proxy_frame, text="💾 Cache Proxy", padding=10)
        cache_frame.pack(fill=tk.X, padx=10, pady=5)

        self.cache_stats_label = ttk.Label(cache_frame, text="Đang load cache...", font=("Arial", 10))
        self.cache_stats_label.pack()

        # Cache controls
        cache_controls = ttk.Frame(cache_frame)
        cache_controls.pack(fill=tk.X, pady=(5, 0))

        ttk.Button(cache_controls, text="🔄 Refresh Cache",
                  command=self.refresh_cache_stats).pack(side="left", padx=(0, 5))
        ttk.Button(cache_controls, text="🗑️ Clear Cache",
                  command=self.clear_proxy_cache).pack(side="left")

        # Proxy list
        proxy_list_frame = ttk.LabelFrame(proxy_frame, text="Danh sách Proxy", padding=10)
        proxy_list_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)

        # Proxy treeview
        proxy_columns = ("Host", "Port", "Username", "Trạng thái", "Ping", "Đang dùng")
        self.proxy_tree = ttk.Treeview(proxy_list_frame, columns=proxy_columns, show="headings", height=15)

        for col in proxy_columns:
            self.proxy_tree.heading(col, text=col)
            if col in ["Port", "Ping"]:
                self.proxy_tree.column(col, width=80)
            elif col in ["Trạng thái", "Đang dùng"]:
                self.proxy_tree.column(col, width=100)
            else:
                self.proxy_tree.column(col, width=120)

        # Proxy scrollbars
        proxy_v_scrollbar = ttk.Scrollbar(proxy_list_frame, orient=tk.VERTICAL, command=self.proxy_tree.yview)
        proxy_h_scrollbar = ttk.Scrollbar(proxy_list_frame, orient=tk.HORIZONTAL, command=self.proxy_tree.xview)
        self.proxy_tree.configure(yscrollcommand=proxy_v_scrollbar.set, xscrollcommand=proxy_h_scrollbar.set)

        # Pack proxy treeview
        self.proxy_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        proxy_v_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        proxy_h_scrollbar.pack(side=tk.BOTTOM, fill=tk.X)

    def create_accounts_tab(self):
        """Tab hiển thị tài khoản đã tạo"""
        accounts_frame = ttk.Frame(self.notebook)
        self.notebook.add(accounts_frame, text="Tài khoản")

        # Control frame
        control_frame = ttk.LabelFrame(accounts_frame, text="Quản lý tài khoản", padding=10)
        control_frame.pack(fill=tk.X, padx=10, pady=5)

        # Buttons
        button_frame = ttk.Frame(control_frame)
        button_frame.pack(fill=tk.X)

        ttk.Button(button_frame, text="Xuất Excel", command=self.export_accounts_excel).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="Xuất CSV", command=self.export_accounts_csv).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="Copy tài khoản", command=self.copy_selected_account).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="Xóa tài khoản", command=self.delete_selected_account).pack(side=tk.LEFT, padx=5)

        # Search frame
        search_frame = ttk.Frame(control_frame)
        search_frame.pack(fill=tk.X, pady=5)

        ttk.Label(search_frame, text="Tìm kiếm:").pack(side=tk.LEFT, padx=5)
        self.search_var = tk.StringVar()
        search_entry = ttk.Entry(search_frame, textvariable=self.search_var, width=30)
        search_entry.pack(side=tk.LEFT, padx=5)
        search_entry.bind('<KeyRelease>', self.filter_accounts)

        ttk.Button(search_frame, text="Làm mới", command=self.refresh_accounts).pack(side=tk.LEFT, padx=5)

        # Accounts list
        accounts_list_frame = ttk.LabelFrame(accounts_frame, text="Danh sách tài khoản", padding=10)
        accounts_list_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)

        # Accounts treeview
        account_columns = ("STT", "Username", "Password", "Email", "Họ tên", "Điện thoại", "Ngày tạo", "Trạng thái")
        self.accounts_tree = ttk.Treeview(accounts_list_frame, columns=account_columns, show="headings", height=20)

        for col in account_columns:
            self.accounts_tree.heading(col, text=col)
            if col == "STT":
                self.accounts_tree.column(col, width=50)
            elif col in ["Username", "Password"]:
                self.accounts_tree.column(col, width=120)
            elif col == "Email":
                self.accounts_tree.column(col, width=200)
            elif col == "Trạng thái":
                self.accounts_tree.column(col, width=100)
            else:
                self.accounts_tree.column(col, width=150)

        # Accounts scrollbars
        accounts_v_scrollbar = ttk.Scrollbar(accounts_list_frame, orient=tk.VERTICAL, command=self.accounts_tree.yview)
        accounts_h_scrollbar = ttk.Scrollbar(accounts_list_frame, orient=tk.HORIZONTAL, command=self.accounts_tree.xview)
        self.accounts_tree.configure(yscrollcommand=accounts_v_scrollbar.set, xscrollcommand=accounts_h_scrollbar.set)

        # Pack accounts treeview
        self.accounts_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        accounts_v_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        accounts_h_scrollbar.pack(side=tk.BOTTOM, fill=tk.X)

    def create_settings_tab(self):
        """Tab cài đặt"""
        settings_frame = ttk.Frame(self.notebook)
        self.notebook.add(settings_frame, text="Cài đặt")

        # Registration settings
        reg_frame = ttk.LabelFrame(settings_frame, text="Cài đặt đăng ký", padding=10)
        reg_frame.pack(fill=tk.X, padx=10, pady=5)

        # URL đăng ký
        ttk.Label(reg_frame, text="URL đăng ký:").grid(row=0, column=0, sticky=tk.W, padx=5, pady=5)
        self.registration_url_var = tk.StringVar(value="https://13win16.com/register")
        ttk.Entry(reg_frame, textvariable=self.registration_url_var, width=50).grid(row=0, column=1, padx=5, pady=5)

        # Delay settings
        ttk.Label(reg_frame, text="Delay giữa các lần đăng ký (giây):").grid(row=1, column=0, sticky=tk.W, padx=5, pady=5)
        self.delay_var = tk.StringVar(value="2")
        ttk.Spinbox(reg_frame, from_=1, to=10, textvariable=self.delay_var, width=10).grid(row=1, column=1, sticky=tk.W, padx=5, pady=5)

        # Timeout settings
        ttk.Label(reg_frame, text="Timeout (giây):").grid(row=2, column=0, sticky=tk.W, padx=5, pady=5)
        self.timeout_var = tk.StringVar(value="30")
        ttk.Spinbox(reg_frame, from_=10, to=120, textvariable=self.timeout_var, width=10).grid(row=2, column=1, sticky=tk.W, padx=5, pady=5)

        # Browser settings
        browser_frame = ttk.LabelFrame(settings_frame, text="Cài đặt trình duyệt", padding=10)
        browser_frame.pack(fill=tk.X, padx=10, pady=5)

        # Headless mode
        self.headless_var = tk.BooleanVar(value=False)
        ttk.Checkbutton(browser_frame, text="Chạy ẩn (headless)", variable=self.headless_var).grid(row=0, column=0, sticky=tk.W, padx=5, pady=5)

        # User agent
        ttk.Label(browser_frame, text="User Agent:").grid(row=1, column=0, sticky=tk.W, padx=5, pady=5)
        self.user_agent_var = tk.StringVar(value="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36")
        ttk.Entry(browser_frame, textvariable=self.user_agent_var, width=80).grid(row=1, column=1, padx=5, pady=5)

        # Save/Load settings
        settings_btn_frame = ttk.Frame(settings_frame)
        settings_btn_frame.pack(fill=tk.X, padx=10, pady=10)

        ttk.Button(settings_btn_frame, text="Lưu cài đặt", command=self.save_settings).pack(side=tk.LEFT, padx=5)
        ttk.Button(settings_btn_frame, text="Tải cài đặt", command=self.load_settings).pack(side=tk.LEFT, padx=5)
        ttk.Button(settings_btn_frame, text="Reset mặc định", command=self.reset_settings).pack(side=tk.LEFT, padx=5)

    def create_logs_tab(self):
        """Tab hiển thị logs"""
        logs_frame = ttk.Frame(self.notebook)
        self.notebook.add(logs_frame, text="Logs")

        # Control frame
        log_control_frame = ttk.Frame(logs_frame)
        log_control_frame.pack(fill=tk.X, padx=10, pady=5)

        ttk.Button(log_control_frame, text="Xóa logs", command=self.clear_logs).pack(side=tk.LEFT, padx=5)
        ttk.Button(log_control_frame, text="Lưu logs", command=self.save_logs).pack(side=tk.LEFT, padx=5)
        ttk.Button(log_control_frame, text="Làm mới", command=self.refresh_logs).pack(side=tk.LEFT, padx=5)

        # Auto scroll checkbox
        self.auto_scroll_var = tk.BooleanVar(value=True)
        ttk.Checkbutton(log_control_frame, text="Auto scroll", variable=self.auto_scroll_var).pack(side=tk.RIGHT, padx=5)

        # Logs text area
        self.logs_text = scrolledtext.ScrolledText(logs_frame, height=30, width=120)
        self.logs_text.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)

        # Setup log handler để hiển thị logs trong UI
        self.setup_ui_log_handler()

        # Load cache stats khi khởi động
        self.refresh_cache_stats()

    def setup_ui_log_handler(self):
        """Cấu hình log handler để hiển thị logs trong UI"""
        class UILogHandler(logging.Handler):
            def __init__(self, text_widget, auto_scroll_var):
                super().__init__()
                self.text_widget = text_widget
                self.auto_scroll_var = auto_scroll_var

            def emit(self, record):
                try:
                    msg = self.format(record)
                    self.text_widget.insert(tk.END, msg + '\n')
                    if self.auto_scroll_var.get():
                        self.text_widget.see(tk.END)
                except:
                    pass

        # Thêm handler vào root logger
        ui_handler = UILogHandler(self.logs_text, self.auto_scroll_var)
        ui_handler.setFormatter(logging.Formatter('%(asctime)s - %(levelname)s - %(message)s'))
        logging.getLogger().addHandler(ui_handler)

    # Event handlers
    def start_registration(self):
        """Bắt đầu đăng ký tài khoản"""
        if self.is_running:
            messagebox.showwarning("Cảnh báo", "Đăng ký đang chạy!")
            return

        try:
            num_accounts = int(self.num_accounts_var.get())
            max_concurrent = int(self.concurrent_var.get())

            if num_accounts <= 0:
                messagebox.showerror("Lỗi", "Số lượng tài khoản phải lớn hơn 0")
                return

            if max_concurrent <= 0:
                messagebox.showerror("Lỗi", "Số Chrome đồng thời phải lớn hơn 0")
                return

            # Kiểm tra proxy
            if self.proxy_manager.get_working_proxy_count() == 0:
                result = messagebox.askyesno("Cảnh báo",
                    "Không có proxy khả dụng. Bạn có muốn tiếp tục không sử dụng proxy?")
                if not result:
                    return

            # Cập nhật URL đăng ký
            self.registration_service.registration_url = self.registration_url_var.get()

            # Reset kết quả
            self.clear_results()

            # Cập nhật UI
            self.is_running = True
            self.start_btn.config(state=tk.DISABLED)
            self.stop_btn.config(state=tk.NORMAL)
            self.status_label.config(text="Đang đăng ký...")
            self.progress_var.set(0)

            # Chạy trong thread riêng
            self.registration_thread = threading.Thread(
                target=self._run_registration,
                args=(num_accounts, max_concurrent)
            )
            self.registration_thread.start()

        except ValueError:
            messagebox.showerror("Lỗi", "Vui lòng nhập số hợp lệ")

    def _run_registration(self, num_accounts, max_concurrent):
        """Chạy đăng ký trong thread riêng"""
        try:
            def update_callback(result):
                # Cập nhật UI từ thread
                self.root.after(0, self._update_registration_result, result)

            # Bắt đầu đăng ký
            self.registration_service.register_multiple_accounts(
                num_accounts, max_concurrent, update_callback
            )

            # Hoàn thành
            self.root.after(0, self._registration_completed)

        except Exception as e:
            self.logger.error(f"Lỗi trong quá trình đăng ký: {str(e)}")
            self.root.after(0, self._registration_error, str(e))

    def _update_registration_result(self, result):
        """Cập nhật kết quả đăng ký lên UI"""
        try:
            account = result.get('account', {})
            account_index = result.get('account_index', 0)

            # Thêm vào bảng kết quả
            status = "Thành công" if result.get('success', False) else "Thất bại"

            item = self.results_tree.insert("", tk.END, values=(
                account_index + 1,
                account.get('username', 'N/A'),
                account.get('password', 'N/A'),
                account.get('email', 'N/A'),
                account.get('full_name', 'N/A'),
                status
            ))

            # Đổi màu dựa trên trạng thái
            if status == "Thất bại":
                self.results_tree.set(item, "Trạng thái", f"Thất bại: {result.get('error', 'Unknown')}")

            # Cập nhật thống kê
            self._update_stats()

            # Cập nhật progress
            progress = (account_index + 1) / int(self.num_accounts_var.get()) * 100
            self.progress_var.set(progress)

        except Exception as e:
            self.logger.error(f"Lỗi cập nhật UI: {str(e)}")

    def _update_stats(self):
        """Cập nhật thống kê"""
        stats = self.registration_service.get_registration_stats()

        self.total_label.config(text=f"Tổng: {stats['total']}")
        self.success_label.config(text=f"Thành công: {stats['success']}")
        self.failed_label.config(text=f"Thất bại: {stats['failed']}")
        self.rate_label.config(text=f"Tỷ lệ: {stats['success_rate']}%")

    def _registration_completed(self):
        """Xử lý khi đăng ký hoàn thành"""
        self.is_running = False
        self.start_btn.config(state=tk.NORMAL)
        self.stop_btn.config(state=tk.DISABLED)
        self.status_label.config(text="Hoàn thành đăng ký")
        self.progress_var.set(100)

        # Hiển thị thông báo
        stats = self.registration_service.get_registration_stats()
        messagebox.showinfo("Hoàn thành",
            f"Đăng ký hoàn thành!\n"
            f"Tổng: {stats['total']}\n"
            f"Thành công: {stats['success']}\n"
            f"Thất bại: {stats['failed']}\n"
            f"Tỷ lệ thành công: {stats['success_rate']}%"
        )

    def _registration_error(self, error_msg):
        """Xử lý lỗi đăng ký"""
        self.is_running = False
        self.start_btn.config(state=tk.NORMAL)
        self.stop_btn.config(state=tk.DISABLED)
        self.status_label.config(text=f"Lỗi: {error_msg}")

        # Hiển thị lỗi chi tiết với gợi ý khắc phục
        error_details = f"Có lỗi xảy ra: {error_msg}\n\n"

        if "chrome" in error_msg.lower() or "driver" in error_msg.lower():
            error_details += "🔧 Gợi ý khắc phục lỗi Chrome:\n"
            if "winerror 193" in error_msg.lower() or "version" in error_msg.lower():
                error_details += "⚡ QUICK FIX: Chạy 'python quick_fix_chrome.py'\n"
                error_details += "1. Hoặc chạy 'python fix_chromedriver.py'\n"
                error_details += "2. Cập nhật Chrome lên version mới nhất\n"
                error_details += "3. Tắt antivirus tạm thời\n"
            else:
                error_details += "1. Kiểm tra Chrome browser đã cài đặt\n"
                error_details += "2. Tắt antivirus tạm thời\n"
                error_details += "3. Chạy với quyền Administrator\n"
                error_details += "4. Chạy 'python test_chrome.py' để kiểm tra\n"
            error_details += "5. Xem file CHROME_TROUBLESHOOTING.md"
        elif "proxy" in error_msg.lower():
            error_details += "🌐 Gợi ý khắc phục lỗi Proxy:\n"
            error_details += "1. Kiểm tra proxy có hoạt động không\n"
            error_details += "2. Thử không sử dụng proxy\n"
            error_details += "3. Kiểm tra username/password proxy"
        else:
            error_details += "📋 Gợi ý chung:\n"
            error_details += "1. Kiểm tra kết nối internet\n"
            error_details += "2. Kiểm tra URL đăng ký trong tab Cài đặt\n"
            error_details += "3. Xem logs chi tiết trong tab Logs"

        messagebox.showerror("Lỗi", error_details)

    def stop_registration(self):
        """Dừng đăng ký"""
        if not self.is_running:
            return

        result = messagebox.askyesno("Xác nhận", "Bạn có chắc muốn dừng đăng ký?")
        if result:
            self.is_running = False
            self.start_btn.config(state=tk.NORMAL)
            self.stop_btn.config(state=tk.DISABLED)
            self.status_label.config(text="Đã dừng")

            # Terminate thread (không an toàn nhưng cần thiết)
            if self.registration_thread and self.registration_thread.is_alive():
                # Có thể cần implement cách dừng thread an toàn hơn
                pass

    def generate_sample_account(self):
        """Tạo tài khoản mẫu"""
        try:
            account = self.account_generator.generate_complete_account()

            # Hiển thị trong dialog
            info_text = f"""
Tài khoản mẫu:
Username: {account['username']}
Password: {account['password']}
Email: {account['email']}
Họ tên: {account['full_name']}
Điện thoại: {account['phone']}
Ngày sinh: {account['birth_day']}/{account['birth_month']}/{account['birth_year']}
Địa chỉ: {account['address']}, {account['city']}
            """

            messagebox.showinfo("Tài khoản mẫu", info_text)

        except Exception as e:
            messagebox.showerror("Lỗi", f"Không thể tạo tài khoản mẫu: {str(e)}")

    def test_chrome(self):
        """Test Chrome browser"""
        try:
            self.status_label.config(text="Đang test Chrome...")

            def test_worker():
                try:
                    # Test tạo Chrome instance
                    driver = self.registration_service.create_chrome_instance()

                    if driver:
                        # Test điều hướng
                        driver.get("https://www.google.com")
                        title = driver.title
                        driver.quit()

                        self.root.after(0, lambda: self._test_chrome_success(title))
                    else:
                        self.root.after(0, lambda: self._test_chrome_failed("Không thể tạo Chrome instance"))

                except Exception as e:
                    self.root.after(0, lambda: self._test_chrome_failed(str(e)))

            threading.Thread(target=test_worker).start()

        except Exception as e:
            messagebox.showerror("Lỗi", f"Lỗi test Chrome: {str(e)}")

    def _test_chrome_success(self, title):
        """Chrome test thành công"""
        self.status_label.config(text="Sẵn sàng")
        messagebox.showinfo("Test Chrome thành công",
            f"✓ Chrome hoạt động bình thường!\n"
            f"Đã truy cập Google thành công.\n"
            f"Title: {title}\n\n"
            f"Tool sẵn sàng để đăng ký tài khoản."
        )

    def _test_chrome_failed(self, error_msg):
        """Chrome test thất bại"""
        self.status_label.config(text="Lỗi Chrome")

        error_details = f"✗ Test Chrome thất bại!\n\n"
        error_details += f"Lỗi: {error_msg}\n\n"
        error_details += "🔧 Gợi ý khắc phục:\n"
        error_details += "1. Cài đặt Chrome: https://www.google.com/chrome/\n"
        error_details += "2. Tắt antivirus tạm thời\n"
        error_details += "3. Chạy với quyền Administrator\n"
        error_details += "4. Chạy 'python test_chrome.py' để debug\n"
        error_details += "5. Xem CHROME_TROUBLESHOOTING.md"

        messagebox.showerror("Test Chrome thất bại", error_details)

    def save_results(self):
        """Lưu kết quả đăng ký"""
        try:
            filename = filedialog.asksaveasfilename(
                defaultextension=".txt",
                filetypes=[("Text files", "*.txt"), ("All files", "*.*")]
            )

            if filename:
                if self.registration_service.save_results_to_file(filename):
                    messagebox.showinfo("Thành công", f"Đã lưu kết quả vào {filename}")
                else:
                    messagebox.showerror("Lỗi", "Không thể lưu file")

        except Exception as e:
            messagebox.showerror("Lỗi", f"Lỗi lưu file: {str(e)}")

    def clear_results(self):
        """Xóa kết quả hiện tại"""
        for item in self.results_tree.get_children():
            self.results_tree.delete(item)

        self.total_label.config(text="Tổng: 0")
        self.success_label.config(text="Thành công: 0")
        self.failed_label.config(text="Thất bại: 0")
        self.rate_label.config(text="Tỷ lệ: 0%")

    # Proxy management methods
    def load_proxies_from_file(self):
        """Load proxy từ file"""
        try:
            filename = filedialog.askopenfilename(
                title="Chọn file proxy",
                filetypes=[("Text files", "*.txt"), ("All files", "*.*")]
            )

            if filename:
                count = self.proxy_manager.load_proxies_from_file(filename)

                # Hiển thị thông tin chi tiết
                stats = self.proxy_manager.get_stats()
                working_count = len([p for p in self.proxy_manager.proxies if hasattr(p, 'is_working') and p.is_working])

                message = f"Đã load {count} proxy từ file\n\n"
                message += f"Tổng proxy: {stats['total']}\n"
                message += f"Đã test (working): {working_count}\n"
                message += f"Chưa test: {stats['total'] - working_count}"

                messagebox.showinfo("Thành công", message)
                self.update_proxy_list()
                self.update_proxy_stats()

        except Exception as e:
            messagebox.showerror("Lỗi", f"Lỗi load proxy: {str(e)}")

    def fetch_free_proxies(self):
        """Fetch proxy miễn phí"""
        try:
            self.status_label.config(text="Đang fetch proxy miễn phí...")

            def fetch_worker():
                try:
                    from proxy_manager import fetch_free_proxies

                    def progress_callback(message):
                        self.root.after(0, lambda: self.status_label.config(text=message))

                    proxies = fetch_free_proxies(progress_callback)

                    # Thêm vào proxy manager
                    for proxy in proxies:
                        self.proxy_manager.proxies.append(proxy)

                    self.root.after(0, lambda: self._fetch_proxies_completed(len(proxies)))

                except Exception as e:
                    self.root.after(0, lambda: self._fetch_proxies_error(str(e)))

            threading.Thread(target=fetch_worker).start()

        except Exception as e:
            messagebox.showerror("Lỗi", f"Lỗi fetch proxy: {str(e)}")

    def _fetch_proxies_completed(self, count):
        """Hoàn thành fetch proxy"""
        self.status_label.config(text="Sẵn sàng")
        messagebox.showinfo("Thành công", f"Đã fetch {count} proxy miễn phí")
        self.update_proxy_list()

    def _fetch_proxies_error(self, error_msg):
        """Lỗi fetch proxy"""
        self.status_label.config(text="Sẵn sàng")
        messagebox.showerror("Lỗi", f"Lỗi fetch proxy: {error_msg}")

    def test_all_proxies(self):
        """Test tất cả proxy"""
        if not self.proxy_manager.proxies:
            messagebox.showwarning("Cảnh báo", "Không có proxy để test")
            return

        try:
            total_proxies = len(self.proxy_manager.proxies)
            self.status_label.config(text=f"Đang test {total_proxies} proxy...")

            def test_worker():
                try:
                    def progress_callback(tested, total, working):
                        progress_percent = (tested / total) * 100
                        message = f"Test proxy: {tested}/{total} ({progress_percent:.1f}%) - Hoạt động: {working}"
                        self.root.after(0, lambda: self.status_label.config(text=message))
                        self.root.after(0, lambda: self.proxy_progress_var.set(progress_percent))
                        self.root.after(0, lambda: self.update_proxy_stats())
                        self.root.after(0, lambda: self.update_proxy_list())

                    working_count = self.proxy_manager.test_all_proxies(progress_callback=progress_callback)
                    self.root.after(0, lambda: self._test_proxies_completed(working_count))
                except Exception as e:
                    self.root.after(0, lambda: self._test_proxies_error(str(e)))

            threading.Thread(target=test_worker).start()

        except Exception as e:
            messagebox.showerror("Lỗi", f"Lỗi test proxy: {str(e)}")

    def _test_proxies_completed(self, working_count):
        """Hoàn thành test proxy"""
        self.status_label.config(text="Sẵn sàng")
        self.proxy_progress_var.set(100)
        total_count = len(self.proxy_manager.proxies)
        success_rate = (working_count/total_count*100) if total_count > 0 else 0

        messagebox.showinfo("Hoàn thành",
            f"Test proxy hoàn thành!\n"
            f"Tổng: {total_count}\n"
            f"Hoạt động: {working_count}\n"
            f"Tỷ lệ thành công: {success_rate:.1f}%"
        )
        self.update_proxy_list()
        self.update_proxy_stats()

        # Reset progress bar sau 2 giây
        self.root.after(2000, lambda: self.proxy_progress_var.set(0))

    def _test_proxies_error(self, error_msg):
        """Lỗi test proxy"""
        self.status_label.config(text="Sẵn sàng")
        messagebox.showerror("Lỗi", f"Lỗi test proxy: {error_msg}")

    def add_proxy_manually(self):
        """Thêm proxy thủ công"""
        try:
            host = self.proxy_host_var.get().strip()
            port = self.proxy_port_var.get().strip()
            username = self.proxy_user_var.get().strip()
            password = self.proxy_pass_var.get().strip()

            if not host or not port:
                messagebox.showerror("Lỗi", "Vui lòng nhập host và port")
                return

            try:
                port = int(port)
            except ValueError:
                messagebox.showerror("Lỗi", "Port phải là số")
                return

            # Thêm proxy
            self.proxy_manager.add_proxy(
                host, port,
                username if username else None,
                password if password else None
            )

            # Clear input
            self.proxy_host_var.set("")
            self.proxy_port_var.set("")
            self.proxy_user_var.set("")
            self.proxy_pass_var.set("")

            self.update_proxy_list()
            messagebox.showinfo("Thành công", "Đã thêm proxy")

        except Exception as e:
            messagebox.showerror("Lỗi", f"Lỗi thêm proxy: {str(e)}")

    def clear_all_proxies(self):
        """Xóa tất cả proxy"""
        result = messagebox.askyesno("Xác nhận", "Bạn có chắc muốn xóa tất cả proxy?")
        if result:
            self.proxy_manager.clear_all()
            self.update_proxy_list()
            self.update_proxy_stats()
            messagebox.showinfo("Thành công", "Đã xóa tất cả proxy")

    def save_working_proxies(self):
        """Lưu proxy hoạt động"""
        try:
            filename = filedialog.asksaveasfilename(
                defaultextension=".txt",
                filetypes=[("Text files", "*.txt"), ("All files", "*.*")]
            )

            if filename:
                if self.proxy_manager.save_working_proxies(filename):
                    messagebox.showinfo("Thành công", f"Đã lưu proxy vào {filename}")
                else:
                    messagebox.showerror("Lỗi", "Không thể lưu file")

        except Exception as e:
            messagebox.showerror("Lỗi", f"Lỗi lưu proxy: {str(e)}")

    def save_all_proxies(self):
        """Lưu tất cả proxy"""
        try:
            filename = filedialog.asksaveasfilename(
                defaultextension=".txt",
                filetypes=[("Text files", "*.txt"), ("All files", "*.*")]
            )

            if filename:
                if self.proxy_manager.save_all_proxies(filename):
                    messagebox.showinfo("Thành công", f"Đã lưu tất cả proxy vào {filename}")
                else:
                    messagebox.showerror("Lỗi", "Không thể lưu file")

        except Exception as e:
            messagebox.showerror("Lỗi", f"Lỗi lưu tất cả proxy: {str(e)}")

    def update_proxy_list(self):
        """Cập nhật danh sách proxy"""
        # Clear existing items
        for item in self.proxy_tree.get_children():
            self.proxy_tree.delete(item)

        # Add proxies
        for proxy in self.proxy_manager.proxies:
            status = "Hoạt động" if proxy.is_working else "Không hoạt động"
            in_use = "Có" if proxy.in_use else "Không"
            ping = f"{proxy.response_time}ms" if proxy.response_time > 0 else "N/A"

            self.proxy_tree.insert("", tk.END, values=(
                proxy.host,
                proxy.port,
                proxy.username or "N/A",
                status,
                ping,
                in_use
            ))

    def update_proxy_stats(self):
        """Cập nhật thống kê proxy"""
        stats = self.proxy_manager.get_stats()
        self.proxy_stats_label.config(
            text=f"Tổng: {stats['total']} | Hoạt động: {stats['working']} | "
                 f"Đang dùng: {stats['in_use']} | Khả dụng: {stats['available']}"
        )

        # Cập nhật cache stats
        self.refresh_cache_stats()

    # Account management methods
    def export_accounts_excel(self):
        """Xuất tài khoản ra Excel"""
        try:
            import pandas as pd

            if not self.registration_service.registration_results:
                messagebox.showwarning("Cảnh báo", "Không có dữ liệu để xuất")
                return

            # Chuẩn bị dữ liệu
            data = []
            for result in self.registration_service.registration_results:
                if result.get('success', False):
                    account = result.get('account', {})
                    data.append({
                        'Username': account.get('username', ''),
                        'Password': account.get('password', ''),
                        'Email': account.get('email', ''),
                        'Họ tên': account.get('full_name', ''),
                        'Điện thoại': account.get('phone', ''),
                        'Ngày sinh': f"{account.get('birth_day', '')}/{account.get('birth_month', '')}/{account.get('birth_year', '')}",
                        'Địa chỉ': account.get('address', ''),
                        'Thành phố': account.get('city', ''),
                        'Trạng thái': 'Thành công'
                    })

            if not data:
                messagebox.showwarning("Cảnh báo", "Không có tài khoản thành công để xuất")
                return

            # Chọn file
            filename = filedialog.asksaveasfilename(
                defaultextension=".xlsx",
                filetypes=[("Excel files", "*.xlsx"), ("All files", "*.*")]
            )

            if filename:
                df = pd.DataFrame(data)
                df.to_excel(filename, index=False)
                messagebox.showinfo("Thành công", f"Đã xuất {len(data)} tài khoản ra {filename}")

        except ImportError:
            messagebox.showerror("Lỗi", "Cần cài đặt pandas và openpyxl để xuất Excel")
        except Exception as e:
            messagebox.showerror("Lỗi", f"Lỗi xuất Excel: {str(e)}")

    def export_accounts_csv(self):
        """Xuất tài khoản ra CSV"""
        try:
            if not self.registration_service.registration_results:
                messagebox.showwarning("Cảnh báo", "Không có dữ liệu để xuất")
                return

            filename = filedialog.asksaveasfilename(
                defaultextension=".csv",
                filetypes=[("CSV files", "*.csv"), ("All files", "*.*")]
            )

            if filename:
                import csv
                with open(filename, 'w', newline='', encoding='utf-8') as f:
                    writer = csv.writer(f)

                    # Header
                    writer.writerow(['Username', 'Password', 'Email', 'Họ tên', 'Điện thoại', 'Ngày sinh', 'Trạng thái'])

                    # Data
                    count = 0
                    for result in self.registration_service.registration_results:
                        if result.get('success', False):
                            account = result.get('account', {})
                            writer.writerow([
                                account.get('username', ''),
                                account.get('password', ''),
                                account.get('email', ''),
                                account.get('full_name', ''),
                                account.get('phone', ''),
                                f"{account.get('birth_day', '')}/{account.get('birth_month', '')}/{account.get('birth_year', '')}",
                                'Thành công'
                            ])
                            count += 1

                messagebox.showinfo("Thành công", f"Đã xuất {count} tài khoản ra {filename}")

        except Exception as e:
            messagebox.showerror("Lỗi", f"Lỗi xuất CSV: {str(e)}")

    def copy_selected_account(self):
        """Copy tài khoản được chọn"""
        try:
            selection = self.accounts_tree.selection()
            if not selection:
                messagebox.showwarning("Cảnh báo", "Vui lòng chọn tài khoản")
                return

            item = selection[0]
            values = self.accounts_tree.item(item, 'values')

            if len(values) >= 4:
                account_info = f"Username: {values[1]}\nPassword: {values[2]}\nEmail: {values[3]}"

                # Copy to clipboard
                self.root.clipboard_clear()
                self.root.clipboard_append(account_info)

                messagebox.showinfo("Thành công", "Đã copy thông tin tài khoản")

        except Exception as e:
            messagebox.showerror("Lỗi", f"Lỗi copy tài khoản: {str(e)}")

    def delete_selected_account(self):
        """Xóa tài khoản được chọn"""
        try:
            selection = self.accounts_tree.selection()
            if not selection:
                messagebox.showwarning("Cảnh báo", "Vui lòng chọn tài khoản")
                return

            result = messagebox.askyesno("Xác nhận", "Bạn có chắc muốn xóa tài khoản này?")
            if result:
                for item in selection:
                    self.accounts_tree.delete(item)
                messagebox.showinfo("Thành công", "Đã xóa tài khoản")

        except Exception as e:
            messagebox.showerror("Lỗi", f"Lỗi xóa tài khoản: {str(e)}")

    def filter_accounts(self, event=None):
        """Lọc tài khoản theo từ khóa"""
        # TODO: Implement search functionality
        search_term = self.search_var.get().lower()
        if not search_term:
            self.refresh_accounts()
            return

        # Filter accounts based on search term
        # This is a placeholder implementation

    def refresh_accounts(self):
        """Làm mới danh sách tài khoản"""
        # Clear and reload accounts
        for item in self.accounts_tree.get_children():
            self.accounts_tree.delete(item)

        # Add accounts from registration results
        if hasattr(self.registration_service, 'registration_results'):
            for i, result in enumerate(self.registration_service.registration_results):
                account = result.get('account', {})
                status = "Thành công" if result.get('success', False) else "Thất bại"

                self.accounts_tree.insert("", tk.END, values=(
                    i + 1,
                    account.get('username', 'N/A'),
                    account.get('password', 'N/A'),
                    account.get('email', 'N/A'),
                    account.get('full_name', 'N/A'),
                    account.get('phone', 'N/A'),
                    datetime.now().strftime("%Y-%m-%d %H:%M"),
                    status
                ))

    # Settings methods
    def save_settings(self):
        """Lưu cài đặt"""
        try:
            settings = {
                'registration_url': self.registration_url_var.get(),
                'delay': self.delay_var.get(),
                'timeout': self.timeout_var.get(),
                'headless': self.headless_var.get(),
                'user_agent': self.user_agent_var.get(),
                'num_accounts': self.num_accounts_var.get(),
                'concurrent': self.concurrent_var.get()
            }

            with open('settings.json', 'w', encoding='utf-8') as f:
                json.dump(settings, f, indent=2, ensure_ascii=False)

            messagebox.showinfo("Thành công", "Đã lưu cài đặt")

        except Exception as e:
            messagebox.showerror("Lỗi", f"Lỗi lưu cài đặt: {str(e)}")

    def load_settings(self):
        """Tải cài đặt"""
        try:
            if os.path.exists('settings.json'):
                with open('settings.json', 'r', encoding='utf-8') as f:
                    settings = json.load(f)

                self.registration_url_var.set(settings.get('registration_url', 'https://13win16.com/register'))
                self.delay_var.set(settings.get('delay', '2'))
                self.timeout_var.set(settings.get('timeout', '30'))
                self.headless_var.set(settings.get('headless', False))
                self.user_agent_var.set(settings.get('user_agent', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'))
                self.num_accounts_var.set(settings.get('num_accounts', '5'))
                self.concurrent_var.set(settings.get('concurrent', '1'))  # Giảm default từ 3 xuống 1

        except Exception as e:
            self.logger.warning(f"Không thể load cài đặt: {str(e)}")

    def reset_settings(self):
        """Reset cài đặt về mặc định"""
        result = messagebox.askyesno("Xác nhận", "Bạn có chắc muốn reset cài đặt về mặc định?")
        if result:
            self.registration_url_var.set("https://13win16.com/register")
            self.delay_var.set("2")
            self.timeout_var.set("30")
            self.headless_var.set(False)
            self.user_agent_var.set("Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36")
            self.num_accounts_var.set("5")
            self.concurrent_var.set("1")  # Giảm reset default từ 3 xuống 1
            messagebox.showinfo("Thành công", "Đã reset cài đặt")

    def refresh_cache_stats(self):
        """Refresh cache statistics"""
        try:
            stats = self.proxy_manager.proxy_cache.get_cache_stats()

            cache_text = f"Working: {stats['recent_working']}/{stats['total_working']} | "
            cache_text += f"Failed: {stats['total_failed']} | "
            cache_text += f"Updated: {stats['last_updated']}"

            self.cache_stats_label.config(text=cache_text)

        except Exception as e:
            self.cache_stats_label.config(text=f"Lỗi load cache: {str(e)}")
            self.logger.error(f"Lỗi refresh cache stats: {e}")

    def clear_proxy_cache(self):
        """Clear proxy cache"""
        try:
            result = messagebox.askyesno("Xác nhận",
                "Bạn có chắc muốn xóa cache proxy?\n"
                "Điều này sẽ làm mất danh sách proxy đã test.")

            if result:
                self.proxy_manager.proxy_cache.clear_cache()
                self.refresh_cache_stats()
                messagebox.showinfo("Thành công", "Đã xóa cache proxy")

        except Exception as e:
            messagebox.showerror("Lỗi", f"Lỗi xóa cache: {str(e)}")

    # Logs methods
    def clear_logs(self):
        """Xóa logs"""
        result = messagebox.askyesno("Xác nhận", "Bạn có chắc muốn xóa tất cả logs?")
        if result:
            self.logs_text.delete(1.0, tk.END)

    def save_logs(self):
        """Lưu logs"""
        try:
            filename = filedialog.asksaveasfilename(
                defaultextension=".log",
                filetypes=[("Log files", "*.log"), ("Text files", "*.txt"), ("All files", "*.*")]
            )

            if filename:
                with open(filename, 'w', encoding='utf-8') as f:
                    f.write(self.logs_text.get(1.0, tk.END))
                messagebox.showinfo("Thành công", f"Đã lưu logs vào {filename}")

        except Exception as e:
            messagebox.showerror("Lỗi", f"Lỗi lưu logs: {str(e)}")

    def refresh_logs(self):
        """Làm mới logs"""
        # Scroll to end
        self.logs_text.see(tk.END)

# Main function
def main():
    root = tk.Tk()
    Win13AutoRegistrationUI(root)

    # Center window
    root.update_idletasks()
    x = (root.winfo_screenwidth() // 2) - (root.winfo_width() // 2)
    y = (root.winfo_screenheight() // 2) - (root.winfo_height() // 2)
    root.geometry(f"+{x}+{y}")

    root.mainloop()

if __name__ == "__main__":
    main()
