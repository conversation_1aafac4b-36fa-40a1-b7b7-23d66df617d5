#!/usr/bin/env python3
"""
Test script để kiểm tra việc tạo thư mục
"""

import os
import sys
import tempfile
import shutil
import random

# Thêm src vào path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def test_temp_directory():
    """Test tạo thư mục temp"""
    print("=== TEST TẠO THỦ MỤC TEMP ===")
    
    try:
        temp_dir = tempfile.gettempdir()
        print(f"Temp directory: {temp_dir}")
        
        # Test tạo thư mục con
        test_dir = os.path.join(temp_dir, f"test_dir_{random.randint(1000, 9999)}")
        os.makedirs(test_dir, exist_ok=True)
        print(f"✓ Đã tạo: {test_dir}")
        
        # Test quyền write
        test_file = os.path.join(test_dir, "test.txt")
        with open(test_file, 'w') as f:
            f.write("test content")
        print("✓ Có quyền write")
        
        # Cleanup
        shutil.rmtree(test_dir, ignore_errors=True)
        print("✓ Đã xóa test directory")
        
        return True
        
    except Exception as e:
        print(f"✗ Lỗi: {e}")
        return False

def test_current_directory():
    """Test tạo thư mục trong current directory"""
    print("\n=== TEST TẠO THỦ MỤC CURRENT DIR ===")
    
    try:
        current_dir = os.getcwd()
        print(f"Current directory: {current_dir}")
        
        # Test tạo thư mục con
        test_dir = os.path.join(current_dir, f"test_current_{random.randint(1000, 9999)}")
        os.makedirs(test_dir, exist_ok=True)
        print(f"✓ Đã tạo: {test_dir}")
        
        # Test quyền write
        test_file = os.path.join(test_dir, "test.txt")
        with open(test_file, 'w') as f:
            f.write("test content")
        print("✓ Có quyền write")
        
        # Cleanup
        shutil.rmtree(test_dir, ignore_errors=True)
        print("✓ Đã xóa test directory")
        
        return True
        
    except Exception as e:
        print(f"✗ Lỗi: {e}")
        return False

def test_chrome_profiles():
    """Test tạo Chrome profiles"""
    print("\n=== TEST TẠO CHROME PROFILES ===")
    
    try:
        from src.win13_registration_service import Win13RegistrationService
        
        service = Win13RegistrationService()
        
        # Test tạo safe profile path
        test_names = [
            "profile_1_1234",
            "profile with spaces",
            "profile/with/slashes",
            "profile:with:colons",
            "very_long_profile_name_that_exceeds_normal_limits_and_should_be_truncated"
        ]
        
        for name in test_names:
            try:
                safe_path = service._create_safe_profile_path(name)
                print(f"✓ '{name}' → '{safe_path}'")
                
                # Test tạo thư mục
                os.makedirs(safe_path, exist_ok=True)
                
                # Test write
                test_file = os.path.join(safe_path, "test.txt")
                with open(test_file, 'w') as f:
                    f.write("test")
                
                # Cleanup
                shutil.rmtree(safe_path, ignore_errors=True)
                
            except Exception as e:
                print(f"✗ '{name}' lỗi: {e}")
        
        return True
        
    except Exception as e:
        print(f"✗ Lỗi: {e}")
        return False

def test_proxy_extension():
    """Test tạo proxy extension directory"""
    print("\n=== TEST TẠO PROXY EXTENSION ===")
    
    try:
        from src.win13_registration_service import Win13RegistrationService
        from selenium.webdriver.chrome.options import Options
        
        service = Win13RegistrationService()
        options = Options()
        
        # Test proxy data
        proxy = {
            'type': 'http',
            'host': '127.0.0.1',
            'port': '8080',
            'username': 'test',
            'password': 'test'
        }
        
        # Test tạo extension
        extension_dir = service._create_proxy_auth_extension(proxy, options)
        print(f"✓ Đã tạo extension: {extension_dir}")
        
        # Kiểm tra files
        manifest_file = os.path.join(extension_dir, "manifest.json")
        background_file = os.path.join(extension_dir, "background.js")
        
        if os.path.exists(manifest_file):
            print("✓ manifest.json tồn tại")
        else:
            print("✗ manifest.json không tồn tại")
        
        if os.path.exists(background_file):
            print("✓ background.js tồn tại")
        else:
            print("✗ background.js không tồn tại")
        
        # Cleanup
        shutil.rmtree(extension_dir, ignore_errors=True)
        print("✓ Đã cleanup extension")
        
        return True
        
    except Exception as e:
        print(f"✗ Lỗi: {e}")
        return False

def test_permissions():
    """Test quyền truy cập thư mục"""
    print("\n=== TEST QUYỀN TRUY CẬP ===")
    
    try:
        # Test các thư mục khác nhau
        test_dirs = [
            tempfile.gettempdir(),
            os.getcwd(),
            os.path.expanduser("~"),
            "C:\\temp" if os.name == 'nt' else "/tmp"
        ]
        
        for test_dir in test_dirs:
            try:
                if not os.path.exists(test_dir):
                    print(f"⚠ {test_dir} không tồn tại")
                    continue
                
                # Test tạo thư mục con
                sub_dir = os.path.join(test_dir, f"test_{random.randint(1000, 9999)}")
                os.makedirs(sub_dir, exist_ok=True)
                
                # Test write
                test_file = os.path.join(sub_dir, "test.txt")
                with open(test_file, 'w') as f:
                    f.write("test")
                
                print(f"✓ {test_dir} - có quyền read/write")
                
                # Cleanup
                shutil.rmtree(sub_dir, ignore_errors=True)
                
            except Exception as e:
                print(f"✗ {test_dir} - lỗi: {e}")
        
        return True
        
    except Exception as e:
        print(f"✗ Lỗi: {e}")
        return False

def main():
    """Main function"""
    print("🔧 DIRECTORY CREATION TEST TOOL")
    print("=" * 50)
    
    tests = [
        ("Temp Directory", test_temp_directory),
        ("Current Directory", test_current_directory),
        ("Chrome Profiles", test_chrome_profiles),
        ("Proxy Extension", test_proxy_extension),
        ("Permissions", test_permissions)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"✗ {test_name} crashed: {e}")
            results.append((test_name, False))
    
    # Summary
    print("\n" + "=" * 50)
    print("📊 KẾT QUẢ TEST:")
    
    for test_name, result in results:
        status = "✓ PASS" if result else "✗ FAIL"
        print(f"{test_name}: {status}")
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    if passed == total:
        print(f"\n🎉 TẤT CẢ {total} TEST THÀNH CÔNG!")
        print("Việc tạo thư mục hoạt động bình thường.")
    else:
        print(f"\n❌ {total - passed}/{total} TEST THẤT BẠI!")
        print("\n🔧 Gợi ý khắc phục:")
        print("1. Chạy với quyền Administrator")
        print("2. Kiểm tra antivirus có block không")
        print("3. Kiểm tra disk space")
        print("4. Kiểm tra quyền truy cập thư mục")

if __name__ == "__main__":
    main()
