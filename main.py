#!/usr/bin/env python3
"""
13Win16 Auto Registration Tool
Tool đăng ký tự động tài khoản 13win16 với UI
"""

import sys
import os

# Thêm src vào path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

try:
    from src.main_ui import main
    
    if __name__ == "__main__":
        print("=== 13Win16 Auto Registration Tool ===")
        print("Khởi động giao diện...")
        main()
        
except ImportError as e:
    print(f"Lỗi import: {e}")
    print("<PERSON>ui lòng cài đặt các thư viện cần thiết:")
    print("pip install -r requirements.txt")
    input("Nhấn Enter để thoát...")
except Exception as e:
    print(f"Lỗi khởi động: {e}")
    input("Nhấn Enter để thoát...")
