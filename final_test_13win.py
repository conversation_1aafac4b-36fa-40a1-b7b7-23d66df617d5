#!/usr/bin/env python3
"""
Final test for 13win registration with proxy fix
"""

import os
import sys
import time

# Thêm src vào path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from win13_registration_service import Win13RegistrationService
from proxy_manager import ProxyManager
from account_generator import AccountGenerator

def main():
    """Final test"""
    print("🎯 FINAL 13WIN REGISTRATION TEST")
    print("=" * 60)
    
    registration_service = None
    driver = None
    
    try:
        # Setup
        print("🔧 Setting up services...")
        proxy_manager = ProxyManager()
        registration_service = Win13RegistrationService(proxy_manager)
        account_generator = AccountGenerator()
        
        # Generate account
        print("👤 Generating test account...")
        account_data = account_generator.generate_complete_account()
        print(f"✓ Username: {account_data['username']}")
        print(f"✓ Password: {account_data['password']}")
        print(f"✓ Full Name: {account_data['full_name']}")
        print(f"✓ Email: {account_data['email']}")
        
        # Create Chrome (will fallback if proxy fails)
        print("\n🚀 Creating ultra-stable Chrome...")
        driver = registration_service.create_chrome_instance()
        
        if not driver:
            print("❌ Failed to create Chrome instance")
            return
        
        print("✅ Chrome created successfully!")
        
        # Test basic navigation
        print("\n📍 Testing basic navigation...")
        driver.get("https://www.google.com")
        time.sleep(2)
        title = driver.title
        print(f"✓ Google test: {title}")
        
        # Navigate to 13win
        print("\n📍 Navigating to 13win...")
        driver.get("https://13win16.com")
        time.sleep(5)
        
        current_url = driver.current_url
        print(f"✓ Current URL: {current_url}")
        
        # Check if on registration page
        if "register" in current_url.lower():
            print("✅ Successfully reached registration page!")
            
            # Fill form
            print("\n✏️ Filling registration form...")
            try:
                fill_success = registration_service.fill_registration_form(driver, account_data)
                
                if fill_success:
                    print("✅ Form filling successful!")
                    print("\n🎉 ALL TESTS PASSED!")
                    print("Tool is ready for production use!")
                else:
                    print("⚠️ Form filling had some issues")
                    
            except Exception as form_error:
                print(f"❌ Form filling error: {form_error}")
        else:
            print("⚠️ Not on registration page, but navigation successful")
        
        # Keep browser open for inspection
        print("\n👀 Keeping browser open for 30 seconds for manual inspection...")
        print("You can check the form filling results manually.")
        time.sleep(30)
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        
    finally:
        # Cleanup
        if driver and registration_service:
            try:
                registration_service.chrome_manager.cleanup_driver(driver)
                print("\n✓ Driver cleaned up")
            except Exception as cleanup_error:
                print(f"⚠️ Cleanup warning: {cleanup_error}")
        
        if registration_service:
            try:
                registration_service.chrome_manager.cleanup_all()
                print("✓ All resources cleaned up")
            except Exception as cleanup_error:
                print(f"⚠️ Final cleanup warning: {cleanup_error}")
        
        print("\n" + "=" * 60)
        print("🏁 FINAL TEST COMPLETED")
        print("=" * 60)
        
        print("\n💡 If the test was successful, you can now run:")
        print("python main.py")
        print("\nThe tool will:")
        print("✅ Handle proxy failures automatically")
        print("✅ Create ultra-stable Chrome instances")
        print("✅ Fill all 4 form fields correctly")
        print("✅ Submit forms automatically")
        print("✅ Clean up resources properly")

if __name__ == "__main__":
    main()
