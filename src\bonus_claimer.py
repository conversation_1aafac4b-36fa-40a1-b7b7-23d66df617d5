#!/usr/bin/env python3
"""
Bonus Claimer - Tự động nhận thưởng sau khi đăng ký
"""

import time
import logging
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException, NoSuchElementException

class BonusClaimer:
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.event_url = "https://www.13win16.com/home/<USER>"

    def claim_registration_bonus(self, driver):
        """Claim bonus sau khi đăng ký thành công - Enhanced version"""
        try:
            self.logger.info("🎁 Bắt đầu claim bonus đăng ký...")

            # Step 1: Navigate to event page với retry
            self.logger.info(f"📍 Điều hướng đến trang sự kiện: {self.event_url}")

            max_nav_attempts = 3
            for attempt in range(max_nav_attempts):
                try:
                    driver.get(self.event_url)
                    time.sleep(5)  # Tăng thời gian chờ

                    # Verify URL
                    current_url = driver.current_url
                    self.logger.info(f"📍 Current URL: {current_url}")

                    if "event" in current_url.lower():
                        self.logger.info("✅ Đã vào trang sự kiện")
                        break
                    else:
                        self.logger.warning(f"⚠️ URL không đúng, thử lại lần {attempt + 1}")
                        if attempt < max_nav_attempts - 1:
                            time.sleep(3)
                            continue

                except Exception as nav_error:
                    self.logger.warning(f"⚠️ Navigation attempt {attempt + 1} failed: {nav_error}")
                    if attempt == max_nav_attempts - 1:
                        raise nav_error
                    time.sleep(3)

            # Step 2: Wait for page load với timeout dài hơn
            self.logger.info("⏳ Chờ trang sự kiện load hoàn toàn...")
            WebDriverWait(driver, 15).until(
                EC.presence_of_element_located((By.TAG_NAME, "body"))
            )

            # Wait for JavaScript to load
            time.sleep(5)

            # Step 3: Click "Nhiệm Vụ" tab với retry
            mission_clicked = False
            for attempt in range(3):
                if self.click_mission_tab(driver):
                    self.logger.info("✅ Đã click tab Nhiệm Vụ")
                    mission_clicked = True
                    break
                else:
                    self.logger.warning(f"⚠️ Click Nhiệm Vụ attempt {attempt + 1} failed")
                    time.sleep(3)

            if not mission_clicked:
                self.logger.error("❌ Không thể click tab Nhiệm Vụ sau 3 lần thử")
                return False

            # Step 4: Find and claim registration bonus với retry
            for attempt in range(3):
                if self.claim_registration_mission(driver):
                    self.logger.info("🎉 Đã nhận thưởng đăng ký 14K thành công!")
                    return True
                else:
                    self.logger.warning(f"⚠️ Claim bonus attempt {attempt + 1} failed")
                    time.sleep(3)

            self.logger.warning("⚠️ Không thể nhận thưởng đăng ký sau 3 lần thử")
            return False

        except Exception as e:
            self.logger.error(f"❌ Lỗi claim bonus: {str(e)}")
            import traceback
            self.logger.error(f"❌ Traceback: {traceback.format_exc()}")
            return False

    def click_mission_tab(self, driver):
        """Click vào tab Nhiệm Vụ - Enhanced version"""
        try:
            self.logger.info("🔍 Tìm tab Nhiệm Vụ...")

            # Tìm tab "Nhiệm Vụ" bằng nhiều cách
            selectors = [
                # Text-based selectors
                "//span[contains(text(), 'Nhiệm Vụ')]",
                "//div[contains(text(), 'Nhiệm Vụ')]",
                "//button[contains(text(), 'Nhiệm Vụ')]",
                "//a[contains(text(), 'Nhiệm Vụ')]",
                "//li[contains(text(), 'Nhiệm Vụ')]",
                "//p[contains(text(), 'Nhiệm Vụ')]",

                # Class-based selectors
                "//*[contains(@class, 'tab') and contains(text(), 'Nhiệm Vụ')]",
                "//*[contains(@class, 'mission') and contains(text(), 'Nhiệm Vụ')]",
                "//*[contains(@class, 'nav') and contains(text(), 'Nhiệm Vụ')]",
                "//*[contains(@class, 'menu') and contains(text(), 'Nhiệm Vụ')]",

                # Alternative text variations
                "//span[contains(text(), 'Mission')]",
                "//div[contains(text(), 'Mission')]",
                "//span[contains(text(), 'nhiệm vụ')]",
                "//div[contains(text(), 'nhiệm vụ')]",

                # Generic clickable elements with mission text
                "//*[contains(text(), 'Nhiệm Vụ') and (@onclick or @href or contains(@class, 'click'))]"
            ]

            # Try each selector
            for i, selector in enumerate(selectors):
                try:
                    self.logger.info(f"🔍 Thử selector {i+1}/{len(selectors)}: {selector[:50]}...")

                    mission_tab = WebDriverWait(driver, 3).until(
                        EC.element_to_be_clickable((By.XPATH, selector))
                    )

                    # Highlight element
                    driver.execute_script("arguments[0].style.border='3px solid red'", mission_tab)

                    # Scroll to element
                    driver.execute_script("arguments[0].scrollIntoView({behavior: 'smooth', block: 'center'});", mission_tab)
                    time.sleep(1)

                    # Try multiple click methods
                    click_methods = [
                        lambda: mission_tab.click(),
                        lambda: driver.execute_script("arguments[0].click();", mission_tab),
                        lambda: driver.execute_script("arguments[0].dispatchEvent(new MouseEvent('click', {bubbles: true}));", mission_tab)
                    ]

                    for method in click_methods:
                        try:
                            method()
                            self.logger.info("✅ Đã click tab Nhiệm Vụ")
                            time.sleep(3)  # Wait for tab to load
                            return True
                        except Exception as click_error:
                            self.logger.debug(f"Click method failed: {click_error}")
                            continue

                except TimeoutException:
                    continue
                except Exception as e:
                    self.logger.debug(f"Selector {i+1} failed: {e}")
                    continue

            # Fallback: Try to find any clickable element with mission-related text
            self.logger.info("🔄 Fallback: Tìm bất kỳ element nào có text Nhiệm Vụ...")
            try:
                all_elements = driver.find_elements(By.XPATH, "//*[contains(text(), 'Nhiệm Vụ') or contains(text(), 'Mission')]")
                self.logger.info(f"📋 Tìm thấy {len(all_elements)} elements có text Nhiệm Vụ")

                for element in all_elements:
                    try:
                        if element.is_displayed() and element.is_enabled():
                            element_text = element.text
                            self.logger.info(f"🎯 Thử click element: {element_text}")

                            driver.execute_script("arguments[0].scrollIntoView(true);", element)
                            time.sleep(1)
                            driver.execute_script("arguments[0].click();", element)
                            time.sleep(3)

                            self.logger.info("✅ Đã click element Nhiệm Vụ (fallback)")
                            return True
                    except Exception as e:
                        continue

            except Exception as fallback_error:
                self.logger.error(f"❌ Fallback failed: {fallback_error}")

            self.logger.error("❌ Không tìm thấy tab Nhiệm Vụ")
            return False

        except Exception as e:
            self.logger.error(f"❌ Lỗi click mission tab: {e}")
            return False

    def claim_registration_mission(self, driver):
        """Tìm và claim nhiệm vụ đăng ký - Enhanced version"""
        try:
            self.logger.info("🔍 Tìm nhiệm vụ đăng ký tài khoản...")

            # Wait for missions to load
            time.sleep(5)

            # Tìm nhiệm vụ đăng ký bằng text
            registration_keywords = [
                "đăng ký",
                "đăng kí",
                "tài khoản",
                "14k",
                "14000",
                "14,000",
                "registration",
                "register",
                "account",
                "sign up",
                "signup"
            ]

            # Strategy 1: Tìm elements có class mission/task/reward
            mission_selectors = [
                "//*[contains(@class, 'mission')]",
                "//*[contains(@class, 'task')]",
                "//*[contains(@class, 'reward')]",
                "//*[contains(@class, 'event')]",
                "//*[contains(@class, 'bonus')]",
                "//*[contains(@class, 'gift')]",
                "//*[contains(@class, 'prize')]"
            ]

            mission_elements = []
            for selector in mission_selectors:
                try:
                    elements = driver.find_elements(By.XPATH, selector)
                    mission_elements.extend(elements)
                    self.logger.info(f"📋 Tìm thấy {len(elements)} elements với selector: {selector}")
                except:
                    continue

            # Strategy 2: Fallback - tìm tất cả text elements
            if not mission_elements:
                self.logger.info("🔄 Fallback: Tìm tất cả text elements...")
                mission_elements = driver.find_elements(By.XPATH, "//div | //span | //p | //li | //td")

            self.logger.info(f"📋 Tổng cộng {len(mission_elements)} elements để kiểm tra")

            # Check each element for registration mission
            for i, element in enumerate(mission_elements):
                try:
                    element_text = element.text.lower()

                    # Skip empty elements
                    if not element_text.strip():
                        continue

                    # Check if this element contains registration mission keywords
                    matching_keywords = [kw for kw in registration_keywords if kw in element_text]

                    if matching_keywords:
                        self.logger.info(f"🎯 Element {i+1}: Tìm thấy keywords {matching_keywords}")
                        self.logger.info(f"📝 Text: {element_text[:200]}...")

                        # Highlight element
                        try:
                            driver.execute_script("arguments[0].style.border='3px solid green'", element)
                        except:
                            pass

                        # Tìm nút "Nhận" gần element này
                        if self.find_and_click_claim_button(driver, element):
                            return True

                except Exception as e:
                    continue

            # Strategy 3: Tìm trực tiếp nút "Nhận"
            self.logger.info("🔄 Strategy 3: Tìm trực tiếp tất cả nút Nhận...")
            return self.find_all_claim_buttons(driver)

        except Exception as e:
            self.logger.error(f"❌ Lỗi claim registration mission: {e}")
            import traceback
            self.logger.error(f"❌ Traceback: {traceback.format_exc()}")
            return False

    def find_and_click_claim_button(self, driver, mission_element):
        """Tìm nút Nhận gần mission element"""
        try:
            # Tìm trong parent/sibling elements
            parent = mission_element.find_element(By.XPATH, "./..")

            claim_selectors = [
                ".//button[contains(text(), 'Nhận')]",
                ".//span[contains(text(), 'Nhận')]",
                ".//div[contains(text(), 'Nhận')]",
                ".//a[contains(text(), 'Nhận')]",
                ".//*[contains(@class, 'claim') or contains(@class, 'receive')]",
                ".//*[contains(@style, 'green') or contains(@class, 'green')]"
            ]

            for selector in claim_selectors:
                try:
                    claim_button = parent.find_element(By.XPATH, selector)

                    # Check if button is clickable
                    if claim_button.is_enabled() and claim_button.is_displayed():
                        self.logger.info(f"🎯 Tìm thấy nút Nhận: {claim_button.text}")

                        # Scroll and click
                        driver.execute_script("arguments[0].scrollIntoView(true);", claim_button)
                        time.sleep(1)
                        claim_button.click()

                        self.logger.info("🎉 Đã click nút Nhận!")
                        time.sleep(2)

                        # Check for success message
                        return self.check_claim_success(driver)

                except NoSuchElementException:
                    continue

        except Exception as e:
            self.logger.debug(f"Lỗi find claim button: {e}")

        return False

    def find_all_claim_buttons(self, driver):
        """Tìm tất cả nút Nhận và thử click - Enhanced version"""
        try:
            self.logger.info("🔍 Tìm tất cả nút Nhận có thể...")

            claim_selectors = [
                # Text-based selectors
                "//button[contains(text(), 'Nhận')]",
                "//span[contains(text(), 'Nhận')]",
                "//div[contains(text(), 'Nhận')]",
                "//a[contains(text(), 'Nhận')]",
                "//li[contains(text(), 'Nhận')]",
                "//p[contains(text(), 'Nhận')]",

                # English variations
                "//button[contains(text(), 'Claim')]",
                "//span[contains(text(), 'Claim')]",
                "//button[contains(text(), 'Receive')]",
                "//span[contains(text(), 'Receive')]",
                "//button[contains(text(), 'Get')]",
                "//span[contains(text(), 'Get')]",

                # Class-based selectors
                "//*[contains(@class, 'claim')]",
                "//*[contains(@class, 'receive')]",
                "//*[contains(@class, 'get')]",
                "//*[contains(@class, 'btn-claim')]",
                "//*[contains(@class, 'btn-receive')]",
                "//*[contains(@class, 'button-claim')]",

                # Color-based selectors (green buttons)
                "//*[contains(@class, 'green') and (contains(text(), 'Nhận') or contains(@class, 'btn'))]",
                "//*[contains(@style, 'green') and (contains(text(), 'Nhận') or contains(@class, 'btn'))]",

                # Generic button selectors
                "//button[contains(@class, 'btn') and contains(text(), 'Nhận')]",
                "//input[@type='button' and contains(@value, 'Nhận')]",
                "//input[@type='submit' and contains(@value, 'Nhận')]"
            ]

            all_buttons_found = []

            for i, selector in enumerate(claim_selectors):
                try:
                    claim_buttons = driver.find_elements(By.XPATH, selector)
                    if claim_buttons:
                        self.logger.info(f"🔍 Selector {i+1}: Tìm thấy {len(claim_buttons)} nút")
                        all_buttons_found.extend(claim_buttons)

                except Exception as e:
                    continue

            # Remove duplicates
            unique_buttons = []
            for button in all_buttons_found:
                if button not in unique_buttons:
                    unique_buttons.append(button)

            self.logger.info(f"📋 Tổng cộng {len(unique_buttons)} nút unique để thử")

            # Try clicking each button
            for i, button in enumerate(unique_buttons):
                try:
                    if button.is_enabled() and button.is_displayed():
                        button_text = button.text or button.get_attribute('value') or f"Button {i+1}"
                        self.logger.info(f"🎯 Thử click nút {i+1}/{len(unique_buttons)}: {button_text}")

                        # Highlight button
                        try:
                            driver.execute_script("arguments[0].style.border='3px solid blue'", button)
                        except:
                            pass

                        # Scroll to button
                        driver.execute_script("arguments[0].scrollIntoView({behavior: 'smooth', block: 'center'});", button)
                        time.sleep(1)

                        # Try multiple click methods
                        click_methods = [
                            lambda: button.click(),
                            lambda: driver.execute_script("arguments[0].click();", button),
                            lambda: driver.execute_script("arguments[0].dispatchEvent(new MouseEvent('click', {bubbles: true}));", button)
                        ]

                        for method in click_methods:
                            try:
                                method()
                                self.logger.info(f"✅ Đã click nút: {button_text}")
                                time.sleep(3)  # Wait for response

                                # Check success
                                if self.check_claim_success(driver):
                                    return True

                                break  # If click succeeded, don't try other methods
                            except Exception as click_error:
                                self.logger.debug(f"Click method failed: {click_error}")
                                continue

                except Exception as e:
                    self.logger.debug(f"Button {i+1} failed: {e}")
                    continue

            self.logger.warning("⚠️ Không có nút Nhận nào hoạt động")
            return False

        except Exception as e:
            self.logger.error(f"❌ Lỗi find all claim buttons: {e}")
            return False

    def check_claim_success(self, driver):
        """Kiểm tra xem có nhận thưởng thành công không - Enhanced version"""
        try:
            self.logger.info("🔍 Kiểm tra kết quả claim bonus...")

            # Tìm success messages
            success_keywords = [
                "thành công",
                "nhận thưởng",
                "14k",
                "14000",
                "14,000",
                "hoàn thành",
                "success",
                "completed",
                "claimed",
                "received"
            ]

            # Wait for response
            time.sleep(3)

            # Log current URL
            current_url = driver.current_url
            self.logger.info(f"📍 Current URL after claim: {current_url}")

            # Check page text
            page_text = driver.page_source.lower()

            for keyword in success_keywords:
                if keyword in page_text:
                    self.logger.info(f"✅ Phát hiện thành công với keyword: {keyword}")
                    return True

            # Check for any popup/modal/toast
            popup_selectors = [
                "//*[contains(@class, 'modal')]",
                "//*[contains(@class, 'popup')]",
                "//*[contains(@class, 'toast')]",
                "//*[contains(@class, 'alert')]",
                "//*[contains(@class, 'notification')]",
                "//*[contains(@class, 'message')]",
                "//*[contains(@class, 'success')]"
            ]

            for selector in popup_selectors:
                try:
                    popup_elements = driver.find_elements(By.XPATH, selector)
                    for popup in popup_elements:
                        if popup.is_displayed():
                            popup_text = popup.text.lower()
                            if popup_text.strip():
                                self.logger.info(f"📋 Popup text: {popup_text[:200]}...")
                                for keyword in success_keywords:
                                    if keyword in popup_text:
                                        self.logger.info(f"✅ Popup thành công: {keyword}")
                                        return True
                except:
                    continue

            # Check page title
            try:
                page_title = driver.title.lower()
                self.logger.info(f"📋 Page title: {page_title}")
                for keyword in success_keywords:
                    if keyword in page_title:
                        self.logger.info(f"✅ Title thành công: {keyword}")
                        return True
            except:
                pass

            # Check for any visible text changes
            try:
                visible_text = driver.find_element(By.TAG_NAME, "body").text.lower()
                self.logger.info(f"📋 Body text length: {len(visible_text)} chars")

                # Look for specific success patterns
                success_patterns = [
                    "nhận thành công",
                    "claim success",
                    "bonus received",
                    "reward claimed"
                ]

                for pattern in success_patterns:
                    if pattern in visible_text:
                        self.logger.info(f"✅ Pattern thành công: {pattern}")
                        return True

            except:
                pass

            self.logger.info("ℹ️ Không phát hiện message thành công rõ ràng")
            self.logger.info("✅ Giả định thành công (không có lỗi)")
            return True  # Assume success if no clear error

        except Exception as e:
            self.logger.error(f"❌ Lỗi check success: {e}")
            return True  # Assume success if can't check
