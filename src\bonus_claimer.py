#!/usr/bin/env python3
"""
Bonus Claimer - Tự động nhận thưởng sau khi đăng ký
"""

import time
import logging
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException, NoSuchElementException

class BonusClaimer:
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.event_url = "https://www.13win16.com/home/<USER>"
        
    def claim_registration_bonus(self, driver):
        """Claim bonus sau khi đăng ký thành công"""
        try:
            self.logger.info("🎁 Bắt đầu claim bonus đăng ký...")
            
            # Step 1: Navigate to event page
            self.logger.info(f"📍 Điều hướng đến trang sự kiện: {self.event_url}")
            driver.get(self.event_url)
            time.sleep(3)
            
            # Step 2: Wait for page load
            self.logger.info("⏳ Chờ trang sự kiện load...")
            WebDriverWait(driver, 10).until(
                EC.presence_of_element_located((By.TAG_NAME, "body"))
            )
            
            # Step 3: Click "Nhiệm Vụ" tab
            if self.click_mission_tab(driver):
                self.logger.info("✅ Đã click tab Nhiệm Vụ")
                
                # Step 4: Find and claim registration bonus
                if self.claim_registration_mission(driver):
                    self.logger.info("🎉 Đã nhận thưởng đăng ký 14K thành công!")
                    return True
                else:
                    self.logger.warning("⚠️ Không thể nhận thưởng đăng ký")
                    return False
            else:
                self.logger.error("❌ Không thể click tab Nhiệm Vụ")
                return False
                
        except Exception as e:
            self.logger.error(f"❌ Lỗi claim bonus: {str(e)}")
            return False
    
    def click_mission_tab(self, driver):
        """Click vào tab Nhiệm Vụ"""
        try:
            # Tìm tab "Nhiệm Vụ" bằng nhiều cách
            selectors = [
                "//span[contains(text(), 'Nhiệm Vụ')]",
                "//div[contains(text(), 'Nhiệm Vụ')]",
                "//button[contains(text(), 'Nhiệm Vụ')]",
                "//a[contains(text(), 'Nhiệm Vụ')]",
                "//*[contains(@class, 'tab') and contains(text(), 'Nhiệm Vụ')]",
                "//*[contains(@class, 'mission') and contains(text(), 'Nhiệm Vụ')]"
            ]
            
            for selector in selectors:
                try:
                    self.logger.info(f"🔍 Tìm tab Nhiệm Vụ với selector: {selector}")
                    mission_tab = WebDriverWait(driver, 5).until(
                        EC.element_to_be_clickable((By.XPATH, selector))
                    )
                    
                    # Scroll to element
                    driver.execute_script("arguments[0].scrollIntoView(true);", mission_tab)
                    time.sleep(1)
                    
                    # Click
                    mission_tab.click()
                    self.logger.info("✅ Đã click tab Nhiệm Vụ")
                    time.sleep(2)
                    return True
                    
                except TimeoutException:
                    continue
                except Exception as e:
                    self.logger.debug(f"Selector {selector} failed: {e}")
                    continue
            
            self.logger.error("❌ Không tìm thấy tab Nhiệm Vụ")
            return False
            
        except Exception as e:
            self.logger.error(f"❌ Lỗi click mission tab: {e}")
            return False
    
    def claim_registration_mission(self, driver):
        """Tìm và claim nhiệm vụ đăng ký"""
        try:
            self.logger.info("🔍 Tìm nhiệm vụ đăng ký tài khoản...")
            
            # Wait for missions to load
            time.sleep(3)
            
            # Tìm nhiệm vụ đăng ký bằng text
            registration_keywords = [
                "đăng ký",
                "đăng kí", 
                "tài khoản",
                "14k",
                "14000",
                "registration"
            ]
            
            # Tìm tất cả elements có thể chứa nhiệm vụ
            mission_elements = driver.find_elements(By.XPATH, "//*[contains(@class, 'mission') or contains(@class, 'task') or contains(@class, 'reward')]")
            
            if not mission_elements:
                # Fallback: tìm tất cả div/span có text
                mission_elements = driver.find_elements(By.XPATH, "//div | //span | //p")
            
            self.logger.info(f"📋 Tìm thấy {len(mission_elements)} elements để kiểm tra")
            
            for element in mission_elements:
                try:
                    element_text = element.text.lower()
                    
                    # Check if this element contains registration mission keywords
                    if any(keyword in element_text for keyword in registration_keywords):
                        self.logger.info(f"🎯 Tìm thấy nhiệm vụ đăng ký: {element_text[:100]}...")
                        
                        # Tìm nút "Nhận" gần element này
                        if self.find_and_click_claim_button(driver, element):
                            return True
                            
                except Exception as e:
                    continue
            
            # Fallback: tìm tất cả nút "Nhận" màu xanh
            self.logger.info("🔄 Fallback: Tìm tất cả nút Nhận...")
            return self.find_all_claim_buttons(driver)
            
        except Exception as e:
            self.logger.error(f"❌ Lỗi claim registration mission: {e}")
            return False
    
    def find_and_click_claim_button(self, driver, mission_element):
        """Tìm nút Nhận gần mission element"""
        try:
            # Tìm trong parent/sibling elements
            parent = mission_element.find_element(By.XPATH, "./..")
            
            claim_selectors = [
                ".//button[contains(text(), 'Nhận')]",
                ".//span[contains(text(), 'Nhận')]", 
                ".//div[contains(text(), 'Nhận')]",
                ".//a[contains(text(), 'Nhận')]",
                ".//*[contains(@class, 'claim') or contains(@class, 'receive')]",
                ".//*[contains(@style, 'green') or contains(@class, 'green')]"
            ]
            
            for selector in claim_selectors:
                try:
                    claim_button = parent.find_element(By.XPATH, selector)
                    
                    # Check if button is clickable
                    if claim_button.is_enabled() and claim_button.is_displayed():
                        self.logger.info(f"🎯 Tìm thấy nút Nhận: {claim_button.text}")
                        
                        # Scroll and click
                        driver.execute_script("arguments[0].scrollIntoView(true);", claim_button)
                        time.sleep(1)
                        claim_button.click()
                        
                        self.logger.info("🎉 Đã click nút Nhận!")
                        time.sleep(2)
                        
                        # Check for success message
                        return self.check_claim_success(driver)
                        
                except NoSuchElementException:
                    continue
                    
        except Exception as e:
            self.logger.debug(f"Lỗi find claim button: {e}")
            
        return False
    
    def find_all_claim_buttons(self, driver):
        """Tìm tất cả nút Nhận và thử click"""
        try:
            claim_selectors = [
                "//button[contains(text(), 'Nhận')]",
                "//span[contains(text(), 'Nhận')]",
                "//div[contains(text(), 'Nhận')]", 
                "//a[contains(text(), 'Nhận')]",
                "//*[contains(@class, 'claim')]",
                "//*[contains(@class, 'receive')]"
            ]
            
            for selector in claim_selectors:
                try:
                    claim_buttons = driver.find_elements(By.XPATH, selector)
                    self.logger.info(f"🔍 Tìm thấy {len(claim_buttons)} nút với selector: {selector}")
                    
                    for button in claim_buttons:
                        try:
                            if button.is_enabled() and button.is_displayed():
                                button_text = button.text
                                self.logger.info(f"🎯 Thử click nút: {button_text}")
                                
                                # Scroll and click
                                driver.execute_script("arguments[0].scrollIntoView(true);", button)
                                time.sleep(1)
                                button.click()
                                time.sleep(2)
                                
                                # Check success
                                if self.check_claim_success(driver):
                                    return True
                                    
                        except Exception as e:
                            continue
                            
                except Exception as e:
                    continue
            
            return False
            
        except Exception as e:
            self.logger.error(f"❌ Lỗi find all claim buttons: {e}")
            return False
    
    def check_claim_success(self, driver):
        """Kiểm tra xem có nhận thưởng thành công không"""
        try:
            # Tìm success messages
            success_keywords = [
                "thành công",
                "nhận thưởng",
                "14k", 
                "14000",
                "hoàn thành",
                "success"
            ]
            
            # Wait a bit for success message
            time.sleep(2)
            
            # Check page text
            page_text = driver.page_source.lower()
            
            for keyword in success_keywords:
                if keyword in page_text:
                    self.logger.info(f"✅ Phát hiện thành công với keyword: {keyword}")
                    return True
            
            # Check for any popup/modal
            try:
                popup_elements = driver.find_elements(By.XPATH, "//*[contains(@class, 'modal') or contains(@class, 'popup') or contains(@class, 'toast')]")
                for popup in popup_elements:
                    popup_text = popup.text.lower()
                    for keyword in success_keywords:
                        if keyword in popup_text:
                            self.logger.info(f"✅ Popup thành công: {popup_text[:100]}...")
                            return True
            except:
                pass
            
            self.logger.info("ℹ️ Không phát hiện message thành công rõ ràng")
            return True  # Assume success if no error
            
        except Exception as e:
            self.logger.debug(f"Lỗi check success: {e}")
            return True  # Assume success if can't check
