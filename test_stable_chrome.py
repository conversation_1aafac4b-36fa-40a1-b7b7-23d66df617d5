#!/usr/bin/env python3
"""
Test Chrome với config ổn định để tránh crash
"""

import os
import sys
import time

# Thêm src vào path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service

def get_stable_chrome_options():
    """Tạo Chrome options ổn định"""
    options = Options()
    
    # Basic stability options
    options.add_argument("--no-sandbox")
    options.add_argument("--disable-dev-shm-usage")
    options.add_argument("--disable-gpu")
    
    # Memory optimization
    options.add_argument("--memory-pressure-off")
    options.add_argument("--max_old_space_size=2048")
    
    # Process management
    options.add_argument("--process-per-site")
    options.add_argument("--disable-background-networking")
    options.add_argument("--disable-background-timer-throttling")
    
    # Disable unnecessary features
    options.add_argument("--disable-extensions")
    options.add_argument("--disable-plugins")
    options.add_argument("--disable-images")
    options.add_argument("--disable-default-apps")
    options.add_argument("--disable-sync")
    options.add_argument("--disable-translate")
    options.add_argument("--mute-audio")
    options.add_argument("--no-first-run")
    
    # Security and certificates
    options.add_argument("--ignore-certificate-errors")
    options.add_argument("--ignore-ssl-errors")
    options.add_argument("--ignore-certificate-errors-spki-list")
    options.add_argument("--ignore-certificate-errors-ssl")
    
    # DevTools
    options.add_argument("--remote-debugging-port=0")
    
    # Automation
    options.add_argument("--disable-blink-features=AutomationControlled")
    options.add_experimental_option("excludeSwitches", ["enable-automation"])
    options.add_experimental_option('useAutomationExtension', False)
    
    return options

def test_single_chrome():
    """Test một Chrome instance"""
    print("🧪 Test single Chrome instance...")
    
    driver = None
    try:
        options = get_stable_chrome_options()
        
        # ChromeDriver
        chromedriver_path = "chromedriver.exe"
        if os.path.exists(chromedriver_path):
            service = Service(chromedriver_path)
        else:
            print("❌ Không tìm thấy chromedriver.exe")
            return False
        
        # Tạo driver
        driver = webdriver.Chrome(service=service, options=options)
        print("✅ Chrome khởi tạo thành công")
        
        # Test navigation
        driver.get("https://www.google.com")
        print("✅ Navigation thành công")
        
        # Test basic operations
        title = driver.title
        print(f"✅ Page title: {title}")
        
        return True
        
    except Exception as e:
        print(f"❌ Lỗi: {e}")
        return False
        
    finally:
        if driver:
            try:
                driver.quit()
                print("✅ Đã đóng Chrome")
            except:
                pass

def test_multiple_chrome_sequential():
    """Test nhiều Chrome instances tuần tự"""
    print("\n🧪 Test multiple Chrome instances (sequential)...")
    
    success_count = 0
    total_tests = 3
    
    for i in range(total_tests):
        print(f"\n--- Test instance {i+1}/{total_tests} ---")
        
        if test_single_chrome():
            success_count += 1
            print(f"✅ Instance {i+1} thành công")
        else:
            print(f"❌ Instance {i+1} thất bại")
        
        # Delay giữa các test
        if i < total_tests - 1:
            print("⏳ Delay 3 giây...")
            time.sleep(3)
    
    print(f"\n📊 Kết quả: {success_count}/{total_tests} instances thành công")
    return success_count == total_tests

def test_multiple_chrome_concurrent():
    """Test nhiều Chrome instances đồng thời"""
    print("\n🧪 Test multiple Chrome instances (concurrent)...")
    
    drivers = []
    success_count = 0
    total_instances = 2  # Giảm xuống 2 để test
    
    try:
        options = get_stable_chrome_options()
        
        # ChromeDriver
        chromedriver_path = "chromedriver.exe"
        if os.path.exists(chromedriver_path):
            service = Service(chromedriver_path)
        else:
            print("❌ Không tìm thấy chromedriver.exe")
            return False
        
        # Tạo nhiều instances với delay
        for i in range(total_instances):
            try:
                print(f"🚀 Tạo Chrome instance {i+1}...")
                
                # Delay giữa các instances
                if i > 0:
                    delay = 3
                    print(f"⏳ Delay {delay}s...")
                    time.sleep(delay)
                
                driver = webdriver.Chrome(service=service, options=options)
                drivers.append(driver)
                print(f"✅ Instance {i+1} khởi tạo thành công")
                
                # Test navigation
                driver.get("https://www.google.com")
                print(f"✅ Instance {i+1} navigation thành công")
                
                success_count += 1
                
            except Exception as e:
                print(f"❌ Instance {i+1} thất bại: {e}")
        
        print(f"\n📊 Đã tạo {success_count}/{total_instances} instances thành công")
        
        # Chờ một chút
        if success_count > 0:
            print("⏳ Chờ 5 giây để test stability...")
            time.sleep(5)
        
        return success_count == total_instances
        
    except Exception as e:
        print(f"❌ Lỗi tổng thể: {e}")
        return False
        
    finally:
        # Đóng tất cả drivers
        for i, driver in enumerate(drivers):
            try:
                driver.quit()
                print(f"✅ Đã đóng instance {i+1}")
            except:
                pass

def main():
    """Main function"""
    print("🔧 STABLE CHROME TEST TOOL")
    print("=" * 50)
    
    try:
        # Test 1: Single instance
        test1_success = test_single_chrome()
        
        # Test 2: Multiple sequential
        test2_success = test_multiple_chrome_sequential()
        
        # Test 3: Multiple concurrent
        test3_success = test_multiple_chrome_concurrent()
        
        # Summary
        print("\n" + "=" * 50)
        print("📊 KẾT QUẢ TỔNG QUAN:")
        print(f"Single instance: {'✅ PASS' if test1_success else '❌ FAIL'}")
        print(f"Multiple sequential: {'✅ PASS' if test2_success else '❌ FAIL'}")
        print(f"Multiple concurrent: {'✅ PASS' if test3_success else '❌ FAIL'}")
        
        if test1_success and test2_success and test3_success:
            print("\n🎉 TẤT CẢ TEST THÀNH CÔNG!")
            print("Chrome config ổn định, có thể chạy tool với nhiều instances.")
            print("\n💡 Khuyến nghị:")
            print("- Sử dụng 1-2 Chrome instances đồng thời")
            print("- Delay 3-5 giây giữa các instances")
            print("- Monitor memory usage")
        else:
            print("\n⚠️ MỘT SỐ TEST THẤT BẠI!")
            print("Khuyến nghị chỉ chạy 1 Chrome instance tại một thời điểm.")
            
    except KeyboardInterrupt:
        print("\n\nĐã hủy bởi người dùng.")
    except Exception as e:
        print(f"\nLỗi: {e}")

if __name__ == "__main__":
    main()
