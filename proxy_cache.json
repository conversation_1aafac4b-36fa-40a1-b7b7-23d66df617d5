{"working_proxies": [{"key": "**************:7777", "host": "**************", "port": 7777, "type": "http", "username": null, "password": null, "first_tested": "2025-05-29T15:46:40.034738", "last_tested": "2025-05-29T15:54:28.802516", "success_count": 3, "response_time": 975}, {"key": "*************:9094", "host": "*************", "port": 9094, "type": "http", "username": null, "password": null, "first_tested": "2025-05-29T15:46:40.659037", "last_tested": "2025-05-29T15:54:26.686441", "success_count": 4, "response_time": 1598}, {"key": "*************:8081", "host": "*************", "port": 8081, "type": "http", "username": null, "password": null, "first_tested": "2025-05-29T15:46:40.824362", "last_tested": "2025-05-29T15:54:29.490347", "success_count": 4, "response_time": 1150}, {"key": "************:80", "host": "************", "port": 80, "type": "http", "username": null, "password": null, "first_tested": "2025-05-29T15:46:41.136145", "last_tested": "2025-05-29T15:46:41.136145", "success_count": 1, "response_time": 2089}, {"key": "*************:9094", "host": "*************", "port": 9094, "type": "http", "username": null, "password": null, "first_tested": "2025-05-29T15:46:41.142160", "last_tested": "2025-05-29T15:54:25.564137", "success_count": 4, "response_time": 2098}, {"key": "*************:4002", "host": "*************", "port": 4002, "type": "http", "username": null, "password": null, "first_tested": "2025-05-29T15:46:41.180775", "last_tested": "2025-05-29T15:50:28.870965", "success_count": 2, "response_time": 2142}, {"key": "*************:10801", "host": "*************", "port": 10801, "type": "http", "username": null, "password": null, "first_tested": "2025-05-29T15:46:41.184758", "last_tested": "2025-05-29T15:50:26.757058", "success_count": 2, "response_time": 2150}, {"key": "**************:8080", "host": "**************", "port": 8080, "type": "http", "username": null, "password": null, "first_tested": "2025-05-29T15:50:25.479332", "last_tested": "2025-05-29T15:54:24.850847", "success_count": 2, "response_time": 840}, {"key": "*************:18080", "host": "*************", "port": 18080, "type": "http", "username": null, "password": null, "first_tested": "2025-05-29T15:50:25.666020", "last_tested": "2025-05-29T15:54:28.208601", "success_count": 3, "response_time": 1021}, {"key": "**************:3128", "host": "**************", "port": 3128, "type": "http", "username": null, "password": null, "first_tested": "2025-05-29T15:50:25.852444", "last_tested": "2025-05-29T15:54:24.405233", "success_count": 2, "response_time": 1035}, {"key": "*************:8080", "host": "*************", "port": 8080, "type": "http", "username": null, "password": null, "first_tested": "2025-05-29T15:50:26.627338", "last_tested": "2025-05-29T15:54:31.361445", "success_count": 3, "response_time": 1123}, {"key": "*************:202", "host": "*************", "port": 202, "type": "http", "username": null, "password": null, "first_tested": "2025-05-29T15:50:26.777058", "last_tested": "2025-05-29T15:50:26.777058", "success_count": 1, "response_time": 2158}, {"key": "************:45", "host": "************", "port": 45, "type": "http", "username": null, "password": null, "first_tested": "2025-05-29T15:50:27.168203", "last_tested": "2025-05-29T15:50:27.168203", "success_count": 1, "response_time": 2553}, {"key": "**************:1080", "host": "**************", "port": 1080, "type": "http", "username": null, "password": null, "first_tested": "2025-05-29T15:50:27.245434", "last_tested": "2025-05-29T15:50:27.245434", "success_count": 1, "response_time": 2619}, {"key": "*************:9098", "host": "*************", "port": 9098, "type": "http", "username": null, "password": null, "first_tested": "2025-05-29T15:50:27.360123", "last_tested": "2025-05-29T15:50:27.360123", "success_count": 1, "response_time": 2724}, {"key": "************:8080", "host": "************", "port": 8080, "type": "http", "username": null, "password": null, "first_tested": "2025-05-29T15:50:27.373331", "last_tested": "2025-05-29T15:50:27.373331", "success_count": 1, "response_time": 2747}, {"key": "*************:8888", "host": "*************", "port": 8888, "type": "http", "username": null, "password": null, "first_tested": "2025-05-29T15:50:27.633030", "last_tested": "2025-05-29T15:50:27.633030", "success_count": 1, "response_time": 3023}, {"key": "************:138", "host": "************", "port": 138, "type": "http", "username": null, "password": null, "first_tested": "2025-05-29T15:50:28.197335", "last_tested": "2025-05-29T15:50:28.197335", "success_count": 1, "response_time": 3556}, {"key": "************:8081", "host": "************", "port": 8081, "type": "http", "username": null, "password": null, "first_tested": "2025-05-29T15:50:28.331169", "last_tested": "2025-05-29T15:50:28.331169", "success_count": 1, "response_time": 2634}, {"key": "************:8880", "host": "************", "port": 8880, "type": "http", "username": null, "password": null, "first_tested": "2025-05-29T15:50:28.793477", "last_tested": "2025-05-29T15:54:23.959447", "success_count": 2, "response_time": 1524}, {"key": "************:10024", "host": "************", "port": 10024, "type": "http", "username": null, "password": null, "first_tested": "2025-05-29T15:50:28.840754", "last_tested": "2025-05-29T15:54:24.358233", "success_count": 2, "response_time": 1242}, {"key": "**************:80", "host": "**************", "port": 80, "type": "http", "username": null, "password": null, "first_tested": "2025-05-29T15:50:29.459455", "last_tested": "2025-05-29T15:50:29.459455", "success_count": 1, "response_time": 604}, {"key": "************:8001", "host": "************", "port": 8001, "type": "http", "username": null, "password": null, "first_tested": "2025-05-29T15:50:29.566768", "last_tested": "2025-05-29T15:50:29.566768", "success_count": 1, "response_time": 2181}, {"key": "**********:35", "host": "**********", "port": 35, "type": "http", "username": null, "password": null, "first_tested": "2025-05-29T15:50:30.664981", "last_tested": "2025-05-29T15:50:30.664981", "success_count": 1, "response_time": 2437}, {"key": "**************:8080", "host": "**************", "port": 8080, "type": "http", "username": null, "password": null, "first_tested": "2025-05-29T15:50:31.775551", "last_tested": "2025-05-29T15:54:28.961204", "success_count": 2, "response_time": 7145}, {"key": "*************:12000", "host": "*************", "port": 12000, "type": "http", "username": null, "password": null, "first_tested": "2025-05-29T15:50:32.837426", "last_tested": "2025-05-29T15:54:24.199175", "success_count": 2, "response_time": 2148}, {"key": "**************:80", "host": "**************", "port": 80, "type": "http", "username": null, "password": null, "first_tested": "2025-05-29T15:50:32.964033", "last_tested": "2025-05-29T15:50:32.964033", "success_count": 1, "response_time": 711}, {"key": "************:80", "host": "************", "port": 80, "type": "http", "username": null, "password": null, "first_tested": "2025-05-29T15:50:33.249304", "last_tested": "2025-05-29T15:54:26.756402", "success_count": 3, "response_time": 8604}], "failed_proxies": [{"key": "*************:8000", "host": "*************", "port": 8000, "first_failed": "2025-05-29T15:46:39.185781", "last_failed": "2025-05-29T15:54:32.325520", "fail_count": 3, "last_error": "HTTPConnectionPool(host='*************', port=8000): Max retries exceeded with url: http://httpbin.org/ip (Caused by ProxyError('Unable to connect to proxy', ConnectTimeoutError(<urllib3.connection.HTTPConnection object at 0x000001603EE81510>, 'Connection to ************* timed out. (connect timeout=10)')))"}, {"key": "**************:8080", "host": "**************", "port": 8080, "first_failed": "2025-05-29T15:46:39.273411", "last_failed": "2025-05-29T15:54:25.774008", "fail_count": 2, "last_error": "HTTPConnectionPool(host='**************', port=8080): Max retries exceeded with url: http://httpbin.org/ip (Caused by ProxyError('Unable to connect to proxy', RemoteDisconnected('Remote end closed connection without response')))"}, {"key": "***********:80", "host": "***********", "port": 80, "first_failed": "2025-05-29T15:46:39.456576", "last_failed": "2025-05-29T15:54:22.469762", "fail_count": 3, "last_error": "HTTPConnectionPool(host='***********', port=80): Max retries exceeded with url: http://httpbin.org/ip (Caused by ProxyError('Unable to connect to proxy', ConnectionResetError(10054, 'An existing connection was forcibly closed by the remote host', None, 10054, None)))"}, {"key": "*************:8000", "host": "*************", "port": 8000, "first_failed": "2025-05-29T15:46:39.630145", "last_failed": "2025-05-29T15:54:23.170148", "fail_count": 3, "last_error": "HTTPConnectionPool(host='*************', port=8000): Max retries exceeded with url: http://httpbin.org/ip (Caused by ProxyError('Unable to connect to proxy', RemoteDisconnected('Remote end closed connection without response')))"}, {"key": "************:8001", "host": "************", "port": 8001, "first_failed": "2025-05-29T15:46:39.768580", "last_failed": "2025-05-29T15:54:33.692349", "fail_count": 3, "last_error": "HTTPConnectionPool(host='************', port=8001): Max retries exceeded with url: http://httpbin.org/ip (Caused by ProxyError('Unable to connect to proxy', ConnectTimeoutError(<urllib3.connection.HTTPConnection object at 0x000001603EED00D0>, 'Connection to ************ timed out. (connect timeout=10)')))"}, {"key": "************:888", "host": "************", "port": 888, "first_failed": "2025-05-29T15:46:40.298007", "last_failed": "2025-05-29T15:54:32.045549", "fail_count": 3, "last_error": "HTTPConnectionPool(host='************', port=888): Max retries exceeded with url: http://httpbin.org/ip (Caused by ProxyError('Unable to connect to proxy', ConnectTimeoutError(<urllib3.connection.HTTPConnection object at 0x000001603EF50190>, 'Connection to ************ timed out. (connect timeout=10)')))"}, {"key": "************:8002", "host": "************", "port": 8002, "first_failed": "2025-05-29T15:46:41.032643", "last_failed": "2025-05-29T15:54:39.021328", "fail_count": 3, "last_error": "HTTPConnectionPool(host='************', port=8002): Max retries exceeded with url: http://httpbin.org/ip (Caused by ProxyError('Unable to connect to proxy', ConnectTimeoutError(<urllib3.connection.HTTPConnection object at 0x000001603EF1F010>, 'Connection to ************ timed out. (connect timeout=10)')))"}, {"key": "**************:3128", "host": "**************", "port": 3128, "first_failed": "2025-05-29T15:46:43.307498", "last_failed": "2025-05-29T15:46:43.307498", "fail_count": 1, "last_error": "HTTPConnectionPool(host='**************', port=3128): Max retries exceeded with url: http://httpbin.org/ip (Caused by ProxyError('Unable to connect to proxy', ConnectionResetError(10054, 'An existing connection was forcibly closed by the remote host', None, 10054, None)))"}, {"key": "************:8080", "host": "************", "port": 8080, "first_failed": "2025-05-29T15:46:46.799136", "last_failed": "2025-05-29T15:54:34.068580", "fail_count": 3, "last_error": "HTTPConnectionPool(host='************', port=8080): Max retries exceeded with url: http://httpbin.org/ip (Caused by ProxyError('Unable to connect to proxy', ConnectTimeoutError(<urllib3.connection.HTTPConnection object at 0x000001603EED2510>, 'Connection to ************ timed out. (connect timeout=10)')))"}, {"key": "************:8004", "host": "************", "port": 8004, "first_failed": "2025-05-29T15:46:47.764991", "last_failed": "2025-05-29T15:54:35.815570", "fail_count": 3, "last_error": "HTTPConnectionPool(host='************', port=8004): Max retries exceeded with url: http://httpbin.org/ip (Caused by ProxyError('Unable to connect to proxy', ConnectTimeoutError(<urllib3.connection.HTTPConnection object at 0x000001603EEEDE90>, 'Connection to ************ timed out. (connect timeout=10)')))"}, {"key": "*************:80", "host": "*************", "port": 80, "first_failed": "2025-05-29T15:46:49.023476", "last_failed": "2025-05-29T15:54:32.046549", "fail_count": 3, "last_error": "HTTPConnectionPool(host='*************', port=80): Max retries exceeded with url: http://httpbin.org/ip (Caused by ProxyError('Unable to connect to proxy', ConnectTimeoutError(<urllib3.connection.HTTPConnection object at 0x000001603EF45E50>, 'Connection to ************* timed out. (connect timeout=10)')))"}, {"key": "**************:81", "host": "**************", "port": 81, "first_failed": "2025-05-29T15:46:49.024476", "last_failed": "2025-05-29T15:54:32.046549", "fail_count": 3, "last_error": "HTTPConnectionPool(host='**************', port=81): Max retries exceeded with url: http://httpbin.org/ip (Caused by ProxyError('Unable to connect to proxy', ConnectTimeoutError(<urllib3.connection.HTTPConnection object at 0x000001603EF33DD0>, 'Connection to ************** timed out. (connect timeout=10)')))"}, {"key": "*************:11", "host": "*************", "port": 11, "first_failed": "2025-05-29T15:46:49.037939", "last_failed": "2025-05-29T15:54:32.061559", "fail_count": 3, "last_error": "HTTPConnectionPool(host='*************', port=11): Max retries exceeded with url: http://httpbin.org/ip (Caused by ProxyError('Unable to connect to proxy', ConnectTimeoutError(<urllib3.connection.HTTPConnection object at 0x000001603F021D90>, 'Connection to ************* timed out. (connect timeout=10)')))"}, {"key": "*************:18080", "host": "*************", "port": 18080, "first_failed": "2025-05-29T15:46:49.038949", "last_failed": "2025-05-29T15:54:32.045549", "fail_count": 3, "last_error": "HTTPConnectionPool(host='*************', port=18080): Max retries exceeded with url: http://httpbin.org/ip (Caused by ProxyError('Unable to connect to proxy', ConnectTimeoutError(<urllib3.connection.HTTPConnection object at 0x000001603EF471D0>, 'Connection to ************* timed out. (connect timeout=10)')))"}, {"key": "*************:80", "host": "*************", "port": 80, "first_failed": "2025-05-29T15:46:49.039947", "last_failed": "2025-05-29T15:54:32.046549", "fail_count": 3, "last_error": "HTTPConnectionPool(host='*************', port=80): Max retries exceeded with url: http://httpbin.org/ip (Caused by ProxyError('Unable to connect to proxy', ConnectTimeoutError(<urllib3.connection.HTTPConnection object at 0x000001603EFA5E50>, 'Connection to ************* timed out. (connect timeout=10)')))"}, {"key": "*************:31281", "host": "*************", "port": 31281, "first_failed": "2025-05-29T15:46:49.039947", "last_failed": "2025-05-29T15:54:32.046549", "fail_count": 3, "last_error": "HTTPConnectionPool(host='*************', port=31281): Max retries exceeded with url: http://httpbin.org/ip (Caused by ProxyError('Unable to connect to proxy', ConnectTimeoutError(<urllib3.connection.HTTPConnection object at 0x000001603EF6C6D0>, 'Connection to ************* timed out. (connect timeout=10)')))"}, {"key": "*************:1080", "host": "*************", "port": 1080, "first_failed": "2025-05-29T15:46:49.040943", "last_failed": "2025-05-29T15:54:32.046549", "fail_count": 3, "last_error": "HTTPConnectionPool(host='*************', port=1080): Max retries exceeded with url: http://httpbin.org/ip (Caused by ProxyError('Unable to connect to proxy', ConnectTimeoutError(<urllib3.connection.HTTPConnection object at 0x000001603EF8CA10>, 'Connection to ************* timed out. (connect timeout=10)')))"}, {"key": "*************:4002", "host": "*************", "port": 4002, "first_failed": "2025-05-29T15:46:49.040943", "last_failed": "2025-05-29T15:54:32.046549", "fail_count": 3, "last_error": "HTTPConnectionPool(host='*************', port=4002): Max retries exceeded with url: http://httpbin.org/ip (Caused by ProxyError('Unable to connect to proxy', ConnectTimeoutError(<urllib3.connection.HTTPConnection object at 0x000001603EF5CE90>, 'Connection to ************* timed out. (connect timeout=10)')))"}, {"key": "***********:9443", "host": "***********", "port": 9443, "first_failed": "2025-05-29T15:46:49.045940", "last_failed": "2025-05-29T15:54:32.046549", "fail_count": 3, "last_error": "HTTPConnectionPool(host='***********', port=9443): Max retries exceeded with url: http://httpbin.org/ip (Caused by ProxyError('Unable to connect to proxy', ConnectTimeoutError(<urllib3.connection.HTTPConnection object at 0x000001603EF8EE50>, 'Connection to *********** timed out. (connect timeout=10)')))"}, {"key": "************:3127", "host": "************", "port": 3127, "first_failed": "2025-05-29T15:46:49.046938", "last_failed": "2025-05-29T15:54:32.060559", "fail_count": 3, "last_error": "HTTPConnectionPool(host='************', port=3127): Max retries exceeded with url: http://httpbin.org/ip (Caused by ProxyError('Unable to connect to proxy', ConnectTimeoutError(<urllib3.connection.HTTPConnection object at 0x000001603F022E50>, 'Connection to ************ timed out. (connect timeout=10)')))"}, {"key": "*************:6666", "host": "*************", "port": 6666, "first_failed": "2025-05-29T15:46:49.053200", "last_failed": "2025-05-29T15:54:33.973492", "fail_count": 3, "last_error": "HTTPConnectionPool(host='*************', port=6666): Max retries exceeded with url: http://httpbin.org/ip (Caused by ProxyError('Unable to connect to proxy', ConnectTimeoutError(<urllib3.connection.HTTPConnection object at 0x00000160375D5590>, 'Connection to ************* timed out. (connect timeout=10)')))"}, {"key": "*************:999", "host": "*************", "port": 999, "first_failed": "2025-05-29T15:46:49.053200", "last_failed": "2025-05-29T15:54:32.593998", "fail_count": 3, "last_error": "HTTPConnectionPool(host='*************', port=999): Max retries exceeded with url: http://httpbin.org/ip (Caused by ProxyError('Unable to connect to proxy', ConnectTimeoutError(<urllib3.connection.HTTPConnection object at 0x000001603EF94CD0>, 'Connection to ************* timed out. (connect timeout=10)')))"}, {"key": "************:31433", "host": "************", "port": 31433, "first_failed": "2025-05-29T15:46:49.054175", "last_failed": "2025-05-29T15:54:32.499323", "fail_count": 3, "last_error": "HTTPConnectionPool(host='************', port=31433): Max retries exceeded with url: http://httpbin.org/ip (Caused by ProxyError('Unable to connect to proxy', ConnectTimeoutError(<urllib3.connection.HTTPConnection object at 0x000001603EEB3F90>, 'Connection to ************ timed out. (connect timeout=10)')))"}, {"key": "*************:9091", "host": "*************", "port": 9091, "first_failed": "2025-05-29T15:46:49.054175", "last_failed": "2025-05-29T15:54:34.413201", "fail_count": 3, "last_error": "HTTPConnectionPool(host='*************', port=9091): Max retries exceeded with url: http://httpbin.org/ip (Caused by ProxyError('Unable to connect to proxy', ConnectTimeoutError(<urllib3.connection.HTTPConnection object at 0x000001603EEA74D0>, 'Connection to ************* timed out. (connect timeout=10)')))"}, {"key": "*************:8080", "host": "*************", "port": 8080, "first_failed": "2025-05-29T15:46:49.055173", "last_failed": "2025-05-29T15:54:32.217345", "fail_count": 3, "last_error": "HTTPConnectionPool(host='*************', port=8080): Max retries exceeded with url: http://httpbin.org/ip (Caused by ProxyError('Unable to connect to proxy', ConnectTimeoutError(<urllib3.connection.HTTPConnection object at 0x000001603F038190>, 'Connection to ************* timed out. (connect timeout=10)')))"}, {"key": "*************:9080", "host": "*************", "port": 9080, "first_failed": "2025-05-29T15:46:49.055173", "last_failed": "2025-05-29T15:54:34.867829", "fail_count": 3, "last_error": "HTTPConnectionPool(host='*************', port=9080): Max retries exceeded with url: http://httpbin.org/ip (Caused by ProxyError('Unable to connect to proxy', ConnectTimeoutError(<urllib3.connection.HTTPConnection object at 0x000001603EEBF510>, 'Connection to ************* timed out. (connect timeout=10)')))"}, {"key": "**************:80", "host": "**************", "port": 80, "first_failed": "2025-05-29T15:46:49.068399", "last_failed": "2025-05-29T15:54:34.992222", "fail_count": 3, "last_error": "HTTPConnectionPool(host='**************', port=80): Max retries exceeded with url: http://httpbin.org/ip (Caused by ProxyError('Unable to connect to proxy', ConnectTimeoutError(<urllib3.connection.HTTPConnection object at 0x000001603EE85C10>, 'Connection to ************** timed out. (connect timeout=10)')))"}, {"key": "*************:80", "host": "*************", "port": 80, "first_failed": "2025-05-29T15:46:49.099423", "last_failed": "2025-05-29T15:54:22.604392", "fail_count": 3, "last_error": "HTTPConnectionPool(host='*************', port=80): Max retries exceeded with url: http://httpbin.org/ip (Caused by ProxyError('Unable to connect to proxy', ConnectionResetError(10054, 'An existing connection was forcibly closed by the remote host', None, 10054, None)))"}, {"key": "*************:999", "host": "*************", "port": 999, "first_failed": "2025-05-29T15:46:49.302209", "last_failed": "2025-05-29T15:54:36.739160", "fail_count": 3, "last_error": "HTTPConnectionPool(host='*************', port=999): Max retries exceeded with url: http://httpbin.org/ip (Caused by ProxyError('Unable to connect to proxy', ConnectTimeoutError(<urllib3.connection.HTTPConnection object at 0x000001603EEDBB50>, 'Connection to ************* timed out. (connect timeout=10)')))"}, {"key": "************:45", "host": "************", "port": 45, "first_failed": "2025-05-29T15:46:49.303210", "last_failed": "2025-05-29T15:54:32.310415", "fail_count": 3, "last_error": "HTTPConnectionPool(host='************', port=45): Read timed out. (read timeout=10)"}, {"key": "*************:90", "host": "*************", "port": 90, "first_failed": "2025-05-29T15:46:49.472277", "last_failed": "2025-05-29T15:54:36.785537", "fail_count": 3, "last_error": "HTTPConnectionPool(host='*************', port=90): Max retries exceeded with url: http://httpbin.org/ip (Caused by ProxyError('Unable to connect to proxy', ConnectTimeoutError(<urllib3.connection.HTTPConnection object at 0x000001603EED9510>, 'Connection to ************* timed out. (connect timeout=10)')))"}, {"key": "*************:888", "host": "*************", "port": 888, "first_failed": "2025-05-29T15:46:49.801506", "last_failed": "2025-05-29T15:54:41.475434", "fail_count": 3, "last_error": "HTTPConnectionPool(host='*************', port=888): Read timed out. (read timeout=10)"}, {"key": "**************:9050", "host": "**************", "port": 9050, "first_failed": "2025-05-29T15:46:50.051769", "last_failed": "2025-05-29T15:54:38.817807", "fail_count": 3, "last_error": "HTTPConnectionPool(host='**************', port=9050): Max retries exceeded with url: http://httpbin.org/ip (Caused by ProxyError('Unable to connect to proxy', ConnectTimeoutError(<urllib3.connection.HTTPConnection object at 0x000001603EF30890>, 'Connection to ************** timed out. (connect timeout=10)')))"}, {"key": "***************:8080", "host": "***************", "port": 8080, "first_failed": "2025-05-29T15:46:51.080340", "last_failed": "2025-05-29T15:50:35.903739", "fail_count": 2, "last_error": "HTTPConnectionPool(host='***************', port=8080): Read timed out. (read timeout=10)"}, {"key": "*************:104", "host": "*************", "port": 104, "first_failed": "2025-05-29T15:46:51.158820", "last_failed": "2025-05-29T15:54:42.062569", "fail_count": 3, "last_error": "HTTPConnectionPool(host='*************', port=104): Max retries exceeded with url: http://httpbin.org/ip (Caused by ProxyError('Unable to connect to proxy', ConnectTimeoutError(<urllib3.connection.HTTPConnection object at 0x000001603EEFEA10>, 'Connection to ************* timed out. (connect timeout=10)')))"}, {"key": "************:9080", "host": "************", "port": 9080, "first_failed": "2025-05-29T15:46:51.159807", "last_failed": "2025-05-29T15:54:42.061692", "fail_count": 3, "last_error": "HTTPConnectionPool(host='************', port=9080): Max retries exceeded with url: http://httpbin.org/ip (Caused by ProxyError('Unable to connect to proxy', ConnectTimeoutError(<urllib3.connection.HTTPConnection object at 0x000001603EF03B90>, 'Connection to ************ timed out. (connect timeout=10)')))"}, {"key": "**************:80", "host": "**************", "port": 80, "first_failed": "2025-05-29T15:46:52.356811", "last_failed": "2025-05-29T15:54:32.515315", "fail_count": 3, "last_error": "HTTPConnectionPool(host='**************', port=80): Read timed out. (read timeout=10)"}, {"key": "*************:80", "host": "*************", "port": 80, "first_failed": "2025-05-29T15:50:24.801661", "last_failed": "2025-05-29T15:54:32.061559", "fail_count": 2, "last_error": "HTTPConnectionPool(host='*************', port=80): Max retries exceeded with url: http://httpbin.org/ip (Caused by ProxyError('Unable to connect to proxy', ConnectTimeoutError(<urllib3.connection.HTTPConnection object at 0x000001603EFB81D0>, 'Connection to ************* timed out. (connect timeout=10)')))"}, {"key": "***********:20002", "host": "***********", "port": 20002, "first_failed": "2025-05-29T15:50:25.010359", "last_failed": "2025-05-29T15:54:32.046549", "fail_count": 2, "last_error": "HTTPConnectionPool(host='***********', port=20002): Max retries exceeded with url: http://httpbin.org/ip (Caused by ProxyError('Unable to connect to proxy', ConnectTimeoutError(<urllib3.connection.HTTPConnection object at 0x000001603EF6F250>, 'Connection to *********** timed out. (connect timeout=10)')))"}, {"key": "*************:80", "host": "*************", "port": 80, "first_failed": "2025-05-29T15:50:25.273684", "last_failed": "2025-05-29T15:54:32.500313", "fail_count": 2, "last_error": "HTTPConnectionPool(host='*************', port=80): Max retries exceeded with url: http://httpbin.org/ip (Caused by ProxyError('Unable to connect to proxy', ConnectTimeoutError(<urllib3.connection.HTTPConnection object at 0x000001603EEBC1D0>, 'Connection to ************* timed out. (connect timeout=10)')))"}, {"key": "***********:8004", "host": "***********", "port": 8004, "first_failed": "2025-05-29T15:50:25.297907", "last_failed": "2025-05-29T15:54:32.046549", "fail_count": 2, "last_error": "HTTPConnectionPool(host='***********', port=8004): Max retries exceeded with url: http://httpbin.org/ip (Caused by ProxyError('Unable to connect to proxy', ConnectTimeoutError(<urllib3.connection.HTTPConnection object at 0x000001603EF52410>, 'Connection to *********** timed out. (connect timeout=10)')))"}, {"key": "*************:8080", "host": "*************", "port": 8080, "first_failed": "2025-05-29T15:50:27.894807", "last_failed": "2025-05-29T15:50:27.894807", "fail_count": 1, "last_error": "HTTPConnectionPool(host='*************', port=8080): Max retries exceeded with url: http://httpbin.org/ip (Caused by ProxyError('Unable to connect to proxy', RemoteDisconnected('Remote end closed connection without response')))"}, {"key": "*************:3128", "host": "*************", "port": 3128, "first_failed": "2025-05-29T15:50:29.450439", "last_failed": "2025-05-29T15:50:29.450439", "fail_count": 1, "last_error": "HTTPConnectionPool(host='*************', port=3128): Max retries exceeded with url: http://httpbin.org/ip (Caused by ProxyError('Unable to connect to proxy', RemoteDisconnected('Remote end closed connection without response')))"}, {"key": "*************:8080", "host": "*************", "port": 8080, "first_failed": "2025-05-29T15:50:32.216343", "last_failed": "2025-05-29T15:50:32.216343", "fail_count": 1, "last_error": "HTTPConnectionPool(host='*************', port=8080): Max retries exceeded with url: http://httpbin.org/ip (Caused by ProxyError('Unable to connect to proxy', RemoteDisconnected('Remote end closed connection without response')))"}, {"key": "*************:8080", "host": "*************", "port": 8080, "first_failed": "2025-05-29T15:50:33.401818", "last_failed": "2025-05-29T15:50:33.401818", "fail_count": 1, "last_error": "HTTPConnectionPool(host='*************', port=8080): Max retries exceeded with url: http://httpbin.org/ip (Caused by ProxyError('Unable to connect to proxy', RemoteDisconnected('Remote end closed connection without response')))"}, {"key": "**********:11", "host": "**********", "port": 11, "first_failed": "2025-05-29T15:50:33.683682", "last_failed": "2025-05-29T15:50:33.683682", "fail_count": 1, "last_error": "HTTPConnectionPool(host='**********', port=11): Max retries exceeded with url: http://httpbin.org/ip (Caused by ProxyError('Unable to connect to proxy', ConnectionResetError(10054, 'An existing connection was forcibly closed by the remote host', None, 10054, None)))"}, {"key": "************:808", "host": "************", "port": 808, "first_failed": "2025-05-29T15:50:34.599739", "last_failed": "2025-05-29T15:54:32.030548", "fail_count": 2, "last_error": "HTTPConnectionPool(host='************', port=808): Max retries exceeded with url: http://httpbin.org/ip (Caused by ProxyError('Unable to connect to proxy', ConnectTimeoutError(<urllib3.connection.HTTPConnection object at 0x000001603EF32E10>, 'Connection to ************ timed out. (connect timeout=10)')))"}, {"key": "***************:2016", "host": "***************", "port": 2016, "first_failed": "2025-05-29T15:50:34.618735", "last_failed": "2025-05-29T15:54:32.046549", "fail_count": 2, "last_error": "HTTPConnectionPool(host='***************', port=2016): Max retries exceeded with url: http://httpbin.org/ip (Caused by ProxyError('Unable to connect to proxy', ConnectTimeoutError(<urllib3.connection.HTTPConnection object at 0x000001603EF97B50>, 'Connection to *************** timed out. (connect timeout=10)')))"}, {"key": "************:80", "host": "************", "port": 80, "first_failed": "2025-05-29T15:50:34.629968", "last_failed": "2025-05-29T15:54:32.780244", "fail_count": 3, "last_error": "HTTPConnectionPool(host='************', port=80): Max retries exceeded with url: http://httpbin.org/ip (Caused by ProxyError('Unable to connect to proxy', ConnectTimeoutError(<urllib3.connection.HTTPConnection object at 0x000001603F036D10>, 'Connection to ************ timed out. (connect timeout=10)')))"}, {"key": "************:8080", "host": "************", "port": 8080, "first_failed": "2025-05-29T15:50:34.629968", "last_failed": "2025-05-29T15:54:33.815123", "fail_count": 2, "last_error": "HTTPConnectionPool(host='************', port=8080): Max retries exceeded with url: http://httpbin.org/ip (Caused by ProxyError('Unable to connect to proxy', ConnectTimeoutError(<urllib3.connection.HTTPConnection object at 0x000001603EED11D0>, 'Connection to ************ timed out. (connect timeout=10)')))"}, {"key": "*************:80", "host": "*************", "port": 80, "first_failed": "2025-05-29T15:50:35.057682", "last_failed": "2025-05-29T15:50:35.057682", "fail_count": 1, "last_error": "HTTPConnectionPool(host='*************', port=80): Max retries exceeded with url: http://httpbin.org/ip (Caused by ProxyError('Unable to connect to proxy', RemoteDisconnected('Remote end closed connection without response')))"}, {"key": "*************:8082", "host": "*************", "port": 8082, "first_failed": "2025-05-29T15:50:36.776699", "last_failed": "2025-05-29T15:50:36.776699", "fail_count": 1, "last_error": "HTTPConnectionPool(host='*************', port=8082): Max retries exceeded with url: http://httpbin.org/ip (Caused by ProxyError('Unable to connect to proxy', ConnectTimeoutError(<urllib3.connection.HTTPConnection object at 0x00000246A4198E90>, 'Connection to ************* timed out. (connect timeout=10)')))"}, {"key": "************:2020", "host": "************", "port": 2020, "first_failed": "2025-05-29T15:50:36.840692", "last_failed": "2025-05-29T15:50:36.840692", "fail_count": 1, "last_error": "HTTPConnectionPool(host='************', port=2020): Max retries exceeded with url: http://httpbin.org/ip (Caused by ProxyError('Unable to connect to proxy', ConnectTimeoutError(<urllib3.connection.HTTPConnection object at 0x00000246A413E090>, 'Connection to ************ timed out. (connect timeout=10)')))"}, {"key": "*************:45", "host": "*************", "port": 45, "first_failed": "2025-05-29T15:50:37.652281", "last_failed": "2025-05-29T15:50:37.652281", "fail_count": 1, "last_error": "HTTPConnectionPool(host='*************', port=45): Max retries exceeded with url: http://httpbin.org/ip (Caused by ProxyError('Unable to connect to proxy', ConnectTimeoutError(<urllib3.connection.HTTPConnection object at 0x00000246A2271350>, 'Connection to ************* timed out. (connect timeout=10)')))"}, {"key": "*************:1111", "host": "*************", "port": 1111, "first_failed": "2025-05-29T15:50:37.919022", "last_failed": "2025-05-29T15:50:37.919022", "fail_count": 1, "last_error": "HTTPConnectionPool(host='*************', port=1111): Max retries exceeded with url: http://httpbin.org/ip (Caused by ProxyError('Unable to connect to proxy', ConnectTimeoutError(<urllib3.connection.HTTPConnection object at 0x00000246A22A5610>, 'Connection to ************* timed out. (connect timeout=10)')))"}, {"key": "***********:8008", "host": "***********", "port": 8008, "first_failed": "2025-05-29T15:50:38.355501", "last_failed": "2025-05-29T15:50:38.355501", "fail_count": 1, "last_error": "HTTPConnectionPool(host='***********', port=8008): Max retries exceeded with url: http://httpbin.org/ip (Caused by ProxyError('Unable to connect to proxy', ConnectTimeoutError(<urllib3.connection.HTTPConnection object at 0x00000246A412B910>, 'Connection to *********** timed out. (connect timeout=10)')))"}, {"key": "************:1234", "host": "************", "port": 1234, "first_failed": "2025-05-29T15:50:38.885732", "last_failed": "2025-05-29T15:50:38.885732", "fail_count": 1, "last_error": "HTTPConnectionPool(host='************', port=1234): Max retries exceeded with url: http://httpbin.org/ip (Caused by ProxyError('Unable to connect to proxy', ConnectTimeoutError(<urllib3.connection.HTTPConnection object at 0x00000246A2271410>, 'Connection to ************ timed out. (connect timeout=10)')))"}, {"key": "***************:80", "host": "***************", "port": 80, "first_failed": "2025-05-29T15:50:39.434318", "last_failed": "2025-05-29T15:50:39.434318", "fail_count": 1, "last_error": "HTTPConnectionPool(host='***************', port=80): Max retries exceeded with url: http://httpbin.org/ip (Caused by ProxyError('Unable to connect to proxy', ConnectTimeoutError(<urllib3.connection.HTTPConnection object at 0x00000246A419BF90>, 'Connection to *************** timed out. (connect timeout=10)')))"}, {"key": "**************:8080", "host": "**************", "port": 8080, "first_failed": "2025-05-29T15:50:39.479152", "last_failed": "2025-05-29T15:50:39.479152", "fail_count": 1, "last_error": "HTTPConnectionPool(host='**************', port=8080): Max retries exceeded with url: http://httpbin.org/ip (Caused by ProxyError('Unable to connect to proxy', ConnectTimeoutError(<urllib3.connection.HTTPConnection object at 0x00000246A2353690>, 'Connection to ************** timed out. (connect timeout=10)')))"}, {"key": "***********:9050", "host": "***********", "port": 9050, "first_failed": "2025-05-29T15:50:39.480046", "last_failed": "2025-05-29T15:50:39.480046", "fail_count": 1, "last_error": "HTTPConnectionPool(host='***********', port=9050): Max retries exceeded with url: http://httpbin.org/ip (Caused by ProxyError('Unable to connect to proxy', ConnectTimeoutError(<urllib3.connection.HTTPConnection object at 0x00000246A40FD090>, 'Connection to *********** timed out. (connect timeout=10)')))"}, {"key": "*************:3129", "host": "*************", "port": 3129, "first_failed": "2025-05-29T15:50:39.586727", "last_failed": "2025-05-29T15:50:39.586727", "fail_count": 1, "last_error": "HTTPConnectionPool(host='*************', port=3129): Max retries exceeded with url: http://httpbin.org/ip (Caused by ProxyError('Unable to connect to proxy', ConnectTimeoutError(<urllib3.connection.HTTPConnection object at 0x00000246A23501D0>, 'Connection to ************* timed out. (connect timeout=10)')))"}, {"key": "*************:443", "host": "*************", "port": 443, "first_failed": "2025-05-29T15:50:43.431153", "last_failed": "2025-05-29T15:50:43.431153", "fail_count": 1, "last_error": "HTTPConnectionPool(host='*************', port=443): Max retries exceeded with url: http://httpbin.org/ip (Caused by ProxyError('Unable to connect to proxy', ConnectTimeoutError(<urllib3.connection.HTTPConnection object at 0x00000246A22A3490>, 'Connection to ************* timed out. (connect timeout=10)')))"}, {"key": "*************:8081", "host": "*************", "port": 8081, "first_failed": "2025-05-29T15:50:44.653003", "last_failed": "2025-05-29T15:50:44.653003", "fail_count": 1, "last_error": "HTTPConnectionPool(host='*************', port=8081): Max retries exceeded with url: http://httpbin.org/ip (Caused by ProxyError('Unable to connect to proxy', ConnectTimeoutError(<urllib3.connection.HTTPConnection object at 0x00000246A248EBD0>, 'Connection to ************* timed out. (connect timeout=10)')))"}, {"key": "*************:10801", "host": "*************", "port": 10801, "first_failed": "2025-05-29T15:54:22.161557", "last_failed": "2025-05-29T15:54:32.139549", "fail_count": 2, "last_error": "HTTPConnectionPool(host='*************', port=10801): Read timed out. (read timeout=10)"}, {"key": "*************:4002", "host": "*************", "port": 4002, "first_failed": "2025-05-29T15:54:22.166561", "last_failed": "2025-05-29T15:54:32.108185", "fail_count": 2, "last_error": "HTTPConnectionPool(host='*************', port=4002): Read timed out. (read timeout=10)"}, {"key": "*************:202", "host": "*************", "port": 202, "first_failed": "2025-05-29T15:54:22.169552", "last_failed": "2025-05-29T15:54:32.781342", "fail_count": 2, "last_error": "HTTPConnectionPool(host='*************', port=202): Max retries exceeded with url: http://httpbin.org/ip (Caused by ProxyError('Unable to connect to proxy', ConnectTimeoutError(<urllib3.connection.HTTPConnection object at 0x000001603F03AC50>, 'Connection to ************* timed out. (connect timeout=10)')))"}, {"key": "************:8001", "host": "************", "port": 8001, "first_failed": "2025-05-29T15:54:22.284465", "last_failed": "2025-05-29T15:54:22.284465", "fail_count": 1, "last_error": "HTTPConnectionPool(host='************', port=8001): Max retries exceeded with url: http://httpbin.org/ip (Caused by ProxyError('Unable to connect to proxy', ConnectionResetError(10054, 'An existing connection was forcibly closed by the remote host', None, 10054, None)))"}, {"key": "*************:8888", "host": "*************", "port": 8888, "first_failed": "2025-05-29T15:54:22.453767", "last_failed": "2025-05-29T15:54:32.310415", "fail_count": 2, "last_error": "HTTPConnectionPool(host='*************', port=8888): Read timed out. (read timeout=10)"}, {"key": "************:138", "host": "************", "port": 138, "first_failed": "2025-05-29T15:54:22.530805", "last_failed": "2025-05-29T15:54:33.198933", "fail_count": 2, "last_error": "HTTPConnectionPool(host='************', port=138): Max retries exceeded with url: http://httpbin.org/ip (Caused by ProxyError('Unable to connect to proxy', ConnectTimeoutError(<urllib3.connection.HTTPConnection object at 0x00000160375D7390>, 'Connection to ************ timed out. (connect timeout=10)')))"}, {"key": "************:8081", "host": "************", "port": 8081, "first_failed": "2025-05-29T15:54:22.572803", "last_failed": "2025-05-29T15:54:41.380348", "fail_count": 2, "last_error": "HTTPConnectionPool(host='************', port=8081): Max retries exceeded with url: http://httpbin.org/ip (Caused by ProxyError('Unable to connect to proxy', ConnectTimeoutError(<urllib3.connection.HTTPConnection object at 0x000001603EF0ED50>, 'Connection to ************ timed out. (connect timeout=10)')))"}, {"key": "************:8080", "host": "************", "port": 8080, "first_failed": "2025-05-29T15:54:22.746348", "last_failed": "2025-05-29T15:54:33.074400", "fail_count": 2, "last_error": "HTTPConnectionPool(host='************', port=8080): Max retries exceeded with url: http://httpbin.org/ip (Caused by ProxyError('Unable to connect to proxy', ConnectTimeoutError(<urllib3.connection.HTTPConnection object at 0x0000016037539E50>, 'Connection to ************ timed out. (connect timeout=10)')))"}, {"key": "*************:9098", "host": "*************", "port": 9098, "first_failed": "2025-05-29T15:54:22.758215", "last_failed": "2025-05-29T15:54:34.210494", "fail_count": 2, "last_error": "HTTPConnectionPool(host='*************', port=9098): Max retries exceeded with url: http://httpbin.org/ip (Caused by ProxyError('Unable to connect to proxy', ConnectTimeoutError(<urllib3.connection.HTTPConnection object at 0x000001603EEEF810>, 'Connection to ************* timed out. (connect timeout=10)')))"}, {"key": "**********:35", "host": "**********", "port": 35, "first_failed": "2025-05-29T15:54:32.030548", "last_failed": "2025-05-29T15:54:32.030548", "fail_count": 1, "last_error": "HTTPConnectionPool(host='**********', port=35): Max retries exceeded with url: http://httpbin.org/ip (Caused by ProxyError('Unable to connect to proxy', ConnectTimeoutError(<urllib3.connection.HTTPConnection object at 0x000001603EF0D710>, 'Connection to ********** timed out. (connect timeout=10)')))"}, {"key": "**************:80", "host": "**************", "port": 80, "first_failed": "2025-05-29T15:54:32.030548", "last_failed": "2025-05-29T15:54:32.030548", "fail_count": 1, "last_error": "HTTPConnectionPool(host='**************', port=80): Max retries exceeded with url: http://httpbin.org/ip (Caused by ProxyError('Unable to connect to proxy', ConnectTimeoutError(<urllib3.connection.HTTPConnection object at 0x000001603EF01BD0>, 'Connection to ************** timed out. (connect timeout=10)')))"}, {"key": "**************:80", "host": "**************", "port": 80, "first_failed": "2025-05-29T15:54:33.352772", "last_failed": "2025-05-29T15:54:33.352772", "fail_count": 1, "last_error": "HTTPConnectionPool(host='**************', port=80): Read timed out. (read timeout=10)"}], "last_updated": "2025-05-29T15:54:42.062569"}