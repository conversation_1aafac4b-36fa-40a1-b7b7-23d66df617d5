{"working_proxies": [{"key": "**************:7777", "host": "**************", "port": 7777, "type": "http", "username": null, "password": null, "first_tested": "2025-05-29T15:46:40.034738", "last_tested": "2025-05-29T15:46:40.034738", "success_count": 1, "response_time": 975}, {"key": "*************:9094", "host": "*************", "port": 9094, "type": "http", "username": null, "password": null, "first_tested": "2025-05-29T15:46:40.659037", "last_tested": "2025-05-29T15:50:29.426243", "success_count": 2, "response_time": 1598}, {"key": "*************:8081", "host": "*************", "port": 8081, "type": "http", "username": null, "password": null, "first_tested": "2025-05-29T15:46:40.824362", "last_tested": "2025-05-29T15:50:26.207170", "success_count": 2, "response_time": 1150}, {"key": "************:80", "host": "************", "port": 80, "type": "http", "username": null, "password": null, "first_tested": "2025-05-29T15:46:41.136145", "last_tested": "2025-05-29T15:46:41.136145", "success_count": 1, "response_time": 2089}, {"key": "*************:9094", "host": "*************", "port": 9094, "type": "http", "username": null, "password": null, "first_tested": "2025-05-29T15:46:41.142160", "last_tested": "2025-05-29T15:50:27.581782", "success_count": 2, "response_time": 2098}, {"key": "*************:4002", "host": "*************", "port": 4002, "type": "http", "username": null, "password": null, "first_tested": "2025-05-29T15:46:41.180775", "last_tested": "2025-05-29T15:50:28.870965", "success_count": 2, "response_time": 2142}, {"key": "*************:10801", "host": "*************", "port": 10801, "type": "http", "username": null, "password": null, "first_tested": "2025-05-29T15:46:41.184758", "last_tested": "2025-05-29T15:50:26.757058", "success_count": 2, "response_time": 2150}, {"key": "**************:8080", "host": "**************", "port": 8080, "type": "http", "username": null, "password": null, "first_tested": "2025-05-29T15:50:25.479332", "last_tested": "2025-05-29T15:50:25.479332", "success_count": 1, "response_time": 840}, {"key": "*************:18080", "host": "*************", "port": 18080, "type": "http", "username": null, "password": null, "first_tested": "2025-05-29T15:50:25.666020", "last_tested": "2025-05-29T15:50:25.666020", "success_count": 1, "response_time": 1021}, {"key": "**************:3128", "host": "**************", "port": 3128, "type": "http", "username": null, "password": null, "first_tested": "2025-05-29T15:50:25.852444", "last_tested": "2025-05-29T15:50:25.852444", "success_count": 1, "response_time": 1035}, {"key": "*************:8080", "host": "*************", "port": 8080, "type": "http", "username": null, "password": null, "first_tested": "2025-05-29T15:50:26.627338", "last_tested": "2025-05-29T15:50:26.627338", "success_count": 1, "response_time": 1123}, {"key": "*************:202", "host": "*************", "port": 202, "type": "http", "username": null, "password": null, "first_tested": "2025-05-29T15:50:26.777058", "last_tested": "2025-05-29T15:50:26.777058", "success_count": 1, "response_time": 2158}, {"key": "************:45", "host": "************", "port": 45, "type": "http", "username": null, "password": null, "first_tested": "2025-05-29T15:50:27.168203", "last_tested": "2025-05-29T15:50:27.168203", "success_count": 1, "response_time": 2553}, {"key": "**************:1080", "host": "**************", "port": 1080, "type": "http", "username": null, "password": null, "first_tested": "2025-05-29T15:50:27.245434", "last_tested": "2025-05-29T15:50:27.245434", "success_count": 1, "response_time": 2619}, {"key": "*************:9098", "host": "*************", "port": 9098, "type": "http", "username": null, "password": null, "first_tested": "2025-05-29T15:50:27.360123", "last_tested": "2025-05-29T15:50:27.360123", "success_count": 1, "response_time": 2724}, {"key": "************:8080", "host": "************", "port": 8080, "type": "http", "username": null, "password": null, "first_tested": "2025-05-29T15:50:27.373331", "last_tested": "2025-05-29T15:50:27.373331", "success_count": 1, "response_time": 2747}, {"key": "*************:8888", "host": "*************", "port": 8888, "type": "http", "username": null, "password": null, "first_tested": "2025-05-29T15:50:27.633030", "last_tested": "2025-05-29T15:50:27.633030", "success_count": 1, "response_time": 3023}, {"key": "************:138", "host": "************", "port": 138, "type": "http", "username": null, "password": null, "first_tested": "2025-05-29T15:50:28.197335", "last_tested": "2025-05-29T15:50:28.197335", "success_count": 1, "response_time": 3556}, {"key": "************:8081", "host": "************", "port": 8081, "type": "http", "username": null, "password": null, "first_tested": "2025-05-29T15:50:28.331169", "last_tested": "2025-05-29T15:50:28.331169", "success_count": 1, "response_time": 2634}, {"key": "************:8880", "host": "************", "port": 8880, "type": "http", "username": null, "password": null, "first_tested": "2025-05-29T15:50:28.793477", "last_tested": "2025-05-29T15:50:28.793477", "success_count": 1, "response_time": 1524}, {"key": "************:10024", "host": "************", "port": 10024, "type": "http", "username": null, "password": null, "first_tested": "2025-05-29T15:50:28.840754", "last_tested": "2025-05-29T15:50:28.840754", "success_count": 1, "response_time": 1242}, {"key": "**************:80", "host": "**************", "port": 80, "type": "http", "username": null, "password": null, "first_tested": "2025-05-29T15:50:29.459455", "last_tested": "2025-05-29T15:50:29.459455", "success_count": 1, "response_time": 604}, {"key": "************:8001", "host": "************", "port": 8001, "type": "http", "username": null, "password": null, "first_tested": "2025-05-29T15:50:29.566768", "last_tested": "2025-05-29T15:50:29.566768", "success_count": 1, "response_time": 2181}, {"key": "**********:35", "host": "**********", "port": 35, "type": "http", "username": null, "password": null, "first_tested": "2025-05-29T15:50:30.664981", "last_tested": "2025-05-29T15:50:30.664981", "success_count": 1, "response_time": 2437}, {"key": "**************:8080", "host": "**************", "port": 8080, "type": "http", "username": null, "password": null, "first_tested": "2025-05-29T15:50:31.775551", "last_tested": "2025-05-29T15:50:31.775551", "success_count": 1, "response_time": 7145}, {"key": "*************:12000", "host": "*************", "port": 12000, "type": "http", "username": null, "password": null, "first_tested": "2025-05-29T15:50:32.837426", "last_tested": "2025-05-29T15:50:32.837426", "success_count": 1, "response_time": 2148}, {"key": "**************:80", "host": "**************", "port": 80, "type": "http", "username": null, "password": null, "first_tested": "2025-05-29T15:50:32.964033", "last_tested": "2025-05-29T15:50:32.964033", "success_count": 1, "response_time": 711}, {"key": "************:80", "host": "************", "port": 80, "type": "http", "username": null, "password": null, "first_tested": "2025-05-29T15:50:33.249304", "last_tested": "2025-05-29T15:50:33.249304", "success_count": 1, "response_time": 8604}], "failed_proxies": [{"key": "*************:8000", "host": "*************", "port": 8000, "first_failed": "2025-05-29T15:46:39.185781", "last_failed": "2025-05-29T15:50:34.622755", "fail_count": 2, "last_error": "HTTPConnectionPool(host='*************', port=8000): Max retries exceeded with url: http://httpbin.org/ip (Caused by ProxyError('Unable to connect to proxy', ConnectTimeoutError(<urllib3.connection.HTTPConnection object at 0x00000246A4178910>, 'Connection to ************* timed out. (connect timeout=10)')))"}, {"key": "**************:8080", "host": "**************", "port": 8080, "first_failed": "2025-05-29T15:46:39.273411", "last_failed": "2025-05-29T15:46:39.273411", "fail_count": 1, "last_error": "HTTPConnectionPool(host='**************', port=8080): Max retries exceeded with url: http://httpbin.org/ip (Caused by ProxyError('Unable to connect to proxy', RemoteDisconnected('Remote end closed connection without response')))"}, {"key": "***********:80", "host": "***********", "port": 80, "first_failed": "2025-05-29T15:46:39.456576", "last_failed": "2025-05-29T15:50:25.030347", "fail_count": 2, "last_error": "HTTPConnectionPool(host='***********', port=80): Max retries exceeded with url: http://httpbin.org/ip (Caused by ProxyError('Unable to connect to proxy', RemoteDisconnected('Remote end closed connection without response')))"}, {"key": "*************:8000", "host": "*************", "port": 8000, "first_failed": "2025-05-29T15:46:39.630145", "last_failed": "2025-05-29T15:50:25.199984", "fail_count": 2, "last_error": "HTTPConnectionPool(host='*************', port=8000): Max retries exceeded with url: http://httpbin.org/ip (Caused by ProxyError('Unable to connect to proxy', RemoteDisconnected('Remote end closed connection without response')))"}, {"key": "************:8001", "host": "************", "port": 8001, "first_failed": "2025-05-29T15:46:39.768580", "last_failed": "2025-05-29T15:50:34.629968", "fail_count": 2, "last_error": "HTTPConnectionPool(host='************', port=8001): Max retries exceeded with url: http://httpbin.org/ip (Caused by ProxyError('Unable to connect to proxy', ConnectTimeoutError(<urllib3.connection.HTTPConnection object at 0x00000246A228BB90>, 'Connection to ************ timed out. (connect timeout=10)')))"}, {"key": "************:888", "host": "************", "port": 888, "first_failed": "2025-05-29T15:46:40.298007", "last_failed": "2025-05-29T15:50:34.606740", "fail_count": 2, "last_error": "HTTPConnectionPool(host='************', port=888): Max retries exceeded with url: http://httpbin.org/ip (Caused by ProxyError('Unable to connect to proxy', ConnectTimeoutError(<urllib3.connection.HTTPConnection object at 0x00000246A40F0590>, 'Connection to ************ timed out. (connect timeout=10)')))"}, {"key": "************:8002", "host": "************", "port": 8002, "first_failed": "2025-05-29T15:46:41.032643", "last_failed": "2025-05-29T15:50:35.319748", "fail_count": 2, "last_error": "HTTPConnectionPool(host='************', port=8002): Max retries exceeded with url: http://httpbin.org/ip (Caused by ProxyError('Unable to connect to proxy', ConnectTimeoutError(<urllib3.connection.HTTPConnection object at 0x00000246A2351DD0>, 'Connection to ************ timed out. (connect timeout=10)')))"}, {"key": "**************:3128", "host": "**************", "port": 3128, "first_failed": "2025-05-29T15:46:43.307498", "last_failed": "2025-05-29T15:46:43.307498", "fail_count": 1, "last_error": "HTTPConnectionPool(host='**************', port=3128): Max retries exceeded with url: http://httpbin.org/ip (Caused by ProxyError('Unable to connect to proxy', ConnectionResetError(10054, 'An existing connection was forcibly closed by the remote host', None, 10054, None)))"}, {"key": "************:8080", "host": "************", "port": 8080, "first_failed": "2025-05-29T15:46:46.799136", "last_failed": "2025-05-29T15:50:34.629968", "fail_count": 2, "last_error": "HTTPConnectionPool(host='************', port=8080): Max retries exceeded with url: http://httpbin.org/ip (Caused by ProxyError('Unable to connect to proxy', ConnectTimeoutError(<urllib3.connection.HTTPConnection object at 0x00000246A22A4310>, 'Connection to ************ timed out. (connect timeout=10)')))"}, {"key": "************:8004", "host": "************", "port": 8004, "first_failed": "2025-05-29T15:46:47.764991", "last_failed": "2025-05-29T15:50:34.629968", "fail_count": 2, "last_error": "HTTPConnectionPool(host='************', port=8004): Max retries exceeded with url: http://httpbin.org/ip (Caused by ProxyError('Unable to connect to proxy', ConnectTimeoutError(<urllib3.connection.HTTPConnection object at 0x00000246A2330C90>, 'Connection to ************ timed out. (connect timeout=10)')))"}, {"key": "*************:80", "host": "*************", "port": 80, "first_failed": "2025-05-29T15:46:49.023476", "last_failed": "2025-05-29T15:50:34.600746", "fail_count": 2, "last_error": "HTTPConnectionPool(host='*************', port=80): Max retries exceeded with url: http://httpbin.org/ip (Caused by ProxyError('Unable to connect to proxy', ConnectTimeoutError(<urllib3.connection.HTTPConnection object at 0x00000246A40EE510>, 'Connection to ************* timed out. (connect timeout=10)')))"}, {"key": "**************:81", "host": "**************", "port": 81, "first_failed": "2025-05-29T15:46:49.024476", "last_failed": "2025-05-29T15:50:34.598740", "fail_count": 2, "last_error": "HTTPConnectionPool(host='**************', port=81): Max retries exceeded with url: http://httpbin.org/ip (Caused by ProxyError('Unable to connect to proxy', ConnectTimeoutError(<urllib3.connection.HTTPConnection object at 0x00000246A40D9C10>, 'Connection to ************** timed out. (connect timeout=10)')))"}, {"key": "*************:11", "host": "*************", "port": 11, "first_failed": "2025-05-29T15:46:49.037939", "last_failed": "2025-05-29T15:50:34.618735", "fail_count": 2, "last_error": "HTTPConnectionPool(host='*************', port=11): Max retries exceeded with url: http://httpbin.org/ip (Caused by ProxyError('Unable to connect to proxy', ConnectTimeoutError(<urllib3.connection.HTTPConnection object at 0x00000246A4164650>, 'Connection to ************* timed out. (connect timeout=10)')))"}, {"key": "*************:18080", "host": "*************", "port": 18080, "first_failed": "2025-05-29T15:46:49.038949", "last_failed": "2025-05-29T15:50:34.600746", "fail_count": 2, "last_error": "HTTPConnectionPool(host='*************', port=18080): Max retries exceeded with url: http://httpbin.org/ip (Caused by ProxyError('Unable to connect to proxy', ConnectTimeoutError(<urllib3.connection.HTTPConnection object at 0x00000246A40ED610>, 'Connection to ************* timed out. (connect timeout=10)')))"}, {"key": "*************:80", "host": "*************", "port": 80, "first_failed": "2025-05-29T15:46:49.039947", "last_failed": "2025-05-29T15:50:34.618735", "fail_count": 2, "last_error": "HTTPConnectionPool(host='*************', port=80): Max retries exceeded with url: http://httpbin.org/ip (Caused by ProxyError('Unable to connect to proxy', ConnectTimeoutError(<urllib3.connection.HTTPConnection object at 0x00000246A413D590>, 'Connection to ************* timed out. (connect timeout=10)')))"}, {"key": "*************:31281", "host": "*************", "port": 31281, "first_failed": "2025-05-29T15:46:49.039947", "last_failed": "2025-05-29T15:50:34.621733", "fail_count": 2, "last_error": "HTTPConnectionPool(host='*************', port=31281): Max retries exceeded with url: http://httpbin.org/ip (Caused by ProxyError('Unable to connect to proxy', ConnectTimeoutError(<urllib3.connection.HTTPConnection object at 0x00000246A410CC50>, 'Connection to ************* timed out. (connect timeout=10)')))"}, {"key": "*************:1080", "host": "*************", "port": 1080, "first_failed": "2025-05-29T15:46:49.040943", "last_failed": "2025-05-29T15:50:34.619737", "fail_count": 2, "last_error": "HTTPConnectionPool(host='*************', port=1080): Max retries exceeded with url: http://httpbin.org/ip (Caused by ProxyError('Unable to connect to proxy', ConnectTimeoutError(<urllib3.connection.HTTPConnection object at 0x00000246A4119510>, 'Connection to ************* timed out. (connect timeout=10)')))"}, {"key": "*************:4002", "host": "*************", "port": 4002, "first_failed": "2025-05-29T15:46:49.040943", "last_failed": "2025-05-29T15:50:34.605752", "fail_count": 2, "last_error": "HTTPConnectionPool(host='*************', port=4002): Max retries exceeded with url: http://httpbin.org/ip (Caused by ProxyError('Unable to connect to proxy', ConnectTimeoutError(<urllib3.connection.HTTPConnection object at 0x00000246A40FE710>, 'Connection to ************* timed out. (connect timeout=10)')))"}, {"key": "***********:9443", "host": "***********", "port": 9443, "first_failed": "2025-05-29T15:46:49.045940", "last_failed": "2025-05-29T15:50:34.619737", "fail_count": 2, "last_error": "HTTPConnectionPool(host='***********', port=9443): Max retries exceeded with url: http://httpbin.org/ip (Caused by ProxyError('Unable to connect to proxy', ConnectTimeoutError(<urllib3.connection.HTTPConnection object at 0x00000246A411BD90>, 'Connection to *********** timed out. (connect timeout=10)')))"}, {"key": "************:3127", "host": "************", "port": 3127, "first_failed": "2025-05-29T15:46:49.046938", "last_failed": "2025-05-29T15:50:34.621733", "fail_count": 2, "last_error": "HTTPConnectionPool(host='************', port=3127): Max retries exceeded with url: http://httpbin.org/ip (Caused by ProxyError('Unable to connect to proxy', ConnectTimeoutError(<urllib3.connection.HTTPConnection object at 0x00000246A41548D0>, 'Connection to ************ timed out. (connect timeout=10)')))"}, {"key": "*************:6666", "host": "*************", "port": 6666, "first_failed": "2025-05-29T15:46:49.053200", "last_failed": "2025-05-29T15:50:34.629968", "fail_count": 2, "last_error": "HTTPConnectionPool(host='*************', port=6666): Max retries exceeded with url: http://httpbin.org/ip (Caused by ProxyError('Unable to connect to proxy', ConnectTimeoutError(<urllib3.connection.HTTPConnection object at 0x00000246A2297350>, 'Connection to ************* timed out. (connect timeout=10)')))"}, {"key": "*************:999", "host": "*************", "port": 999, "first_failed": "2025-05-29T15:46:49.053200", "last_failed": "2025-05-29T15:50:34.616752", "fail_count": 2, "last_error": "HTTPConnectionPool(host='*************', port=999): Max retries exceeded with url: http://httpbin.org/ip (Caused by ProxyError('Unable to connect to proxy', ConnectTimeoutError(<urllib3.connection.HTTPConnection object at 0x00000246A4196250>, 'Connection to ************* timed out. (connect timeout=10)')))"}, {"key": "************:31433", "host": "************", "port": 31433, "first_failed": "2025-05-29T15:46:49.054175", "last_failed": "2025-05-29T15:50:34.616752", "fail_count": 2, "last_error": "HTTPConnectionPool(host='************', port=31433): Max retries exceeded with url: http://httpbin.org/ip (Caused by ProxyError('Unable to connect to proxy', ConnectTimeoutError(<urllib3.connection.HTTPConnection object at 0x00000246A417ABD0>, 'Connection to ************ timed out. (connect timeout=10)')))"}, {"key": "*************:9091", "host": "*************", "port": 9091, "first_failed": "2025-05-29T15:46:49.054175", "last_failed": "2025-05-29T15:50:34.629968", "fail_count": 2, "last_error": "HTTPConnectionPool(host='*************', port=9091): Max retries exceeded with url: http://httpbin.org/ip (Caused by ProxyError('Unable to connect to proxy', ConnectTimeoutError(<urllib3.connection.HTTPConnection object at 0x00000246A22B5290>, 'Connection to ************* timed out. (connect timeout=10)')))"}, {"key": "*************:8080", "host": "*************", "port": 8080, "first_failed": "2025-05-29T15:46:49.055173", "last_failed": "2025-05-29T15:50:34.619737", "fail_count": 2, "last_error": "HTTPConnectionPool(host='*************', port=8080): Max retries exceeded with url: http://httpbin.org/ip (Caused by ProxyError('Unable to connect to proxy', ConnectTimeoutError(<urllib3.connection.HTTPConnection object at 0x00000246A4169F10>, 'Connection to ************* timed out. (connect timeout=10)')))"}, {"key": "*************:9080", "host": "*************", "port": 9080, "first_failed": "2025-05-29T15:46:49.055173", "last_failed": "2025-05-29T15:50:34.629968", "fail_count": 2, "last_error": "HTTPConnectionPool(host='*************', port=9080): Max retries exceeded with url: http://httpbin.org/ip (Caused by ProxyError('Unable to connect to proxy', ConnectTimeoutError(<urllib3.connection.HTTPConnection object at 0x00000246A22BC810>, 'Connection to ************* timed out. (connect timeout=10)')))"}, {"key": "**************:80", "host": "**************", "port": 80, "first_failed": "2025-05-29T15:46:49.068399", "last_failed": "2025-05-29T15:50:34.629968", "fail_count": 2, "last_error": "HTTPConnectionPool(host='**************', port=80): Max retries exceeded with url: http://httpbin.org/ip (Caused by ProxyError('Unable to connect to proxy', ConnectTimeoutError(<urllib3.connection.HTTPConnection object at 0x00000246A22BD8D0>, 'Connection to ************** timed out. (connect timeout=10)')))"}, {"key": "*************:80", "host": "*************", "port": 80, "first_failed": "2025-05-29T15:46:49.099423", "last_failed": "2025-05-29T15:50:34.659978", "fail_count": 2, "last_error": "HTTPConnectionPool(host='*************', port=80): Read timed out. (read timeout=10)"}, {"key": "*************:999", "host": "*************", "port": 999, "first_failed": "2025-05-29T15:46:49.302209", "last_failed": "2025-05-29T15:50:34.834090", "fail_count": 2, "last_error": "HTTPConnectionPool(host='*************', port=999): Max retries exceeded with url: http://httpbin.org/ip (Caused by ProxyError('Unable to connect to proxy', ConnectTimeoutError(<urllib3.connection.HTTPConnection object at 0x00000246A23382D0>, 'Connection to ************* timed out. (connect timeout=10)')))"}, {"key": "************:45", "host": "************", "port": 45, "first_failed": "2025-05-29T15:46:49.303210", "last_failed": "2025-05-29T15:46:49.303210", "fail_count": 1, "last_error": "HTTPConnectionPool(host='************', port=45): Read timed out. (read timeout=10)"}, {"key": "*************:90", "host": "*************", "port": 90, "first_failed": "2025-05-29T15:46:49.472277", "last_failed": "2025-05-29T15:50:35.036383", "fail_count": 2, "last_error": "HTTPConnectionPool(host='*************', port=90): Max retries exceeded with url: http://httpbin.org/ip (Caused by ProxyError('Unable to connect to proxy', ConnectTimeoutError(<urllib3.connection.HTTPConnection object at 0x00000246A2339D10>, 'Connection to ************* timed out. (connect timeout=10)')))"}, {"key": "*************:888", "host": "*************", "port": 888, "first_failed": "2025-05-29T15:46:49.801506", "last_failed": "2025-05-29T15:50:35.274711", "fail_count": 2, "last_error": "HTTPConnectionPool(host='*************', port=888): Max retries exceeded with url: http://httpbin.org/ip (Caused by ProxyError('Unable to connect to proxy', ConnectTimeoutError(<urllib3.connection.HTTPConnection object at 0x00000246A233B290>, 'Connection to ************* timed out. (connect timeout=10)')))"}, {"key": "**************:9050", "host": "**************", "port": 9050, "first_failed": "2025-05-29T15:46:50.051769", "last_failed": "2025-05-29T15:50:35.288999", "fail_count": 2, "last_error": "HTTPConnectionPool(host='**************', port=9050): Max retries exceeded with url: http://httpbin.org/ip (Caused by ProxyError('Unable to connect to proxy', ConnectTimeoutError(<urllib3.connection.HTTPConnection object at 0x00000246A233BC90>, 'Connection to ************** timed out. (connect timeout=10)')))"}, {"key": "***************:8080", "host": "***************", "port": 8080, "first_failed": "2025-05-29T15:46:51.080340", "last_failed": "2025-05-29T15:50:35.903739", "fail_count": 2, "last_error": "HTTPConnectionPool(host='***************', port=8080): Read timed out. (read timeout=10)"}, {"key": "*************:104", "host": "*************", "port": 104, "first_failed": "2025-05-29T15:46:51.158820", "last_failed": "2025-05-29T15:50:36.218452", "fail_count": 2, "last_error": "HTTPConnectionPool(host='*************', port=104): Max retries exceeded with url: http://httpbin.org/ip (Caused by ProxyError('Unable to connect to proxy', ConnectTimeoutError(<urllib3.connection.HTTPConnection object at 0x00000246A233BAD0>, 'Connection to ************* timed out. (connect timeout=10)')))"}, {"key": "************:9080", "host": "************", "port": 9080, "first_failed": "2025-05-29T15:46:51.159807", "last_failed": "2025-05-29T15:50:36.638408", "fail_count": 2, "last_error": "HTTPConnectionPool(host='************', port=9080): Max retries exceeded with url: http://httpbin.org/ip (Caused by ProxyError('Unable to connect to proxy', ConnectTimeoutError(<urllib3.connection.HTTPConnection object at 0x00000246A4188B90>, 'Connection to ************ timed out. (connect timeout=10)')))"}, {"key": "**************:80", "host": "**************", "port": 80, "first_failed": "2025-05-29T15:46:52.356811", "last_failed": "2025-05-29T15:50:34.895321", "fail_count": 2, "last_error": "HTTPConnectionPool(host='**************', port=80): Read timed out. (read timeout=10)"}, {"key": "*************:80", "host": "*************", "port": 80, "first_failed": "2025-05-29T15:50:24.801661", "last_failed": "2025-05-29T15:50:24.801661", "fail_count": 1, "last_error": "HTTPConnectionPool(host='*************', port=80): Max retries exceeded with url: http://httpbin.org/ip (Caused by ProxyError('Unable to connect to proxy', ConnectionResetError(10054, 'An existing connection was forcibly closed by the remote host', None, 10054, None)))"}, {"key": "***********:20002", "host": "***********", "port": 20002, "first_failed": "2025-05-29T15:50:25.010359", "last_failed": "2025-05-29T15:50:25.010359", "fail_count": 1, "last_error": "HTTPConnectionPool(host='***********', port=20002): Max retries exceeded with url: http://httpbin.org/ip (Caused by ProxyError('Unable to connect to proxy', ConnectionResetError(10054, 'An existing connection was forcibly closed by the remote host', None, 10054, None)))"}, {"key": "*************:80", "host": "*************", "port": 80, "first_failed": "2025-05-29T15:50:25.273684", "last_failed": "2025-05-29T15:50:25.273684", "fail_count": 1, "last_error": "HTTPConnectionPool(host='*************', port=80): Max retries exceeded with url: http://httpbin.org/ip (Caused by ProxyError('Unable to connect to proxy', RemoteDisconnected('Remote end closed connection without response')))"}, {"key": "***********:8004", "host": "***********", "port": 8004, "first_failed": "2025-05-29T15:50:25.297907", "last_failed": "2025-05-29T15:50:25.297907", "fail_count": 1, "last_error": "HTTPConnectionPool(host='***********', port=8004): Max retries exceeded with url: http://httpbin.org/ip (Caused by ProxyError('Unable to connect to proxy', RemoteDisconnected('Remote end closed connection without response')))"}, {"key": "*************:8080", "host": "*************", "port": 8080, "first_failed": "2025-05-29T15:50:27.894807", "last_failed": "2025-05-29T15:50:27.894807", "fail_count": 1, "last_error": "HTTPConnectionPool(host='*************', port=8080): Max retries exceeded with url: http://httpbin.org/ip (Caused by ProxyError('Unable to connect to proxy', RemoteDisconnected('Remote end closed connection without response')))"}, {"key": "*************:3128", "host": "*************", "port": 3128, "first_failed": "2025-05-29T15:50:29.450439", "last_failed": "2025-05-29T15:50:29.450439", "fail_count": 1, "last_error": "HTTPConnectionPool(host='*************', port=3128): Max retries exceeded with url: http://httpbin.org/ip (Caused by ProxyError('Unable to connect to proxy', RemoteDisconnected('Remote end closed connection without response')))"}, {"key": "*************:8080", "host": "*************", "port": 8080, "first_failed": "2025-05-29T15:50:32.216343", "last_failed": "2025-05-29T15:50:32.216343", "fail_count": 1, "last_error": "HTTPConnectionPool(host='*************', port=8080): Max retries exceeded with url: http://httpbin.org/ip (Caused by ProxyError('Unable to connect to proxy', RemoteDisconnected('Remote end closed connection without response')))"}, {"key": "*************:8080", "host": "*************", "port": 8080, "first_failed": "2025-05-29T15:50:33.401818", "last_failed": "2025-05-29T15:50:33.401818", "fail_count": 1, "last_error": "HTTPConnectionPool(host='*************', port=8080): Max retries exceeded with url: http://httpbin.org/ip (Caused by ProxyError('Unable to connect to proxy', RemoteDisconnected('Remote end closed connection without response')))"}, {"key": "**********:11", "host": "**********", "port": 11, "first_failed": "2025-05-29T15:50:33.683682", "last_failed": "2025-05-29T15:50:33.683682", "fail_count": 1, "last_error": "HTTPConnectionPool(host='**********', port=11): Max retries exceeded with url: http://httpbin.org/ip (Caused by ProxyError('Unable to connect to proxy', ConnectionResetError(10054, 'An existing connection was forcibly closed by the remote host', None, 10054, None)))"}, {"key": "************:808", "host": "************", "port": 808, "first_failed": "2025-05-29T15:50:34.599739", "last_failed": "2025-05-29T15:50:34.599739", "fail_count": 1, "last_error": "HTTPConnectionPool(host='************', port=808): Max retries exceeded with url: http://httpbin.org/ip (Caused by ProxyError('Unable to connect to proxy', ConnectTimeoutError(<urllib3.connection.HTTPConnection object at 0x00000246A40CF610>, 'Connection to ************ timed out. (connect timeout=10)')))"}, {"key": "***************:2016", "host": "***************", "port": 2016, "first_failed": "2025-05-29T15:50:34.618735", "last_failed": "2025-05-29T15:50:34.618735", "fail_count": 1, "last_error": "HTTPConnectionPool(host='***************', port=2016): Max retries exceeded with url: http://httpbin.org/ip (Caused by ProxyError('Unable to connect to proxy', ConnectTimeoutError(<urllib3.connection.HTTPConnection object at 0x00000246A412EF10>, 'Connection to *************** timed out. (connect timeout=10)')))"}, {"key": "************:80", "host": "************", "port": 80, "first_failed": "2025-05-29T15:50:34.629968", "last_failed": "2025-05-29T15:50:34.629968", "fail_count": 1, "last_error": "HTTPConnectionPool(host='************', port=80): Max retries exceeded with url: http://httpbin.org/ip (Caused by ProxyError('Unable to connect to proxy', ConnectTimeoutError(<urllib3.connection.HTTPConnection object at 0x00000246A2267810>, 'Connection to ************ timed out. (connect timeout=10)')))"}, {"key": "************:8080", "host": "************", "port": 8080, "first_failed": "2025-05-29T15:50:34.629968", "last_failed": "2025-05-29T15:50:34.629968", "fail_count": 1, "last_error": "HTTPConnectionPool(host='************', port=8080): Max retries exceeded with url: http://httpbin.org/ip (Caused by ProxyError('Unable to connect to proxy', ConnectTimeoutError(<urllib3.connection.HTTPConnection object at 0x00000246A2296250>, 'Connection to ************ timed out. (connect timeout=10)')))"}, {"key": "*************:80", "host": "*************", "port": 80, "first_failed": "2025-05-29T15:50:35.057682", "last_failed": "2025-05-29T15:50:35.057682", "fail_count": 1, "last_error": "HTTPConnectionPool(host='*************', port=80): Max retries exceeded with url: http://httpbin.org/ip (Caused by ProxyError('Unable to connect to proxy', RemoteDisconnected('Remote end closed connection without response')))"}, {"key": "*************:8082", "host": "*************", "port": 8082, "first_failed": "2025-05-29T15:50:36.776699", "last_failed": "2025-05-29T15:50:36.776699", "fail_count": 1, "last_error": "HTTPConnectionPool(host='*************', port=8082): Max retries exceeded with url: http://httpbin.org/ip (Caused by ProxyError('Unable to connect to proxy', ConnectTimeoutError(<urllib3.connection.HTTPConnection object at 0x00000246A4198E90>, 'Connection to ************* timed out. (connect timeout=10)')))"}, {"key": "************:2020", "host": "************", "port": 2020, "first_failed": "2025-05-29T15:50:36.840692", "last_failed": "2025-05-29T15:50:36.840692", "fail_count": 1, "last_error": "HTTPConnectionPool(host='************', port=2020): Max retries exceeded with url: http://httpbin.org/ip (Caused by ProxyError('Unable to connect to proxy', ConnectTimeoutError(<urllib3.connection.HTTPConnection object at 0x00000246A413E090>, 'Connection to ************ timed out. (connect timeout=10)')))"}, {"key": "*************:45", "host": "*************", "port": 45, "first_failed": "2025-05-29T15:50:37.652281", "last_failed": "2025-05-29T15:50:37.652281", "fail_count": 1, "last_error": "HTTPConnectionPool(host='*************', port=45): Max retries exceeded with url: http://httpbin.org/ip (Caused by ProxyError('Unable to connect to proxy', ConnectTimeoutError(<urllib3.connection.HTTPConnection object at 0x00000246A2271350>, 'Connection to ************* timed out. (connect timeout=10)')))"}, {"key": "*************:1111", "host": "*************", "port": 1111, "first_failed": "2025-05-29T15:50:37.919022", "last_failed": "2025-05-29T15:50:37.919022", "fail_count": 1, "last_error": "HTTPConnectionPool(host='*************', port=1111): Max retries exceeded with url: http://httpbin.org/ip (Caused by ProxyError('Unable to connect to proxy', ConnectTimeoutError(<urllib3.connection.HTTPConnection object at 0x00000246A22A5610>, 'Connection to ************* timed out. (connect timeout=10)')))"}, {"key": "***********:8008", "host": "***********", "port": 8008, "first_failed": "2025-05-29T15:50:38.355501", "last_failed": "2025-05-29T15:50:38.355501", "fail_count": 1, "last_error": "HTTPConnectionPool(host='***********', port=8008): Max retries exceeded with url: http://httpbin.org/ip (Caused by ProxyError('Unable to connect to proxy', ConnectTimeoutError(<urllib3.connection.HTTPConnection object at 0x00000246A412B910>, 'Connection to *********** timed out. (connect timeout=10)')))"}, {"key": "************:1234", "host": "************", "port": 1234, "first_failed": "2025-05-29T15:50:38.885732", "last_failed": "2025-05-29T15:50:38.885732", "fail_count": 1, "last_error": "HTTPConnectionPool(host='************', port=1234): Max retries exceeded with url: http://httpbin.org/ip (Caused by ProxyError('Unable to connect to proxy', ConnectTimeoutError(<urllib3.connection.HTTPConnection object at 0x00000246A2271410>, 'Connection to ************ timed out. (connect timeout=10)')))"}, {"key": "***************:80", "host": "***************", "port": 80, "first_failed": "2025-05-29T15:50:39.434318", "last_failed": "2025-05-29T15:50:39.434318", "fail_count": 1, "last_error": "HTTPConnectionPool(host='***************', port=80): Max retries exceeded with url: http://httpbin.org/ip (Caused by ProxyError('Unable to connect to proxy', ConnectTimeoutError(<urllib3.connection.HTTPConnection object at 0x00000246A419BF90>, 'Connection to *************** timed out. (connect timeout=10)')))"}, {"key": "**************:8080", "host": "**************", "port": 8080, "first_failed": "2025-05-29T15:50:39.479152", "last_failed": "2025-05-29T15:50:39.479152", "fail_count": 1, "last_error": "HTTPConnectionPool(host='**************', port=8080): Max retries exceeded with url: http://httpbin.org/ip (Caused by ProxyError('Unable to connect to proxy', ConnectTimeoutError(<urllib3.connection.HTTPConnection object at 0x00000246A2353690>, 'Connection to ************** timed out. (connect timeout=10)')))"}, {"key": "***********:9050", "host": "***********", "port": 9050, "first_failed": "2025-05-29T15:50:39.480046", "last_failed": "2025-05-29T15:50:39.480046", "fail_count": 1, "last_error": "HTTPConnectionPool(host='***********', port=9050): Max retries exceeded with url: http://httpbin.org/ip (Caused by ProxyError('Unable to connect to proxy', ConnectTimeoutError(<urllib3.connection.HTTPConnection object at 0x00000246A40FD090>, 'Connection to *********** timed out. (connect timeout=10)')))"}, {"key": "*************:3129", "host": "*************", "port": 3129, "first_failed": "2025-05-29T15:50:39.586727", "last_failed": "2025-05-29T15:50:39.586727", "fail_count": 1, "last_error": "HTTPConnectionPool(host='*************', port=3129): Max retries exceeded with url: http://httpbin.org/ip (Caused by ProxyError('Unable to connect to proxy', ConnectTimeoutError(<urllib3.connection.HTTPConnection object at 0x00000246A23501D0>, 'Connection to ************* timed out. (connect timeout=10)')))"}, {"key": "*************:443", "host": "*************", "port": 443, "first_failed": "2025-05-29T15:50:43.431153", "last_failed": "2025-05-29T15:50:43.431153", "fail_count": 1, "last_error": "HTTPConnectionPool(host='*************', port=443): Max retries exceeded with url: http://httpbin.org/ip (Caused by ProxyError('Unable to connect to proxy', ConnectTimeoutError(<urllib3.connection.HTTPConnection object at 0x00000246A22A3490>, 'Connection to ************* timed out. (connect timeout=10)')))"}, {"key": "*************:8081", "host": "*************", "port": 8081, "first_failed": "2025-05-29T15:50:44.653003", "last_failed": "2025-05-29T15:50:44.653003", "fail_count": 1, "last_error": "HTTPConnectionPool(host='*************', port=8081): Max retries exceeded with url: http://httpbin.org/ip (Caused by ProxyError('Unable to connect to proxy', ConnectTimeoutError(<urllib3.connection.HTTPConnection object at 0x00000246A248EBD0>, 'Connection to ************* timed out. (connect timeout=10)')))"}], "last_updated": "2025-05-29T15:50:44.653003"}