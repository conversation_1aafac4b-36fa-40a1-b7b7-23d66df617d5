using System;
using System.Threading.Tasks;
using System.Threading;
using System.IO;
using OpenQA.Selenium;
using OpenQA.Selenium.Chrome;
using OpenQA.Selenium.Support.UI;
using ChromeAutoManager.Models;
using ChromeAutoManager.Core;

namespace ChromeAutoManager.Services
{
    /// <summary>
    /// Service quản lý trình duyệt Chrome
    /// </summary>
    public class BrowserManager : IDisposable
    {
        private ChromeDriver? _driver;
        private readonly ProxyInfo? _proxy;
        private readonly Random _random;
        private bool _disposed = false;

        public BrowserManager(ProxyInfo? proxy = null)
        {
            _proxy = proxy;
            _random = new Random();
        }

        /// <summary>
        /// Kết nối với trình duyệt hiện có
        /// </summary>
        public bool ConnectToExistingBrowser()
        {
            try
            {
                var options = new ChromeOptions();
                options.AddArgument($"--remote-debugging-port={AppConfig.Instance.Browser.DebugPort}");
                options.DebuggerAddress = $"127.0.0.1:{AppConfig.Instance.Browser.DebugPort}";

                var service = ChromeDriverService.CreateDefaultService();
                service.HideCommandPromptWindow = true;

                _driver = new ChromeDriver(service, options);
                
                // Cấu hình timeout
                _driver.Manage().Timeouts().PageLoad = TimeSpan.FromSeconds(AppConfig.Instance.Browser.PageLoadTimeout);
                _driver.Manage().Timeouts().ImplicitWait = TimeSpan.FromSeconds(AppConfig.Instance.Browser.ImplicitWait);

                Logger.LogBrowser("Connect to existing browser", true);
                return true;
            }
            catch (Exception ex)
            {
                Logger.LogBrowser("Connect to existing browser", false, ex.Message);
                return false;
            }
        }

        /// <summary>
        /// Tạo trình duyệt mới
        /// </summary>
        public bool CreateBrowser()
        {
            // Thử kết nối với browser hiện có trước
            if (AppConfig.Instance.Browser.UseExistingBrowser)
            {
                if (ConnectToExistingBrowser())
                {
                    return true;
                }
                Logger.Info("Không thể kết nối browser hiện có, tạo browser mới...");
            }

            try
            {
                var options = new ChromeOptions();

                // Cấu hình cơ bản
                if (AppConfig.Instance.Browser.Headless)
                {
                    options.AddArgument("--headless");
                }

                options.AddArgument($"--window-size={AppConfig.Instance.Browser.WindowWidth},{AppConfig.Instance.Browser.WindowHeight}");
                options.AddArgument("--no-sandbox");
                options.AddArgument("--disable-dev-shm-usage");
                options.AddArgument("--disable-gpu");
                options.AddArgument("--disable-extensions");
                options.AddArgument("--disable-plugins");

                if (AppConfig.Instance.Browser.DisableImages)
                {
                    options.AddArgument("--disable-images");
                }

                // User Agent ngẫu nhiên
                if (AppConfig.Instance.Browser.UserAgentRotation)
                {
                    var userAgents = new[]
                    {
                        "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
                        "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/118.0.0.0 Safari/537.36",
                        "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
                    };
                    var userAgent = userAgents[_random.Next(userAgents.Length)];
                    options.AddArgument($"--user-agent={userAgent}");
                }

                // Cấu hình proxy
                if (_proxy != null)
                {
                    options.AddArgument($"--proxy-server=http://{_proxy.FullAddress}");
                    Logger.Info("Sử dụng proxy: {Proxy}", _proxy.FullAddress);
                }

                // Tắt thông báo
                options.AddUserProfilePreference("profile.default_content_setting_values.notifications", 2);
                options.AddUserProfilePreference("profile.default_content_setting_values.geolocation", 2);
                options.AddUserProfilePreference("profile.default_content_setting_values.media_stream", 2);

                // Tạo service
                var service = ChromeDriverService.CreateDefaultService();
                service.HideCommandPromptWindow = true;

                // Tạo driver
                _driver = new ChromeDriver(service, options);

                // Cấu hình timeout
                _driver.Manage().Timeouts().PageLoad = TimeSpan.FromSeconds(AppConfig.Instance.Browser.PageLoadTimeout);
                _driver.Manage().Timeouts().ImplicitWait = TimeSpan.FromSeconds(AppConfig.Instance.Browser.ImplicitWait);

                Logger.LogBrowser("Create new browser", true);
                return true;
            }
            catch (Exception ex)
            {
                Logger.LogBrowser("Create new browser", false, ex.Message);
                return false;
            }
        }

        /// <summary>
        /// Điều hướng đến URL
        /// </summary>
        public bool NavigateToUrl(string url)
        {
            try
            {
                if (_driver == null)
                {
                    Logger.Error("Driver chưa được khởi tạo");
                    return false;
                }

                _driver.Navigate().GoToUrl(url);
                Logger.LogBrowser("Navigate", true, url);
                return true;
            }
            catch (WebDriverTimeoutException)
            {
                Logger.LogBrowser("Navigate", false, $"Timeout khi load trang: {url}");
                return false;
            }
            catch (Exception ex)
            {
                Logger.LogBrowser("Navigate", false, $"Lỗi khi điều hướng: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Chờ element xuất hiện
        /// </summary>
        public IWebElement? WaitForElement(string selector, By? by = null, int timeout = 10)
        {
            try
            {
                if (_driver == null) return null;

                var wait = new WebDriverWait(_driver, TimeSpan.FromSeconds(timeout));
                var locator = by ?? By.CssSelector(selector);
                return wait.Until(driver => driver.FindElement(locator));
            }
            catch (WebDriverTimeoutException)
            {
                Logger.Warning("Không tìm thấy element: {Selector}", selector);
                return null;
            }
            catch (Exception ex)
            {
                Logger.Error(ex, "Lỗi khi chờ element: {Selector}", selector);
                return null;
            }
        }

        /// <summary>
        /// Chờ element có thể click
        /// </summary>
        public IWebElement? WaitForClickable(string selector, By? by = null, int timeout = 10)
        {
            try
            {
                if (_driver == null) return null;

                var wait = new WebDriverWait(_driver, TimeSpan.FromSeconds(timeout));
                var locator = by ?? By.CssSelector(selector);
                return wait.Until(SeleniumExtras.WaitHelpers.ExpectedConditions.ElementToBeClickable(locator));
            }
            catch (WebDriverTimeoutException)
            {
                Logger.Warning("Element không thể click: {Selector}", selector);
                return null;
            }
            catch (Exception ex)
            {
                Logger.Error(ex, "Lỗi khi chờ element clickable: {Selector}", selector);
                return null;
            }
        }

        /// <summary>
        /// Nhập text an toàn với delay
        /// </summary>
        public async Task<bool> SafeSendKeysAsync(IWebElement element, string text, bool clearFirst = true)
        {
            try
            {
                if (clearFirst)
                {
                    element.Clear();
                }

                // Nhập từng ký tự với delay ngẫu nhiên
                foreach (char c in text)
                {
                    element.SendKeys(c.ToString());
                    await Task.Delay(_random.Next(50, 150));
                }

                // Delay sau khi nhập xong
                await RandomDelayAsync();
                return true;
            }
            catch (Exception ex)
            {
                Logger.Error(ex, "Lỗi khi nhập text: {Text}", text);
                return false;
            }
        }

        /// <summary>
        /// Click an toàn với delay
        /// </summary>
        public async Task<bool> SafeClickAsync(IWebElement element)
        {
            try
            {
                if (_driver == null) return false;

                // Scroll đến element
                ((IJavaScriptExecutor)_driver).ExecuteScript("arguments[0].scrollIntoView(true);", element);
                await Task.Delay(500);

                // Click
                element.Click();
                await RandomDelayAsync();
                return true;
            }
            catch (Exception ex)
            {
                Logger.Error(ex, "Lỗi khi click element");
                return false;
            }
        }

        /// <summary>
        /// Delay ngẫu nhiên
        /// </summary>
        public async Task RandomDelayAsync(double? minDelay = null, double? maxDelay = null)
        {
            var min = minDelay ?? AppConfig.Instance.Registration.DelayBetweenActionsMin;
            var max = maxDelay ?? AppConfig.Instance.Registration.DelayBetweenActionsMax;
            
            var delay = _random.NextDouble() * (max - min) + min;
            await Task.Delay(TimeSpan.FromSeconds(delay));
        }

        /// <summary>
        /// Chụp màn hình
        /// </summary>
        public bool TakeScreenshot(string filename)
        {
            try
            {
                if (_driver == null) return false;

                // Tạo thư mục nếu chưa có
                var directory = Path.GetDirectoryName(filename);
                if (!string.IsNullOrEmpty(directory) && !Directory.Exists(directory))
                {
                    Directory.CreateDirectory(directory);
                }

                var screenshot = ((ITakesScreenshot)_driver).GetScreenshot();
                screenshot.SaveAsFile(filename);
                
                Logger.Info("Đã chụp màn hình: {Filename}", filename);
                return true;
            }
            catch (Exception ex)
            {
                Logger.Error(ex, "Lỗi khi chụp màn hình: {Filename}", filename);
                return false;
            }
        }

        /// <summary>
        /// Lấy source code trang
        /// </summary>
        public string? GetPageSource()
        {
            try
            {
                return _driver?.PageSource;
            }
            catch (Exception ex)
            {
                Logger.Error(ex, "Lỗi khi lấy page source");
                return null;
            }
        }

        /// <summary>
        /// Thực thi JavaScript
        /// </summary>
        public object? ExecuteScript(string script)
        {
            try
            {
                if (_driver == null) return null;
                return ((IJavaScriptExecutor)_driver).ExecuteScript(script);
            }
            catch (Exception ex)
            {
                Logger.Error(ex, "Lỗi khi thực thi script: {Script}", script);
                return null;
            }
        }

        /// <summary>
        /// Đóng trình duyệt
        /// </summary>
        public void CloseBrowser()
        {
            try
            {
                _driver?.Quit();
                Logger.LogBrowser("Close browser", true);
            }
            catch (Exception ex)
            {
                Logger.LogBrowser("Close browser", false, ex.Message);
            }
        }

        public void Dispose()
        {
            if (!_disposed)
            {
                CloseBrowser();
                _disposed = true;
            }
        }
    }
}
