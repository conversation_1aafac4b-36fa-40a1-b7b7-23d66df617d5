#!/usr/bin/env python3
"""
Quick test main tool với 1 tài khoản
"""

import os
import sys

# Thêm src vào path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def main():
    """Quick test main tool"""
    print("🚀 QUICK TEST MAIN TOOL")
    print("=" * 50)
    
    try:
        from win13_registration_service import Win13RegistrationService
        from proxy_manager import ProxyManager
        
        # Setup
        print("🔧 Setting up services...")
        proxy_manager = ProxyManager()
        registration_service = Win13RegistrationService(proxy_manager)
        
        print("📊 Proxy status:")
        print(f"  Total proxies: {len(proxy_manager.proxies)}")
        print(f"  Working proxies: {proxy_manager.get_working_proxy_count()}")
        
        # Test single registration
        print("\n🎯 Testing single account registration...")
        
        def update_callback(result):
            """Callback để hiển thị kết quả"""
            account = result.get('account', {})
            username = account.get('username', 'N/A')
            success = result.get('success', False)
            error = result.get('error', '')
            
            if success:
                print(f"✅ SUCCESS: {username}")
            else:
                print(f"❌ FAILED: {username} - {error}")
        
        # Chạy đăng ký 1 tài khoản
        results = registration_service.register_multiple_accounts(
            num_accounts=1,
            max_concurrent=1,
            callback=update_callback
        )
        
        # Hiển thị kết quả
        print("\n📊 RESULTS:")
        stats = registration_service.get_registration_stats()
        print(f"Total: {stats['total']}")
        print(f"Success: {stats['success']}")
        print(f"Failed: {stats['failed']}")
        print(f"Success rate: {stats['success_rate']}%")
        
        if stats['success'] > 0:
            print("\n🎉 TEST SUCCESSFUL!")
            print("Tool is working correctly with proxy fallback.")
        else:
            print("\n⚠️ TEST FAILED!")
            print("Please check the logs above for issues.")
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
