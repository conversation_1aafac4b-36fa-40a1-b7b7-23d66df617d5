#!/usr/bin/env python3
"""
Test hoàn chỉnh form đăng ký 13win với tất cả fields
"""

import os
import sys
import time

# Thêm src vào path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.support.ui import WebDriverWait

def test_complete_form():
    """Test điền đầy đủ form 13win"""
    print("🔍 TEST COMPLETE 13WIN FORM")
    print("=" * 50)
    
    driver = None
    try:
        # Setup Chrome
        options = Options()
        options.add_argument("--no-sandbox")
        options.add_argument("--disable-dev-shm-usage")
        options.add_argument("--disable-gpu")
        options.add_argument("--disable-images")
        
        # ChromeDriver
        chromedriver_path = "chromedriver.exe"
        if os.path.exists(chromedriver_path):
            service = Service(chromedriver_path)
            print("✓ Sử dụng ChromeDriver local")
        else:
            print("❌ Không tìm thấy chromedriver.exe")
            return False
        
        print("🚀 Khởi tạo Chrome...")
        driver = webdriver.Chrome(service=service, options=options)
        print("✓ Chrome đã sẵn sàng")
        
        # Truy cập 13win
        url = "https://13win16.com"
        print(f"\n📍 Truy cập: {url}")
        driver.get(url)
        
        # Chờ page load
        print("⏳ Chờ trang load...")
        WebDriverWait(driver, 15).until(
            lambda d: d.execute_script("return document.readyState") == "complete"
        )
        
        current_url = driver.current_url
        print(f"✓ Current URL: {current_url}")
        
        # Chờ JS load form
        print("⏳ Chờ JavaScript load form...")
        time.sleep(5)
        
        # Tìm tất cả inputs
        inputs = driver.find_elements(By.TAG_NAME, "input")
        print(f"\n📝 Tìm thấy {len(inputs)} inputs:")
        
        # Phân loại inputs
        username_input = None
        password_input = None
        confirm_password_input = None
        full_name_input = None
        checkbox_input = None
        
        for i, inp in enumerate(inputs):
            try:
                placeholder = inp.get_attribute("placeholder") or ""
                type_attr = inp.get_attribute("type") or "text"
                
                print(f"  {i+1}. type='{type_attr}', placeholder='{placeholder}'")
                
                # Phân loại theo placeholder
                if "điện thoại" in placeholder.lower() or "đăng nhập" in placeholder.lower():
                    username_input = inp
                    print(f"      → USERNAME FIELD")
                elif "mật khẩu" in placeholder.lower() and "xác nhận" not in placeholder.lower():
                    password_input = inp
                    print(f"      → PASSWORD FIELD")
                elif "xác nhận" in placeholder.lower() and "mật khẩu" in placeholder.lower():
                    confirm_password_input = inp
                    print(f"      → CONFIRM PASSWORD FIELD")
                elif "họ tên" in placeholder.lower() or "tên thật" in placeholder.lower():
                    full_name_input = inp
                    print(f"      → FULL NAME FIELD")
                elif type_attr == "checkbox":
                    checkbox_input = inp
                    print(f"      → CHECKBOX FIELD")
                    
            except Exception as e:
                print(f"  {i+1}. Error: {e}")
        
        # Tìm button
        buttons = driver.find_elements(By.TAG_NAME, "button")
        submit_button = None
        
        print(f"\n🔘 Tìm thấy {len(buttons)} button(s):")
        for i, btn in enumerate(buttons):
            try:
                text = btn.text.strip()
                print(f"  {i+1}. text='{text}'")
                if "ĐĂNG KÝ" in text.upper():
                    submit_button = btn
                    print(f"      → SUBMIT BUTTON")
            except Exception as e:
                print(f"  {i+1}. Error: {e}")
        
        # Test data
        test_data = {
            'username': 'testuser12345',
            'password': 'TestPass123!',
            'full_name': 'TRAN HOANG AN'
        }
        
        print(f"\n✏️ Test điền form với data:")
        print(f"  Username: {test_data['username']}")
        print(f"  Password: {test_data['password']}")
        print(f"  Full Name: {test_data['full_name']}")
        
        filled_count = 0
        
        # Điền username
        if username_input:
            try:
                driver.execute_script("arguments[0].scrollIntoView(true);", username_input)
                time.sleep(0.5)
                driver.execute_script("arguments[0].style.border='3px solid red'", username_input)
                username_input.clear()
                username_input.send_keys(test_data['username'])
                print("✅ Đã điền username")
                filled_count += 1
                time.sleep(1)
            except Exception as e:
                print(f"❌ Lỗi điền username: {e}")
        else:
            print("❌ Không tìm thấy username field")
        
        # Điền password
        if password_input:
            try:
                driver.execute_script("arguments[0].scrollIntoView(true);", password_input)
                time.sleep(0.5)
                driver.execute_script("arguments[0].style.border='3px solid blue'", password_input)
                password_input.clear()
                password_input.send_keys(test_data['password'])
                print("✅ Đã điền password")
                filled_count += 1
                time.sleep(1)
            except Exception as e:
                print(f"❌ Lỗi điền password: {e}")
        else:
            print("❌ Không tìm thấy password field")
        
        # Điền confirm password
        if confirm_password_input:
            try:
                driver.execute_script("arguments[0].scrollIntoView(true);", confirm_password_input)
                time.sleep(0.5)
                driver.execute_script("arguments[0].style.border='3px solid green'", confirm_password_input)
                confirm_password_input.clear()
                confirm_password_input.send_keys(test_data['password'])  # Same as password
                print("✅ Đã điền confirm password")
                filled_count += 1
                time.sleep(1)
            except Exception as e:
                print(f"❌ Lỗi điền confirm password: {e}")
        else:
            print("❌ Không tìm thấy confirm password field")
        
        # Điền full name
        if full_name_input:
            try:
                driver.execute_script("arguments[0].scrollIntoView(true);", full_name_input)
                time.sleep(0.5)
                driver.execute_script("arguments[0].style.border='3px solid orange'", full_name_input)
                full_name_input.clear()
                full_name_input.send_keys(test_data['full_name'])
                print("✅ Đã điền full name")
                filled_count += 1
                time.sleep(1)
            except Exception as e:
                print(f"❌ Lỗi điền full name: {e}")
        else:
            print("❌ Không tìm thấy full name field")
        
        # Tick checkbox
        if checkbox_input:
            try:
                if not checkbox_input.is_selected():
                    driver.execute_script("arguments[0].scrollIntoView(true);", checkbox_input)
                    time.sleep(0.5)
                    driver.execute_script("arguments[0].style.border='3px solid purple'", checkbox_input)
                    driver.execute_script("arguments[0].click();", checkbox_input)
                    print("✅ Đã tick checkbox")
                    time.sleep(1)
                else:
                    print("✅ Checkbox đã được tick")
            except Exception as e:
                print(f"❌ Lỗi tick checkbox: {e}")
        else:
            print("❌ Không tìm thấy checkbox")
        
        print(f"\n📊 Kết quả điền form: {filled_count}/4 fields")
        
        # Test submit button
        if submit_button and filled_count >= 3:
            print("\n🚀 Test submit button...")
            try:
                driver.execute_script("arguments[0].scrollIntoView(true);", submit_button)
                time.sleep(0.5)
                driver.execute_script("arguments[0].style.border='5px solid lime'", submit_button)
                print("✅ Submit button đã được highlight")
                print("⚠️ KHÔNG CLICK SUBMIT để tránh đăng ký thật")
                # driver.execute_script("arguments[0].click();", submit_button)
            except Exception as e:
                print(f"❌ Lỗi highlight submit: {e}")
        else:
            print("⚠️ Không test submit (thiếu fields hoặc không tìm thấy button)")
        
        # Chờ để user xem
        print("\n⏳ Chờ 20 giây để bạn xem kết quả...")
        time.sleep(20)
        
        return filled_count >= 3
        
    except Exception as e:
        print(f"❌ Lỗi: {e}")
        return False
        
    finally:
        if driver:
            try:
                driver.quit()
                print("✓ Đã đóng browser")
            except:
                pass

def main():
    """Main function"""
    try:
        success = test_complete_form()
        
        if success:
            print("\n🎉 TEST THÀNH CÔNG!")
            print("Form 13win có thể điền đầy đủ.")
            print("Tool sẽ hoạt động bình thường với auto-submit.")
        else:
            print("\n❌ TEST THẤT BẠI!")
            print("Cần kiểm tra lại field detection.")
            
    except KeyboardInterrupt:
        print("\n\nĐã hủy bởi người dùng.")
    except Exception as e:
        print(f"\nLỗi: {e}")

if __name__ == "__main__":
    main()
