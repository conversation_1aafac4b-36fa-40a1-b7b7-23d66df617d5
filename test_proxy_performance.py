#!/usr/bin/env python3
"""
Test performance của proxy testing
"""

import sys
import os
import time
import logging

# Add src to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def test_proxy_performance():
    """Test proxy testing performance"""
    print("=== TEST PROXY PERFORMANCE ===")
    
    try:
        from proxy_manager import ProxyManager
        
        # Setup logging
        logging.basicConfig(level=logging.WARNING)  # Reduce noise
        
        # Create manager
        print("1. Tạo ProxyManager...")
        manager = ProxyManager()
        
        # Add some test proxies
        print("2. Thêm test proxies...")
        test_proxies = [
            ("*******", 80),
            ("*******", 80), 
            ("127.0.0.1", 8080),
            ("***********", 80),
            ("********", 80)
        ]
        
        for host, port in test_proxies:
            manager.add_proxy(host, port)
        
        print(f"✓ Đã thêm {len(test_proxies)} test proxies")
        
        # Test with reduced workers
        print("3. Test với 5 workers...")
        start_time = time.time()
        
        def progress_callback(tested, total, working):
            print(f"   Progress: {tested}/{total} ({tested/total*100:.1f}%) - Working: {working}")
        
        working_count = manager.test_all_proxies(max_workers=5, progress_callback=progress_callback)
        
        end_time = time.time()
        duration = end_time - start_time
        
        print(f"✓ Test hoàn thành trong {duration:.2f} giây")
        print(f"✓ Working proxies: {working_count}")
        
        # Test stats
        stats = manager.get_stats()
        print(f"✓ Stats: {stats}")
        
        if duration < 30:  # Should complete within 30 seconds
            print("\n🎉 PROXY TEST PERFORMANCE OK!")
            return True
        else:
            print(f"\n⚠️ Test chậm ({duration:.2f}s > 30s)")
            return False
        
    except Exception as e:
        print(f"❌ Lỗi test: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_cache_disabled():
    """Test cache được disable đúng cách"""
    print("\n=== TEST CACHE DISABLE ===")
    
    try:
        from proxy_manager import ProxyManager
        
        manager = ProxyManager()
        
        # Check cache initially exists
        print("1. Check cache ban đầu...")
        if manager.proxy_cache:
            print("✓ Cache tồn tại ban đầu")
        else:
            print("⚠️ Cache không tồn tại ban đầu")
        
        # Simulate test_all_proxies cache disable
        print("2. Simulate disable cache...")
        cache_backup = manager.proxy_cache
        manager.proxy_cache = None
        
        if manager.proxy_cache is None:
            print("✓ Cache đã được disable")
        else:
            print("❌ Cache chưa được disable")
            return False
        
        # Restore cache
        print("3. Restore cache...")
        manager.proxy_cache = cache_backup
        
        if manager.proxy_cache:
            print("✓ Cache đã được restore")
        else:
            print("❌ Cache chưa được restore")
            return False
        
        print("\n🎉 CACHE DISABLE/RESTORE OK!")
        return True
        
    except Exception as e:
        print(f"❌ Lỗi test cache: {e}")
        return False

def main():
    """Main test function"""
    print("🧪 TEST PROXY PERFORMANCE & CACHE\n")
    
    results = []
    
    # Test cache disable/restore
    results.append(test_cache_disabled())
    
    # Test proxy performance
    results.append(test_proxy_performance())
    
    # Summary
    print(f"\n{'='*50}")
    print("📊 KẾT QUẢ:")
    print(f"✓ Thành công: {sum(results)}/{len(results)}")
    print(f"❌ Thất bại: {len(results) - sum(results)}/{len(results)}")
    
    if all(results):
        print("\n🎉 TẤT CẢ TEST THÀNH CÔNG!")
        print("Tool sẽ không bị treo khi test proxy nữa!")
        return True
    else:
        print("\n⚠️ CÓ LỖI CẦN FIX")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
