"""
<PERSON><PERSON><PERSON><PERSON> lý proxy cho đăng ký tài khoản
"""

import requests
import random
import time
import logging
import os
from concurrent.futures import ThreadPoolExecutor, as_completed
import threading
import re
from proxy_cache import ProxyCache

class ProxyInfo:
    def __init__(self, host, port, username=None, password=None, proxy_type="http"):
        self.host = host
        self.port = port
        self.username = username
        self.password = password
        self.type = proxy_type
        self.is_working = False
        self.response_time = 0
        self.in_use = False
        self.last_tested = None

    @property
    def full_address(self):
        return f"{self.host}:{self.port}"

    def to_dict(self):
        return {
            'type': self.type,
            'host': self.host,
            'port': self.port,
            'username': self.username,
            'password': self.password
        }

class ProxyManager:
    def __init__(self):
        self.proxies = []
        self.working_proxies = []
        self.lock = threading.Lock()
        self.logger = logging.getLogger(__name__)

        # Country filtering for 13win (Vietnam first, block US/EU)
        self.preferred_countries = ['VN', 'TH', 'SG', 'MY', 'ID', 'PH', 'KH', 'LA', 'MM']
        self.blocked_countries = ['US', 'UK', 'CA', 'AU', 'DE', 'FR', 'NL', 'IT', 'ES']
        self.vietnam_proxies = []  # Separate list for Vietnam proxies

        # Initialize proxy cache with error handling
        self.proxy_cache = None
        try:
            self.proxy_cache = ProxyCache()
            self.logger.info("✓ Proxy cache khởi tạo thành công")
            # Load cached working proxies at startup
            self.load_cached_proxies()
        except Exception as e:
            self.logger.warning(f"Proxy cache disabled: {e}")
            self.logger.info("Tool sẽ hoạt động bình thường không có cache")

    def load_cached_proxies(self):
        """Load proxy đã cache từ lần trước"""
        try:
            cached_proxies = self.proxy_cache.get_working_proxies(max_age_hours=24)

            for cached_proxy in cached_proxies:
                # Convert cache entry to ProxyInfo
                proxy = ProxyInfo(
                    cached_proxy['host'],
                    cached_proxy['port'],
                    cached_proxy.get('username'),
                    cached_proxy.get('password'),
                    cached_proxy.get('type', 'http')
                )
                proxy.is_working = True
                proxy.response_time = cached_proxy.get('response_time', 0)

                # Detect country for cached proxies
                proxy.country = self._detect_country_from_ip(proxy.host)
                if proxy.country == 'VN':
                    if proxy not in self.vietnam_proxies:
                        self.vietnam_proxies.append(proxy)

                # Add to working proxies if not already there
                if proxy not in self.working_proxies:
                    self.working_proxies.append(proxy)

                # Add to all proxies if not already there
                if proxy not in self.proxies:
                    self.proxies.append(proxy)

            if cached_proxies:
                vietnam_count = len([p for p in cached_proxies if self._detect_country_from_ip(p['host']) == 'VN'])
                self.logger.info(f"✓ Loaded {len(cached_proxies)} cached working proxies ({vietnam_count} Vietnam)")
            else:
                self.logger.info("📝 No cached proxies found")

            # Load Vietnam free proxies if no Vietnam proxies in cache
            if len(self.vietnam_proxies) == 0:
                self.load_vietnam_free_proxies()

        except Exception as e:
            self.logger.error(f"Lỗi load cached proxies: {e}")
            # Fallback: Load Vietnam free proxies
            self.load_vietnam_free_proxies()

    def load_vietnam_free_proxies(self):
        """Load Vietnam free proxies từ file hoặc online"""
        try:
            self.logger.info("🇻🇳 Loading Vietnam free proxies...")

            # Try to load from local file first
            vietnam_file = "vietnam_proxies_free.txt"
            if os.path.exists(vietnam_file):
                loaded_count = self.load_proxies_from_file(vietnam_file)
                if loaded_count > 0:
                    self.logger.info(f"✅ Loaded {loaded_count} Vietnam proxies from local file")
                    return loaded_count

            # Fallback: Create Vietnam proxies from known working IPs
            vietnam_proxies_data = [
                # High-speed Vietnam SOCKS proxies
                ("************", 1080, "socks4"),
                ("************", 1080, "socks4"),
                ("************", 1080, "socks4"),
                ("*************", 1080, "socks4"),
                ("*************", 1080, "socks4"),
                ("************", 1080, "socks4"),
                ("*************", 1080, "socks4"),

                # Vietnam HTTP proxies
                ("*************", 8080, "http"),
                ("**************", 8080, "http"),
                ("*************", 8080, "http"),
                ("*************", 1080, "socks5"),
                ("**************", 8888, "http"),
                ("**************", 53281, "http"),
                ("***************", 8080, "http"),
                ("**************", 1007, "http"),

                # Vietnam regional proxies
                ("***************", 1080, "socks4"),
                ("*************", 5678, "socks4"),
                ("*************", 5678, "socks4"),
                ("**************", 7777, "http"),
                ("*************", 60009, "http"),
                ("*************", 1080, "socks4"),
                ("**************", 1080, "socks4"),
                ("**************", 1080, "socks4"),
                ("*************", 10001, "http"),
                ("*************", 1080, "socks5"),
            ]

            loaded_count = 0
            for host, port, proxy_type in vietnam_proxies_data:
                proxy = ProxyInfo(host, port, proxy_type=proxy_type)
                proxy.country = 'VN'  # Mark as Vietnam
                proxy.is_working = True  # Assume working (will be tested later)

                # Add to all lists
                if proxy not in self.proxies:
                    self.proxies.append(proxy)
                if proxy not in self.working_proxies:
                    self.working_proxies.append(proxy)
                if proxy not in self.vietnam_proxies:
                    self.vietnam_proxies.append(proxy)

                loaded_count += 1

            self.logger.info(f"🇻🇳 Added {loaded_count} Vietnam free proxies")
            return loaded_count

        except Exception as e:
            self.logger.error(f"Lỗi load Vietnam free proxies: {e}")
            return 0

    def load_proxies_from_file(self, filename):
        """Load proxy từ file"""
        try:
            self.logger.info(f"Bắt đầu load proxy từ file: {filename}")

            with open(filename, 'r', encoding='utf-8') as f:
                lines = f.readlines()

            total_lines = len(lines)
            loaded_count = 0
            skipped_count = 0
            error_count = 0

            self.logger.info(f"Đọc được {total_lines} dòng từ file")

            for line_num, line in enumerate(lines, 1):
                line = line.strip()
                if not line or line.startswith('#'):
                    skipped_count += 1
                    continue

                proxy = self._parse_proxy_line(line)
                if proxy:
                    self.proxies.append(proxy)
                    loaded_count += 1

                    # Nếu proxy đã được test và working, thêm vào working_proxies
                    if hasattr(proxy, 'is_working') and proxy.is_working:
                        if proxy not in self.working_proxies:
                            self.working_proxies.append(proxy)

                    if loaded_count % 50 == 0:
                        self.logger.info(f"Đã load {loaded_count} proxy...")
                else:
                    error_count += 1
                    self.logger.debug(f"Dòng {line_num} không hợp lệ: {line}")

            self.logger.info(f"Hoàn thành load proxy từ file {filename}:")
            self.logger.info(f"  - Tổng dòng: {total_lines}")
            self.logger.info(f"  - Đã load: {loaded_count}")
            self.logger.info(f"  - Bỏ qua: {skipped_count}")
            self.logger.info(f"  - Lỗi: {error_count}")

            return loaded_count

        except FileNotFoundError:
            self.logger.warning(f"File proxy không tồn tại: {filename}")
            return 0
        except Exception as e:
            self.logger.error(f"Lỗi load proxy từ file: {str(e)}")
            return 0

    def _parse_proxy_line(self, line):
        """Parse dòng proxy từ file với hỗ trợ comment và trạng thái"""
        try:
            # Tách comment nếu có
            proxy_part = line
            comment_part = ""

            if '#' in line:
                proxy_part, comment_part = line.split('#', 1)
                proxy_part = proxy_part.strip()
                comment_part = comment_part.strip()

            if not proxy_part:
                return None

            # Parse proxy part: host:port hoặc host:port:username:password
            parts = proxy_part.split(':')
            proxy = None

            if len(parts) == 2:
                # host:port
                host, port = parts
                proxy = ProxyInfo(host.strip(), int(port.strip()))
            elif len(parts) == 4:
                # host:port:username:password
                host, port, username, password = parts
                proxy = ProxyInfo(
                    host.strip(),
                    int(port.strip()),
                    username.strip(),
                    password.strip()
                )
            else:
                self.logger.warning(f"Format proxy không hợp lệ: {line}")
                return None

            # Parse comment để lấy thông tin trạng thái nếu có
            if comment_part and proxy:
                # Tìm response time
                time_match = re.search(r'(\d+)ms', comment_part)
                if time_match:
                    proxy.response_time = int(time_match.group(1))

                # Kiểm tra trạng thái
                if 'working' in comment_part.lower():
                    proxy.is_working = True
                    proxy.last_tested = time.time()
                elif 'not working' in comment_part.lower():
                    proxy.is_working = False
                    proxy.last_tested = time.time()

            return proxy

        except Exception as e:
            self.logger.warning(f"Lỗi parse proxy line '{line}': {str(e)}")
            return None

    def add_proxy(self, host, port, username=None, password=None, proxy_type="http"):
        """Thêm proxy thủ công"""
        proxy = ProxyInfo(host, port, username, password, proxy_type)
        self.proxies.append(proxy)
        self.logger.info(f"Đã thêm proxy: {proxy.full_address}")

    def test_proxy(self, proxy, timeout=10):
        """Test một proxy với country detection"""
        try:
            start_time = time.time()

            # Cấu hình proxy cho requests
            proxy_dict = {
                'http': f"{proxy.type}://{proxy.host}:{proxy.port}",
                'https': f"{proxy.type}://{proxy.host}:{proxy.port}"
            }

            # Nếu có auth
            if proxy.username and proxy.password:
                proxy_dict = {
                    'http': f"{proxy.type}://{proxy.username}:{proxy.password}@{proxy.host}:{proxy.port}",
                    'https': f"{proxy.type}://{proxy.username}:{proxy.password}@{proxy.host}:{proxy.port}"
                }

            # Test với httpbin để lấy IP và country
            response = requests.get(
                'http://httpbin.org/ip',
                proxies=proxy_dict,
                timeout=timeout,
                headers={'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'}
            )

            if response.status_code == 200:
                response_time = time.time() - start_time
                proxy.is_working = True
                proxy.response_time = int(response_time * 1000)  # ms
                proxy.last_tested = time.time()

                # Lấy IP và detect country
                try:
                    ip_data = response.json()
                    proxy_ip = ip_data.get('origin', 'Unknown')

                    # Detect country từ IP
                    country = self._detect_country_from_ip(proxy_ip)
                    proxy.country = country

                    # Check if Vietnam proxy
                    if country == 'VN':
                        if proxy not in self.vietnam_proxies:
                            self.vietnam_proxies.append(proxy)
                        self.logger.info(f"🇻🇳 VIETNAM Proxy {proxy.full_address} - IP: {proxy_ip} - {proxy.response_time}ms")
                    elif country in self.blocked_countries:
                        self.logger.warning(f"🚫 BLOCKED Country {country} - Proxy {proxy.full_address} - IP: {proxy_ip}")
                        proxy.is_working = False  # Mark as not working for 13win
                        return False
                    else:
                        self.logger.info(f"🌏 {country} Proxy {proxy.full_address} - IP: {proxy_ip} - {proxy.response_time}ms")

                except Exception as ip_error:
                    self.logger.debug(f"Lỗi detect country: {ip_error}")
                    self.logger.info(f"Proxy {proxy.full_address} hoạt động - {proxy.response_time}ms")

                # Cache working proxy (with error handling)
                try:
                    if self.proxy_cache:
                        self.proxy_cache.add_working_proxy({
                            'host': proxy.host,
                            'port': proxy.port,
                            'type': proxy.type,
                            'username': proxy.username,
                            'password': proxy.password,
                            'response_time': proxy.response_time
                        })
                except Exception as cache_error:
                    self.logger.debug(f"Lỗi cache working proxy: {cache_error}")

                return True
            else:
                proxy.is_working = False
                return False

        except Exception as e:
            proxy.is_working = False
            self.logger.debug(f"Proxy {proxy.full_address} không hoạt động: {str(e)}")

            # Cache failed proxy (with error handling)
            try:
                if self.proxy_cache:
                    self.proxy_cache.add_failed_proxy({
                        'host': proxy.host,
                        'port': proxy.port
                    }, str(e))
            except Exception as cache_error:
                self.logger.debug(f"Lỗi cache failed proxy: {cache_error}")

            return False

    def _detect_country_from_ip(self, ip):
        """Detect country từ IP address"""
        try:
            # Simple country detection based on IP ranges (basic implementation)
            # Vietnam IP ranges (simplified)
            vietnam_ranges = [
                '14.', '27.', '42.', '43.', '45.', '49.', '58.', '59.',
                '61.', '103.', '113.', '115.', '116.', '117.', '118.',
                '119.', '123.', '124.', '125.', '171.', '210.', '222.'
            ]

            # US IP ranges (simplified)
            us_ranges = [
                '3.', '4.', '8.', '15.', '16.', '17.', '18.', '23.', '24.',
                '34.', '35.', '50.', '52.', '54.', '63.', '64.', '65.', '66.',
                '67.', '68.', '69.', '70.', '71.', '72.', '73.', '74.', '75.',
                '76.', '96.', '97.', '98.', '99.', '100.', '104.', '107.',
                '108.', '142.', '162.', '173.', '174.', '184.', '192.', '198.',
                '199.', '204.', '205.', '206.', '207.', '208.', '209.'
            ]

            # Check Vietnam first
            for vn_range in vietnam_ranges:
                if ip.startswith(vn_range):
                    return 'VN'

            # Check US
            for us_range in us_ranges:
                if ip.startswith(us_range):
                    return 'US'

            # Try to detect other countries by first octet
            first_octet = ip.split('.')[0]
            if first_octet in ['1', '2', '5', '6', '7', '9', '11', '12', '13', '19', '20', '21', '22']:
                return 'US'  # Likely US
            elif first_octet in ['36', '39', '40', '41', '46', '47', '48']:
                return 'EU'  # Likely Europe
            elif first_octet in ['101', '102', '106', '110', '111', '112', '114', '120', '121', '122']:
                return 'AS'  # Likely Asia (preferred)
            else:
                return 'Unknown'

        except Exception as e:
            self.logger.debug(f"Lỗi detect country từ IP {ip}: {e}")
            return 'Unknown'

    def test_all_proxies(self, max_workers=20, progress_callback=None):
        """Test tất cả proxy đồng thời"""
        if not self.proxies:
            self.logger.warning("Không có proxy để test")
            return 0

        total_proxies = len(self.proxies)
        self.logger.info(f"Bắt đầu test {total_proxies} proxy...")

        # Disable cache temporarily during bulk testing
        cache_backup = self.proxy_cache
        self.proxy_cache = None
        self.logger.info("🚫 Tạm thời disable cache để test nhanh hơn")

        working_count = 0
        tested_count = 0

        with ThreadPoolExecutor(max_workers=max_workers) as executor:
            # Submit tất cả tasks
            future_to_proxy = {
                executor.submit(self.test_proxy, proxy): proxy
                for proxy in self.proxies
            }

            # Xử lý kết quả
            for future in as_completed(future_to_proxy):
                proxy = future_to_proxy[future]
                tested_count += 1

                try:
                    is_working = future.result()
                    if is_working:
                        working_count += 1
                        with self.lock:
                            if proxy not in self.working_proxies:
                                self.working_proxies.append(proxy)
                        self.logger.info(f"✓ Proxy hoạt động: {proxy.full_address} ({proxy.response_time}ms)")
                    else:
                        self.logger.debug(f"✗ Proxy không hoạt động: {proxy.full_address}")

                    # Log tiến trình
                    if tested_count % 10 == 0 or tested_count == total_proxies:
                        progress_percent = (tested_count / total_proxies) * 100
                        self.logger.info(f"Tiến trình test proxy: {tested_count}/{total_proxies} ({progress_percent:.1f}%) - Hoạt động: {working_count}")

                    # Callback để cập nhật UI
                    if progress_callback:
                        progress_callback(tested_count, total_proxies, working_count)

                except Exception as e:
                    self.logger.error(f"Lỗi test proxy {proxy.full_address}: {str(e)}")

        success_rate = (working_count / total_proxies * 100) if total_proxies > 0 else 0
        self.logger.info(f"Hoàn thành test proxy: {working_count}/{total_proxies} hoạt động ({success_rate:.1f}%)")

        # Restore cache và save working proxies
        self.proxy_cache = cache_backup
        self.logger.info("✅ Khôi phục cache")

        # Batch save all working proxies to cache
        try:
            if self.proxy_cache and working_count > 0:
                self.logger.info(f"💾 Đang lưu {working_count} proxy hoạt động vào cache...")
                for proxy in self.working_proxies:
                    if proxy.is_working:
                        self.proxy_cache.add_working_proxy({
                            'host': proxy.host,
                            'port': proxy.port,
                            'type': proxy.type,
                            'username': proxy.username,
                            'password': proxy.password,
                            'response_time': proxy.response_time
                        })
                self.proxy_cache.force_save()
                self.logger.info("✅ Đã lưu proxy vào cache")
        except Exception as e:
            self.logger.warning(f"Lỗi save cache: {e}")

        return working_count

    def get_proxy(self):
        """Lấy proxy để sử dụng - ưu tiên Vietnam, tránh blocked countries"""
        with self.lock:
            # Ưu tiên Vietnam proxies trước
            vietnam_available = [p for p in self.vietnam_proxies if p.is_working and not p.in_use]
            if vietnam_available:
                proxy = random.choice(vietnam_available)
                proxy.in_use = True
                self.logger.info(f"🇻🇳 Đã cấp phát VIETNAM proxy: {proxy.full_address}")
                return proxy

            # Nếu không có Vietnam, lấy preferred countries
            preferred_available = [p for p in self.working_proxies
                                 if p.is_working and not p.in_use
                                 and hasattr(p, 'country')
                                 and p.country in self.preferred_countries]
            if preferred_available:
                proxy = random.choice(preferred_available)
                proxy.in_use = True
                self.logger.info(f"🌏 Đã cấp phát {getattr(proxy, 'country', 'Unknown')} proxy: {proxy.full_address}")
                return proxy

            # Cuối cùng, lấy bất kỳ proxy nào không bị block
            available_proxies = [p for p in self.working_proxies
                               if p.is_working and not p.in_use
                               and (not hasattr(p, 'country') or p.country not in self.blocked_countries)]

            if not available_proxies:
                self.logger.warning("⚠️ Không có proxy khả dụng (tất cả bị block hoặc đang dùng)")
                return None

            # Chọn proxy ngẫu nhiên
            proxy = random.choice(available_proxies)
            proxy.in_use = True

            self.logger.info(f"Đã cấp phát proxy: {proxy.full_address}")
            return proxy

    def release_proxy(self, proxy):
        """Trả lại proxy sau khi sử dụng"""
        if proxy:
            with self.lock:
                proxy.in_use = False
                self.logger.info(f"Đã trả lại proxy: {proxy.full_address}")

    def get_working_proxy_count(self):
        """Số lượng proxy đang hoạt động"""
        with self.lock:
            return len([p for p in self.working_proxies if p.is_working and not p.in_use])

    def get_stats(self):
        """Thống kê proxy"""
        with self.lock:
            total = len(self.proxies)
            working = len([p for p in self.working_proxies if p.is_working])
            in_use = len([p for p in self.working_proxies if p.in_use])
            available = working - in_use

            return {
                'total': total,
                'working': working,
                'in_use': in_use,
                'available': available
            }

    def save_working_proxies(self, filename="working_proxies.txt"):
        """Lưu proxy hoạt động vào file"""
        try:
            working_proxies_to_save = [p for p in self.working_proxies if p.is_working]

            with open(filename, 'w', encoding='utf-8') as f:
                f.write("# Working Proxies - Generated by Auto Registration Tool\n")
                f.write("# Format: host:port or host:port:username:password\n")
                f.write(f"# Total working proxies: {len(working_proxies_to_save)}\n")
                f.write(f"# Generated: {time.strftime('%Y-%m-%d %H:%M:%S')}\n\n")

                for proxy in working_proxies_to_save:
                    if proxy.username and proxy.password:
                        line = f"{proxy.host}:{proxy.port}:{proxy.username}:{proxy.password}"
                    else:
                        line = f"{proxy.host}:{proxy.port}"

                    f.write(f"{line}  # {proxy.response_time}ms\n")

            self.logger.info(f"Đã lưu {len(working_proxies_to_save)} proxy hoạt động vào {filename}")
            return True

        except Exception as e:
            self.logger.error(f"Lỗi lưu proxy: {str(e)}")
            return False

    def save_all_proxies(self, filename="all_proxies.txt"):
        """Lưu tất cả proxy vào file (bao gồm cả chưa test)"""
        try:
            with open(filename, 'w', encoding='utf-8') as f:
                f.write("# All Proxies - Generated by Auto Registration Tool\n")
                f.write("# Format: host:port or host:port:username:password\n")
                f.write(f"# Total proxies: {len(self.proxies)}\n")
                f.write(f"# Generated: {time.strftime('%Y-%m-%d %H:%M:%S')}\n\n")

                for proxy in self.proxies:
                    if proxy.username and proxy.password:
                        line = f"{proxy.host}:{proxy.port}:{proxy.username}:{proxy.password}"
                    else:
                        line = f"{proxy.host}:{proxy.port}"

                    # Thêm thông tin trạng thái nếu đã test
                    if hasattr(proxy, 'is_working') and proxy.is_working:
                        line += f"  # Working - {proxy.response_time}ms"
                    elif hasattr(proxy, 'is_working') and not proxy.is_working:
                        line += f"  # Not working"
                    else:
                        line += f"  # Not tested"

                    f.write(f"{line}\n")

            self.logger.info(f"Đã lưu {len(self.proxies)} proxy vào {filename}")
            return True

        except Exception as e:
            self.logger.error(f"Lỗi lưu tất cả proxy: {str(e)}")
            return False

    def clear_all(self):
        """Xóa tất cả proxy"""
        with self.lock:
            self.proxies.clear()
            self.working_proxies.clear()
        self.logger.info("Đã xóa tất cả proxy")

    def remove_non_working_proxies(self):
        """Xóa proxy không hoạt động"""
        with self.lock:
            before_count = len(self.working_proxies)
            self.working_proxies = [p for p in self.working_proxies if p.is_working]
            removed_count = before_count - len(self.working_proxies)

        self.logger.info(f"Đã xóa {removed_count} proxy không hoạt động")
        return removed_count

# Proxy sources miễn phí (có thể không ổn định)
FREE_PROXY_SOURCES = [
    "https://raw.githubusercontent.com/TheSpeedX/PROXY-List/master/http.txt",
    "https://raw.githubusercontent.com/clarketm/proxy-list/master/proxy-list-raw.txt",
    "https://raw.githubusercontent.com/sunny9577/proxy-scraper/master/proxies.txt"
]

def fetch_free_proxies(progress_callback=None):
    """Fetch proxy miễn phí từ internet"""
    logger = logging.getLogger(__name__)
    proxies = []
    total_sources = len(FREE_PROXY_SOURCES)

    logger.info(f"Bắt đầu fetch proxy từ {total_sources} nguồn...")

    for i, source in enumerate(FREE_PROXY_SOURCES, 1):
        try:
            logger.info(f"[{i}/{total_sources}] Fetching proxy từ: {source}")

            # Callback để cập nhật UI
            if progress_callback:
                progress_callback(f"Đang fetch từ nguồn {i}/{total_sources}")

            response = requests.get(source, timeout=30)

            if response.status_code == 200:
                lines = response.text.strip().split('\n')
                source_proxies = []

                for line in lines:
                    line = line.strip()
                    if ':' in line and not line.startswith('#'):
                        try:
                            parts = line.split(':')
                            if len(parts) >= 2:
                                host, port = parts[0], parts[1]
                                source_proxies.append(ProxyInfo(host.strip(), int(port.strip())))
                        except:
                            continue

                proxies.extend(source_proxies)
                logger.info(f"✓ Fetch được {len(source_proxies)} proxy từ nguồn {i}")
            else:
                logger.warning(f"✗ Không thể fetch từ nguồn {i} (HTTP {response.status_code})")

        except Exception as e:
            logger.error(f"✗ Lỗi fetch proxy từ nguồn {i}: {str(e)}")

    # Loại bỏ duplicate
    unique_proxies = []
    seen_addresses = set()

    for proxy in proxies:
        if proxy.full_address not in seen_addresses:
            unique_proxies.append(proxy)
            seen_addresses.add(proxy.full_address)

    removed_duplicates = len(proxies) - len(unique_proxies)
    if removed_duplicates > 0:
        logger.info(f"Đã loại bỏ {removed_duplicates} proxy trùng lặp")

    logger.info(f"Tổng cộng fetch được {len(unique_proxies)} proxy unique từ {total_sources} nguồn")
    return unique_proxies
