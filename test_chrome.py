#!/usr/bin/env python3
"""
Test script để kiểm tra Chrome có tạo được không
"""

import sys
import os
import logging

# Thêm src vào path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def setup_logging():
    """Cấu hình logging"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(),
            logging.FileHandler('chrome_test.log', encoding='utf-8')
        ]
    )

def test_basic_chrome():
    """Test tạo Chrome cơ bản"""
    print("=== TEST CHROME CƠ BẢN ===")
    
    try:
        from selenium import webdriver
        from selenium.webdriver.chrome.options import Options
        from selenium.webdriver.chrome.service import Service
        from webdriver_manager.chrome import ChromeDriverManager
        
        print("1. Đang tải ChromeDriver...")
        service = Service(ChromeDriverManager().install())
        print("✓ ChromeDriver đã sẵn sàng")
        
        print("2. Đang tạo Chrome options...")
        options = Options()
        options.add_argument("--no-sandbox")
        options.add_argument("--disable-dev-shm-usage")
        options.add_argument("--disable-gpu")
        print("✓ Chrome options đã cấu hình")
        
        print("3. Đang khởi tạo Chrome...")
        driver = webdriver.Chrome(service=service, options=options)
        print("✓ Chrome đã khởi tạo thành công!")
        
        print("4. Đang test điều hướng...")
        driver.get("https://www.google.com")
        print(f"✓ Đã điều hướng thành công. Title: {driver.title}")
        
        print("5. Đóng Chrome...")
        driver.quit()
        print("✓ Chrome đã đóng thành công")
        
        return True
        
    except Exception as e:
        print(f"✗ Lỗi: {str(e)}")
        return False

def test_chrome_with_proxy():
    """Test Chrome với proxy"""
    print("\n=== TEST CHROME VỚI PROXY ===")
    
    try:
        from src.win13_registration_service import Win13RegistrationService
        
        service = Win13RegistrationService()
        
        print("1. Đang tạo Chrome instance không proxy...")
        driver = service.create_chrome_instance()
        
        if driver:
            print("✓ Chrome instance tạo thành công!")
            
            print("2. Đang test điều hướng...")
            driver.get("https://httpbin.org/ip")
            print(f"✓ Đã điều hướng thành công")
            
            print("3. Đóng Chrome...")
            driver.quit()
            print("✓ Chrome đã đóng thành công")
            return True
        else:
            print("✗ Không thể tạo Chrome instance")
            return False
            
    except Exception as e:
        print(f"✗ Lỗi: {str(e)}")
        return False

def test_chrome_with_profile():
    """Test Chrome với profile riêng"""
    print("\n=== TEST CHROME VỚI PROFILE ===")
    
    try:
        from src.win13_registration_service import Win13RegistrationService
        import tempfile
        import os
        
        service = Win13RegistrationService()
        
        # Tạo profile path
        profile_path = os.path.join(tempfile.gettempdir(), "test_profile")
        
        print(f"1. Đang tạo Chrome với profile: {profile_path}")
        driver = service.create_chrome_instance(profile_path=profile_path)
        
        if driver:
            print("✓ Chrome với profile tạo thành công!")
            
            print("2. Đang test điều hướng...")
            driver.get("https://www.google.com")
            print(f"✓ Đã điều hướng thành công")
            
            print("3. Đóng Chrome...")
            driver.quit()
            print("✓ Chrome đã đóng thành công")
            
            # Cleanup profile
            try:
                import shutil
                shutil.rmtree(profile_path, ignore_errors=True)
                print("✓ Đã xóa profile test")
            except:
                pass
                
            return True
        else:
            print("✗ Không thể tạo Chrome instance với profile")
            return False
            
    except Exception as e:
        print(f"✗ Lỗi: {str(e)}")
        return False

def check_system_requirements():
    """Kiểm tra yêu cầu hệ thống"""
    print("=== KIỂM TRA HỆ THỐNG ===")
    
    # Check Python version
    python_version = sys.version
    print(f"Python version: {python_version}")
    
    # Check Chrome installation
    try:
        import subprocess
        result = subprocess.run(['chrome', '--version'], capture_output=True, text=True)
        if result.returncode == 0:
            print(f"✓ Chrome: {result.stdout.strip()}")
        else:
            print("✗ Chrome không tìm thấy")
    except:
        try:
            result = subprocess.run(['google-chrome', '--version'], capture_output=True, text=True)
            if result.returncode == 0:
                print(f"✓ Chrome: {result.stdout.strip()}")
            else:
                print("✗ Chrome không tìm thấy")
        except:
            print("⚠ Không thể kiểm tra Chrome version")
    
    # Check required packages
    required_packages = ['selenium', 'webdriver_manager', 'requests']
    for package in required_packages:
        try:
            __import__(package)
            print(f"✓ {package} đã cài đặt")
        except ImportError:
            print(f"✗ {package} chưa cài đặt")

def main():
    """Main function"""
    setup_logging()
    
    print("🚀 CHROME TEST TOOL - 13Win16 Auto Registration")
    print("=" * 50)
    
    # Check system requirements
    check_system_requirements()
    print()
    
    # Test basic Chrome
    basic_result = test_basic_chrome()
    
    # Test Chrome with service
    service_result = test_chrome_with_proxy()
    
    # Test Chrome with profile
    profile_result = test_chrome_with_profile()
    
    # Summary
    print("\n" + "=" * 50)
    print("📊 KẾT QUẢ TEST:")
    print(f"Chrome cơ bản: {'✓ PASS' if basic_result else '✗ FAIL'}")
    print(f"Chrome service: {'✓ PASS' if service_result else '✗ FAIL'}")
    print(f"Chrome profile: {'✓ PASS' if profile_result else '✗ FAIL'}")
    
    if all([basic_result, service_result, profile_result]):
        print("\n🎉 TẤT CẢ TEST THÀNH CÔNG! Chrome hoạt động bình thường.")
    else:
        print("\n❌ CÓ LỖI XẢY RA! Vui lòng kiểm tra log để biết chi tiết.")
        print("\nGợi ý khắc phục:")
        print("1. Cài đặt Chrome browser: https://www.google.com/chrome/")
        print("2. Cài đặt dependencies: pip install -r requirements.txt")
        print("3. Kiểm tra antivirus có block Chrome không")
        print("4. Chạy với quyền Administrator")

if __name__ == "__main__":
    main()
