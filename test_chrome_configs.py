#!/usr/bin/env python3
"""
Test các cấu hình Chrome khác nhau để fix crash
"""

import os
import sys
import time

# Thêm src vào path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service

def test_chrome_config(config_name, options):
    """Test một cấu hình Chrome"""
    print(f"\n🧪 Test config: {config_name}")
    print("-" * 40)
    
    driver = None
    try:
        # Sử dụng ChromeDriver local nếu có
        chromedriver_path = "chromedriver.exe"
        if os.path.exists(chromedriver_path):
            service = Service(chromedriver_path)
        else:
            from webdriver_manager.chrome import ChromeDriverManager
            service = Service(ChromeDriverManager().install())
        
        # Tạo driver
        driver = webdriver.Chrome(service=service, options=options)
        print("✓ Chrome khởi tạo thành công")
        
        # Test navigation
        driver.get("https://www.google.com")
        print("✓ Navigation thành công")
        
        # Test basic operations
        title = driver.title
        print(f"✓ Page title: {title}")
        
        # Test form interaction
        search_box = driver.find_element("name", "q")
        search_box.send_keys("test")
        print("✓ Form interaction thành công")
        
        return True
        
    except Exception as e:
        print(f"✗ Lỗi: {e}")
        return False
        
    finally:
        if driver:
            try:
                driver.quit()
                print("✓ Đã đóng browser")
            except:
                pass

def main():
    """Test các cấu hình khác nhau"""
    print("🔧 CHROME CONFIG TEST TOOL")
    print("=" * 50)
    
    configs = []
    
    # Config 1: Minimal (ít arguments nhất)
    options1 = Options()
    options1.add_argument("--no-sandbox")
    options1.add_argument("--disable-dev-shm-usage")
    configs.append(("Minimal", options1))
    
    # Config 2: Basic (thêm một số args cơ bản)
    options2 = Options()
    options2.add_argument("--no-sandbox")
    options2.add_argument("--disable-dev-shm-usage")
    options2.add_argument("--disable-gpu")
    options2.add_argument("--disable-extensions")
    configs.append(("Basic", options2))
    
    # Config 3: Medium (thêm performance args)
    options3 = Options()
    options3.add_argument("--no-sandbox")
    options3.add_argument("--disable-dev-shm-usage")
    options3.add_argument("--disable-gpu")
    options3.add_argument("--disable-extensions")
    options3.add_argument("--disable-images")
    options3.add_argument("--disable-plugins")
    options3.add_argument("--mute-audio")
    configs.append(("Medium", options3))
    
    # Config 4: Full (như hiện tại nhưng bỏ single-process)
    options4 = Options()
    options4.add_argument("--no-sandbox")
    options4.add_argument("--disable-dev-shm-usage")
    options4.add_argument("--disable-gpu")
    options4.add_argument("--disable-extensions")
    options4.add_argument("--disable-images")
    options4.add_argument("--disable-plugins")
    options4.add_argument("--disable-default-apps")
    options4.add_argument("--disable-sync")
    options4.add_argument("--disable-translate")
    options4.add_argument("--mute-audio")
    options4.add_argument("--no-first-run")
    options4.add_argument("--ignore-certificate-errors")
    options4.add_argument("--remote-debugging-port=0")
    configs.append(("Full (no single-process)", options4))
    
    # Config 5: Headless
    options5 = Options()
    options5.add_argument("--headless")
    options5.add_argument("--no-sandbox")
    options5.add_argument("--disable-dev-shm-usage")
    options5.add_argument("--disable-gpu")
    configs.append(("Headless", options5))
    
    # Config 6: Window size specified
    options6 = Options()
    options6.add_argument("--no-sandbox")
    options6.add_argument("--disable-dev-shm-usage")
    options6.add_argument("--window-size=1920,1080")
    options6.add_argument("--start-maximized")
    configs.append(("With window size", options6))
    
    # Test từng config
    results = []
    for config_name, options in configs:
        try:
            success = test_chrome_config(config_name, options)
            results.append((config_name, success))
            
            if success:
                print(f"✅ {config_name}: THÀNH CÔNG")
            else:
                print(f"❌ {config_name}: THẤT BẠI")
                
        except Exception as e:
            print(f"❌ {config_name}: CRASH - {e}")
            results.append((config_name, False))
        
        # Chờ giữa các test
        time.sleep(2)
    
    # Summary
    print("\n" + "=" * 50)
    print("📊 KẾT QUẢ TEST:")
    
    successful_configs = []
    for config_name, success in results:
        status = "✅ PASS" if success else "❌ FAIL"
        print(f"{config_name}: {status}")
        if success:
            successful_configs.append(config_name)
    
    if successful_configs:
        print(f"\n🎉 {len(successful_configs)} config thành công:")
        for config in successful_configs:
            print(f"  - {config}")
        
        print("\n💡 Khuyến nghị:")
        print("Sử dụng config 'Minimal' hoặc 'Basic' để tránh crash.")
        print("Nếu cần performance, thử 'Medium'.")
        print("Tránh quá nhiều arguments có thể gây conflict.")
        
    else:
        print("\n❌ TẤT CẢ CONFIG THẤT BẠI!")
        print("\n🔧 Gợi ý khắc phục:")
        print("1. Cập nhật Chrome browser")
        print("2. Chạy với quyền Administrator")
        print("3. Tắt antivirus tạm thời")
        print("4. Kiểm tra ChromeDriver version")
        print("5. Restart máy tính")

if __name__ == "__main__":
    main()
