using System;

namespace ChromeAutoManager.Models
{
    /// <summary>
    /// Model đại diện cho thông tin tài khoản
    /// </summary>
    public class Account
    {
        public string Username { get; set; } = string.Empty;
        public string Email { get; set; } = string.Empty;
        public string Password { get; set; } = string.Empty;
        public string Phone { get; set; } = string.Empty;
        public string FirstName { get; set; } = string.Empty;
        public string LastName { get; set; } = string.Empty;
        public string FullName { get; set; } = string.Empty;
        public int BirthDay { get; set; }
        public int BirthMonth { get; set; }
        public int BirthYear { get; set; }
        public string Address { get; set; } = string.Empty;
        public string City { get; set; } = string.Empty;
        public string Country { get; set; } = string.Empty;
        public string PostalCode { get; set; } = string.Empty;
        public DateTime CreatedAt { get; set; } = DateTime.Now;

        public override string ToString()
        {
            return $"Username: {Username} | Email: {Email} | Password: {Password} | Phone: {Phone} | Name: {FullName}";
        }

        /// <summary>
        /// Tạo bản sao của tài khoản
        /// </summary>
        public Account Clone()
        {
            return new Account
            {
                Username = this.Username,
                Email = this.Email,
                Password = this.Password,
                Phone = this.Phone,
                FirstName = this.FirstName,
                LastName = this.LastName,
                FullName = this.FullName,
                BirthDay = this.BirthDay,
                BirthMonth = this.BirthMonth,
                BirthYear = this.BirthYear,
                Address = this.Address,
                City = this.City,
                Country = this.Country,
                PostalCode = this.PostalCode,
                CreatedAt = this.CreatedAt
            };
        }

        /// <summary>
        /// Kiểm tra tài khoản có hợp lệ không
        /// </summary>
        public bool IsValid()
        {
            return !string.IsNullOrWhiteSpace(Username) &&
                   !string.IsNullOrWhiteSpace(Password) &&
                   !string.IsNullOrWhiteSpace(FullName);
        }
    }
}
