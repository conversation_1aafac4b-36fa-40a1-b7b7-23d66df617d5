using System;
using System.Linq;
using System.Text;
using Bogus;
using ChromeAutoManager.Models;
using ChromeAutoManager.Core;

namespace ChromeAutoManager.Services
{
    /// <summary>
    /// Service tạo thông tin tài khoản ngẫu nhiên
    /// </summary>
    public class AccountGenerator
    {
        private readonly Faker _faker;
        private readonly Random _random;

        public AccountGenerator()
        {
            _faker = new Faker("vi");
            _random = new Random();
        }

        /// <summary>
        /// Tạo username ngẫu nhiên
        /// </summary>
        public string GenerateUsername(int length = 8)
        {
            var firstName = _faker.Name.FirstName().ToLower().Replace(" ", "");
            var numbers = _random.Next(1000, 9999).ToString();
            var username = firstName + numbers;

            // Đảm bảo độ dài phù hợp
            if (username.Length > length)
            {
                username = username.Substring(0, length);
            }
            else if (username.Length < length)
            {
                var chars = "abcdefghijklmnopqrstuvwxyz**********";
                var sb = new StringBuilder(username);
                while (sb.Length < length)
                {
                    sb.Append(chars[_random.Next(chars.Length)]);
                }
                username = sb.ToString();
            }

            return username;
        }

        /// <summary>
        /// Tạo email ngẫu nhiên
        /// </summary>
        public string GenerateEmail()
        {
            var username = GenerateUsername(10);
            var domain = AppConfig.Instance.Account.EmailDomains[_random.Next(AppConfig.Instance.Account.EmailDomains.Count)];
            return $"{username}@{domain}";
        }

        /// <summary>
        /// Tạo mật khẩu mạnh
        /// </summary>
        public string GeneratePassword()
        {
            var length = AppConfig.Instance.Account.PasswordLength;
            var uppercase = "ABCDEFGHIJKLMNOPQRSTUVWXYZ";
            var lowercase = "abcdefghijklmnopqrstuvwxyz";
            var digits = "**********";
            var special = "!@#$%^&*";

            var password = new StringBuilder();

            // Đảm bảo có ít nhất 1 ký tự từ mỗi loại
            password.Append(uppercase[_random.Next(uppercase.Length)]);
            password.Append(lowercase[_random.Next(lowercase.Length)]);
            password.Append(digits[_random.Next(digits.Length)]);
            password.Append(special[_random.Next(special.Length)]);

            // Thêm các ký tự ngẫu nhiên
            var allChars = uppercase + lowercase + digits + special;
            for (int i = 4; i < length; i++)
            {
                password.Append(allChars[_random.Next(allChars.Length)]);
            }

            // Trộn ngẫu nhiên
            return new string(password.ToString().ToCharArray().OrderBy(x => _random.Next()).ToArray());
        }

        /// <summary>
        /// Tạo số điện thoại ngẫu nhiên
        /// </summary>
        public string GeneratePhone()
        {
            var countryCodes = AppConfig.Instance.Account.PhoneCountryCodes;
            var countryCode = countryCodes[_random.Next(countryCodes.Count)];

            if (countryCode == "+84") // Việt Nam
            {
                // Số điện thoại Việt Nam: +84 + 9 số
                var validPrefixes = new[] { "3", "5", "7", "8", "9" };
                var prefix = validPrefixes[_random.Next(validPrefixes.Length)];
                var phoneNumber = prefix + _random.Next(********, ********).ToString();
                return $"{countryCode}{phoneNumber}";
            }
            else
            {
                // Số điện thoại quốc tế: 10 số
                var phoneNumber = _random.Next(**********, int.MaxValue).ToString().Substring(0, 10);
                return $"{countryCode}{phoneNumber}";
            }
        }

        /// <summary>
        /// Tạo thông tin họ tên
        /// </summary>
        public (string firstName, string lastName, string fullName) GenerateName()
        {
            var firstName = _faker.Name.FirstName();
            var lastName = _faker.Name.LastName();
            var fullName = $"{lastName} {firstName}";
            return (firstName, lastName, fullName);
        }

        /// <summary>
        /// Tạo ngày sinh ngẫu nhiên
        /// </summary>
        public (int day, int month, int year) GenerateBirthDate()
        {
            var minAge = AppConfig.Instance.Account.MinAge;
            var maxAge = AppConfig.Instance.Account.MaxAge;
            var age = _random.Next(minAge, maxAge + 1);
            
            var birthDate = DateTime.Now.AddYears(-age).AddDays(_random.Next(-365, 365));
            return (birthDate.Day, birthDate.Month, birthDate.Year);
        }

        /// <summary>
        /// Tạo địa chỉ ngẫu nhiên
        /// </summary>
        public (string address, string city, string country, string postalCode) GenerateAddress()
        {
            var address = _faker.Address.FullAddress();
            var city = _faker.Address.City();
            var country = _faker.Address.Country();
            var postalCode = _faker.Address.ZipCode();
            return (address, city, country, postalCode);
        }

        /// <summary>
        /// Tạo tài khoản đầy đủ
        /// </summary>
        public Account GenerateCompleteAccount()
        {
            var (firstName, lastName, fullName) = GenerateName();
            var (day, month, year) = GenerateBirthDate();
            var (address, city, country, postalCode) = GenerateAddress();

            var account = new Account
            {
                Username = GenerateUsername(),
                Email = GenerateEmail(),
                Password = GeneratePassword(),
                Phone = GeneratePhone(),
                FirstName = firstName,
                LastName = lastName,
                FullName = fullName,
                BirthDay = day,
                BirthMonth = month,
                BirthYear = year,
                Address = address,
                City = city,
                Country = country,
                PostalCode = postalCode,
                CreatedAt = DateTime.Now
            };

            Logger.Info("Đã tạo tài khoản: {Username} - {Email}", account.Username, account.Email);
            return account;
        }

        /// <summary>
        /// Lưu tài khoản vào file
        /// </summary>
        public void SaveAccount(Account account, string filename)
        {
            try
            {
                var accountLine = account.ToString() + Environment.NewLine;
                System.IO.File.AppendAllText(filename, accountLine, Encoding.UTF8);
                Logger.Info("Đã lưu tài khoản vào file: {Filename}", filename);
            }
            catch (Exception ex)
            {
                Logger.Error(ex, "Lỗi khi lưu tài khoản vào file: {Filename}", filename);
            }
        }
    }
}
