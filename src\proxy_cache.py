#!/usr/bin/env python3
"""
Proxy Cache System - Lưu proxy hoạt động để không cần test lại
"""

import json
import os
import time
import logging
from datetime import datetime, timedelta

class ProxyCache:
    def __init__(self, cache_file="proxy_cache.json"):
        self.cache_file = cache_file
        self.logger = logging.getLogger(__name__)
        self.cache_data = self._load_cache()
        
    def _load_cache(self):
        """Load proxy cache từ file"""
        try:
            if os.path.exists(self.cache_file):
                with open(self.cache_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    self.logger.info(f"✓ Loaded proxy cache: {len(data.get('working_proxies', []))} proxies")
                    return data
            else:
                self.logger.info("📝 Creating new proxy cache")
                return {
                    'working_proxies': [],
                    'failed_proxies': [],
                    'last_updated': None
                }
        except Exception as e:
            self.logger.warning(f"Lỗi load cache: {e}")
            return {
                'working_proxies': [],
                'failed_proxies': [],
                'last_updated': None
            }
    
    def _save_cache(self):
        """Save proxy cache to file"""
        try:
            self.cache_data['last_updated'] = datetime.now().isoformat()
            with open(self.cache_file, 'w', encoding='utf-8') as f:
                json.dump(self.cache_data, f, indent=2, ensure_ascii=False)
            self.logger.info(f"✓ Saved proxy cache: {len(self.cache_data['working_proxies'])} working proxies")
        except Exception as e:
            self.logger.error(f"Lỗi save cache: {e}")
    
    def add_working_proxy(self, proxy_info):
        """Thêm proxy hoạt động vào cache"""
        try:
            proxy_key = f"{proxy_info['host']}:{proxy_info['port']}"
            
            # Kiểm tra đã có chưa
            for cached_proxy in self.cache_data['working_proxies']:
                if cached_proxy['key'] == proxy_key:
                    # Update last_tested
                    cached_proxy['last_tested'] = datetime.now().isoformat()
                    cached_proxy['success_count'] = cached_proxy.get('success_count', 0) + 1
                    self._save_cache()
                    return
            
            # Thêm mới
            proxy_cache_entry = {
                'key': proxy_key,
                'host': proxy_info['host'],
                'port': proxy_info['port'],
                'type': proxy_info.get('type', 'http'),
                'username': proxy_info.get('username'),
                'password': proxy_info.get('password'),
                'first_tested': datetime.now().isoformat(),
                'last_tested': datetime.now().isoformat(),
                'success_count': 1,
                'response_time': proxy_info.get('response_time', 0)
            }
            
            self.cache_data['working_proxies'].append(proxy_cache_entry)
            self.logger.info(f"✓ Added working proxy to cache: {proxy_key}")
            self._save_cache()
            
        except Exception as e:
            self.logger.error(f"Lỗi add working proxy: {e}")
    
    def add_failed_proxy(self, proxy_info, error_msg=""):
        """Thêm proxy thất bại vào cache"""
        try:
            proxy_key = f"{proxy_info['host']}:{proxy_info['port']}"
            
            # Kiểm tra đã có chưa
            for cached_proxy in self.cache_data['failed_proxies']:
                if cached_proxy['key'] == proxy_key:
                    cached_proxy['fail_count'] = cached_proxy.get('fail_count', 0) + 1
                    cached_proxy['last_failed'] = datetime.now().isoformat()
                    cached_proxy['last_error'] = error_msg
                    self._save_cache()
                    return
            
            # Thêm mới
            proxy_cache_entry = {
                'key': proxy_key,
                'host': proxy_info['host'],
                'port': proxy_info['port'],
                'first_failed': datetime.now().isoformat(),
                'last_failed': datetime.now().isoformat(),
                'fail_count': 1,
                'last_error': error_msg
            }
            
            self.cache_data['failed_proxies'].append(proxy_cache_entry)
            self._save_cache()
            
        except Exception as e:
            self.logger.error(f"Lỗi add failed proxy: {e}")
    
    def get_working_proxies(self, max_age_hours=24):
        """Lấy danh sách proxy hoạt động (chưa quá cũ)"""
        try:
            working_proxies = []
            cutoff_time = datetime.now() - timedelta(hours=max_age_hours)
            
            for proxy in self.cache_data['working_proxies']:
                try:
                    last_tested = datetime.fromisoformat(proxy['last_tested'])
                    if last_tested > cutoff_time:
                        working_proxies.append(proxy)
                except:
                    # Nếu không parse được date, bỏ qua
                    continue
            
            self.logger.info(f"📋 Found {len(working_proxies)} cached working proxies (max age: {max_age_hours}h)")
            return working_proxies
            
        except Exception as e:
            self.logger.error(f"Lỗi get working proxies: {e}")
            return []
    
    def is_proxy_failed(self, proxy_info, max_age_hours=6):
        """Kiểm tra proxy có trong danh sách failed không"""
        try:
            proxy_key = f"{proxy_info['host']}:{proxy_info['port']}"
            cutoff_time = datetime.now() - timedelta(hours=max_age_hours)
            
            for proxy in self.cache_data['failed_proxies']:
                if proxy['key'] == proxy_key:
                    try:
                        last_failed = datetime.fromisoformat(proxy['last_failed'])
                        if last_failed > cutoff_time:
                            return True
                    except:
                        continue
            
            return False
            
        except Exception as e:
            self.logger.error(f"Lỗi check failed proxy: {e}")
            return False
    
    def get_cache_stats(self):
        """Lấy thống kê cache"""
        try:
            working_count = len(self.cache_data['working_proxies'])
            failed_count = len(self.cache_data['failed_proxies'])
            
            # Count recent working proxies (last 24h)
            recent_working = len(self.get_working_proxies(24))
            
            last_updated = self.cache_data.get('last_updated')
            if last_updated:
                try:
                    last_updated_dt = datetime.fromisoformat(last_updated)
                    last_updated_str = last_updated_dt.strftime("%Y-%m-%d %H:%M:%S")
                except:
                    last_updated_str = last_updated
            else:
                last_updated_str = "Never"
            
            return {
                'total_working': working_count,
                'recent_working': recent_working,
                'total_failed': failed_count,
                'last_updated': last_updated_str
            }
            
        except Exception as e:
            self.logger.error(f"Lỗi get cache stats: {e}")
            return {
                'total_working': 0,
                'recent_working': 0,
                'total_failed': 0,
                'last_updated': 'Error'
            }
    
    def clear_cache(self):
        """Xóa cache"""
        try:
            self.cache_data = {
                'working_proxies': [],
                'failed_proxies': [],
                'last_updated': None
            }
            self._save_cache()
            self.logger.info("✓ Cleared proxy cache")
        except Exception as e:
            self.logger.error(f"Lỗi clear cache: {e}")
    
    def cleanup_old_entries(self, max_age_days=7):
        """Cleanup các entries cũ"""
        try:
            cutoff_time = datetime.now() - timedelta(days=max_age_days)
            
            # Cleanup working proxies
            original_working = len(self.cache_data['working_proxies'])
            self.cache_data['working_proxies'] = [
                proxy for proxy in self.cache_data['working_proxies']
                if datetime.fromisoformat(proxy['last_tested']) > cutoff_time
            ]
            
            # Cleanup failed proxies
            original_failed = len(self.cache_data['failed_proxies'])
            self.cache_data['failed_proxies'] = [
                proxy for proxy in self.cache_data['failed_proxies']
                if datetime.fromisoformat(proxy['last_failed']) > cutoff_time
            ]
            
            removed_working = original_working - len(self.cache_data['working_proxies'])
            removed_failed = original_failed - len(self.cache_data['failed_proxies'])
            
            if removed_working > 0 or removed_failed > 0:
                self.logger.info(f"🧹 Cleaned up {removed_working} old working + {removed_failed} old failed proxies")
                self._save_cache()
            
        except Exception as e:
            self.logger.error(f"Lỗi cleanup cache: {e}")

# Test function
if __name__ == "__main__":
    logging.basicConfig(level=logging.INFO)
    
    cache = ProxyCache()
    
    # Test add working proxy
    test_proxy = {
        'host': '127.0.0.1',
        'port': 8080,
        'type': 'http',
        'response_time': 1.5
    }
    
    cache.add_working_proxy(test_proxy)
    
    # Get stats
    stats = cache.get_cache_stats()
    print(f"Cache stats: {stats}")
    
    # Get working proxies
    working = cache.get_working_proxies()
    print(f"Working proxies: {len(working)}")
