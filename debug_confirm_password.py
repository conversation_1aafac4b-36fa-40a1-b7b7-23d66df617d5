#!/usr/bin/env python3
"""
Debug confirm password field trên 13win
"""

import os
import sys
import time

# Thêm src vào path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.support.ui import WebDriverWait

def debug_confirm_password():
    """Debug confirm password field"""
    print("🔍 DEBUG CONFIRM PASSWORD FIELD")
    print("=" * 50)
    
    driver = None
    try:
        # Setup Chrome
        options = Options()
        options.add_argument("--no-sandbox")
        options.add_argument("--disable-dev-shm-usage")
        options.add_argument("--disable-gpu")
        
        # ChromeDriver
        chromedriver_path = "chromedriver.exe"
        if os.path.exists(chromedriver_path):
            service = Service(chromedriver_path)
            print("✓ Sử dụng ChromeDriver local")
        else:
            print("❌ Không tìm thấy chromedriver.exe")
            return False
        
        print("🚀 Khởi tạo Chrome...")
        driver = webdriver.Chrome(service=service, options=options)
        print("✓ Chrome đã sẵn sàng")
        
        # Truy cập 13win
        url = "https://13win16.com"
        print(f"\n📍 Truy cập: {url}")
        driver.get(url)
        
        # Chờ page load
        print("⏳ Chờ trang load...")
        WebDriverWait(driver, 15).until(
            lambda d: d.execute_script("return document.readyState") == "complete"
        )
        
        current_url = driver.current_url
        print(f"✓ Current URL: {current_url}")
        
        # Chờ JS load form
        print("⏳ Chờ JavaScript load form...")
        time.sleep(5)
        
        # Tìm tất cả inputs
        inputs = driver.find_elements(By.TAG_NAME, "input")
        print(f"\n📝 Tìm thấy {len(inputs)} inputs:")
        
        # Phân loại inputs chi tiết
        password_inputs = []
        confirm_inputs = []
        
        for i, inp in enumerate(inputs):
            try:
                placeholder = inp.get_attribute("placeholder") or ""
                type_attr = inp.get_attribute("type") or "text"
                name_attr = inp.get_attribute("name") or ""
                id_attr = inp.get_attribute("id") or ""
                
                print(f"\n  {i+1}. Input details:")
                print(f"      type: '{type_attr}'")
                print(f"      placeholder: '{placeholder}'")
                print(f"      name: '{name_attr}'")
                print(f"      id: '{id_attr}'")
                
                # Phân loại
                if "mật khẩu" in placeholder.lower():
                    if "xác nhận" in placeholder.lower():
                        confirm_inputs.append((i+1, inp, placeholder))
                        print(f"      → CONFIRM PASSWORD FIELD ⭐")
                    else:
                        password_inputs.append((i+1, inp, placeholder))
                        print(f"      → PASSWORD FIELD")
                elif type_attr == "password":
                    if len(password_inputs) == 0:
                        password_inputs.append((i+1, inp, "password type"))
                        print(f"      → PASSWORD FIELD (by type)")
                    else:
                        confirm_inputs.append((i+1, inp, "password type"))
                        print(f"      → CONFIRM PASSWORD FIELD (by type) ⭐")
                        
            except Exception as e:
                print(f"  {i+1}. Error: {e}")
        
        print(f"\n📊 Phân loại:")
        print(f"Password fields: {len(password_inputs)}")
        print(f"Confirm password fields: {len(confirm_inputs)}")
        
        # Test điền confirm password
        if confirm_inputs:
            print(f"\n✏️ Test điền confirm password...")
            
            test_password = "TestPass123!"
            
            for idx, (num, element, desc) in enumerate(confirm_inputs):
                try:
                    print(f"\n--- Test confirm field {idx+1}: {desc} ---")
                    
                    # Highlight
                    driver.execute_script("arguments[0].scrollIntoView(true);", element)
                    time.sleep(0.5)
                    driver.execute_script("arguments[0].style.border='5px solid red'", element)
                    
                    # Clear và điền
                    element.clear()
                    time.sleep(0.5)
                    element.send_keys(test_password)
                    
                    # Verify
                    current_value = element.get_attribute("value")
                    print(f"✅ Đã điền: '{current_value}'")
                    
                    time.sleep(2)
                    
                except Exception as e:
                    print(f"❌ Lỗi điền field {idx+1}: {e}")
        else:
            print("\n❌ Không tìm thấy confirm password field!")
            
            # Fallback: tìm theo các selector khác
            print("\n🔍 Fallback search...")
            
            selectors = [
                'input[placeholder="Vui lòng xác nhận lại mật khẩu !"]',
                'input[placeholder*="Vui lòng xác nhận lại mật khẩu"]',
                'input[placeholder*="xác nhận lại mật khẩu"]',
                'input[placeholder*="xác nhận"]',
                'input[name="confirm_password"]',
                'input[id="confirm_password"]'
            ]
            
            for selector in selectors:
                try:
                    elements = driver.find_elements(By.CSS_SELECTOR, selector)
                    if elements:
                        print(f"✓ Tìm thấy với selector: {selector}")
                        element = elements[0]
                        
                        # Test điền
                        driver.execute_script("arguments[0].scrollIntoView(true);", element)
                        driver.execute_script("arguments[0].style.border='5px solid lime'", element)
                        element.clear()
                        element.send_keys("TestPass123!")
                        print(f"✅ Đã điền thành công!")
                        time.sleep(2)
                        break
                    else:
                        print(f"❌ Không tìm thấy với: {selector}")
                except Exception as e:
                    print(f"❌ Lỗi với selector {selector}: {e}")
        
        # Chờ để user xem
        print("\n⏳ Chờ 15 giây để bạn xem kết quả...")
        time.sleep(15)
        
        return True
        
    except Exception as e:
        print(f"❌ Lỗi: {e}")
        return False
        
    finally:
        if driver:
            try:
                driver.quit()
                print("✓ Đã đóng browser")
            except:
                pass

def main():
    """Main function"""
    try:
        success = debug_confirm_password()
        
        if success:
            print("\n🎉 DEBUG HOÀN THÀNH!")
            print("Kiểm tra xem confirm password field có được điền không.")
        else:
            print("\n❌ DEBUG THẤT BẠI!")
            
    except KeyboardInterrupt:
        print("\n\nĐã hủy bởi người dùng.")
    except Exception as e:
        print(f"\nLỗi: {e}")

if __name__ == "__main__":
    main()
