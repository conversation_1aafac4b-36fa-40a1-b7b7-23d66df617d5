#!/usr/bin/env python3
"""
Simple test để kiểm tra 13win website
"""

import os
import time
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service

def simple_test():
    """Test đơn giản 13win"""
    print("🔍 SIMPLE 13WIN TEST")
    print("=" * 40)
    
    driver = None
    try:
        # Setup Chrome với config minimal
        options = Options()
        options.add_argument("--no-sandbox")
        options.add_argument("--disable-dev-shm-usage")
        options.add_argument("--disable-gpu")
        
        # Sử dụng ChromeDriver local nếu có
        chromedriver_path = "chromedriver.exe"
        if os.path.exists(chromedriver_path):
            service = Service(chromedriver_path)
            print(f"✓ Sử dụng ChromeDriver local")
        else:
            print("❌ Không tìm thấy chromedriver.exe")
            print("Chạy 'python quick_fix_chrome.py' để tải ChromeDriver")
            return False
        
        print("🚀 Khởi tạo Chrome...")
        driver = webdriver.Chrome(service=service, options=options)
        print("✓ Chrome đã sẵn sàng")
        
        # Test các URL khác nhau
        test_urls = [
            "https://13win16.com",
            "https://www.13win16.com", 
            "http://13win16.com",
            "https://13win.com"
        ]
        
        working_url = None
        for url in test_urls:
            try:
                print(f"\n📍 Thử URL: {url}")
                driver.get(url)
                time.sleep(3)
                
                current_url = driver.current_url
                title = driver.title
                
                print(f"  Current URL: {current_url}")
                print(f"  Title: {title}")
                
                # Kiểm tra có load được không
                if title and len(title) > 0:
                    print(f"  ✓ Website load thành công")
                    working_url = url
                    break
                else:
                    print(f"  ✗ Website không load được")
                    
            except Exception as e:
                print(f"  ✗ Lỗi: {e}")
                continue
        
        if not working_url:
            print("\n❌ Không thể truy cập website 13win")
            print("Kiểm tra:")
            print("1. Kết nối internet")
            print("2. URL có đúng không")
            print("3. Website có bị block không")
            return False
        
        print(f"\n✅ Sử dụng URL: {working_url}")
        
        # Tìm link đăng ký
        print("\n🔍 Tìm link đăng ký...")
        
        # Tìm tất cả links
        links = driver.find_elements(By.TAG_NAME, "a")
        print(f"Tìm thấy {len(links)} links")
        
        register_links = []
        register_keywords = ["đăng ký", "register", "sign up", "signup", "dang ky"]
        
        for link in links:
            try:
                href = link.get_attribute('href') or ""
                text = link.text.strip().lower()
                
                # Kiểm tra text hoặc href có chứa keyword đăng ký
                for keyword in register_keywords:
                    if keyword in text or keyword in href.lower():
                        register_links.append({
                            'element': link,
                            'href': href,
                            'text': link.text.strip(),
                            'keyword': keyword
                        })
                        print(f"  ✓ Tìm thấy: '{link.text.strip()}' -> {href}")
                        break
                        
            except Exception as e:
                continue
        
        if not register_links:
            print("❌ Không tìm thấy link đăng ký")
            
            # Log một số links để debug
            print("\n📋 Một số links trên trang:")
            for i, link in enumerate(links[:10]):
                try:
                    href = link.get_attribute('href') or 'no-href'
                    text = link.text.strip() or 'no-text'
                    print(f"  {i+1}. '{text}' -> {href}")
                except:
                    pass
            return False
        
        # Thử click vào link đăng ký đầu tiên
        print(f"\n🖱️ Click vào: {register_links[0]['text']}")
        register_links[0]['element'].click()
        time.sleep(5)
        
        # Kiểm tra trang đăng ký
        current_url = driver.current_url
        title = driver.title
        print(f"Register URL: {current_url}")
        print(f"Register title: {title}")
        
        # Tìm form inputs
        print("\n📝 Tìm form inputs...")
        inputs = driver.find_elements(By.TAG_NAME, "input")
        print(f"Tìm thấy {len(inputs)} inputs:")
        
        input_info = []
        for i, inp in enumerate(inputs):
            try:
                name = inp.get_attribute("name") or ""
                id_attr = inp.get_attribute("id") or ""
                type_attr = inp.get_attribute("type") or "text"
                placeholder = inp.get_attribute("placeholder") or ""
                
                info = {
                    'index': i+1,
                    'name': name,
                    'id': id_attr,
                    'type': type_attr,
                    'placeholder': placeholder
                }
                input_info.append(info)
                
                print(f"  {i+1}. type='{type_attr}', name='{name}', id='{id_attr}'")
                if placeholder:
                    print(f"      placeholder='{placeholder}'")
                    
            except Exception as e:
                print(f"  {i+1}. Error: {e}")
        
        # Test điền một input
        print("\n✏️ Test điền form...")
        
        # Tìm input username/email
        username_input = None
        for inp in inputs:
            try:
                name = inp.get_attribute("name") or ""
                type_attr = inp.get_attribute("type") or ""
                placeholder = inp.get_attribute("placeholder") or ""
                
                if any(keyword in name.lower() for keyword in ["user", "email", "login"]) or \
                   any(keyword in placeholder.lower() for keyword in ["user", "email", "tên"]) or \
                   type_attr == "email":
                    username_input = inp
                    print(f"✓ Tìm thấy input username/email")
                    break
            except:
                continue
        
        if username_input:
            try:
                username_input.clear()
                username_input.send_keys("testuser123")
                print("✓ Đã điền test username")
                
                # Highlight để user thấy
                driver.execute_script("arguments[0].style.border='3px solid red'", username_input)
                
            except Exception as e:
                print(f"✗ Lỗi điền username: {e}")
        else:
            print("❌ Không tìm thấy input username/email")
        
        # Chờ để user xem
        print("\n⏳ Chờ 10 giây để bạn xem kết quả...")
        time.sleep(10)
        
        return True
        
    except Exception as e:
        print(f"❌ Lỗi: {e}")
        return False
        
    finally:
        if driver:
            try:
                driver.quit()
                print("✓ Đã đóng browser")
            except:
                pass

def main():
    """Main function"""
    try:
        success = simple_test()
        
        if success:
            print("\n🎉 TEST THÀNH CÔNG!")
            print("Website 13win có thể truy cập và có form đăng ký.")
        else:
            print("\n❌ TEST THẤT BẠI!")
            print("Kiểm tra lại website hoặc kết nối mạng.")
            
    except KeyboardInterrupt:
        print("\n\nĐã hủy bởi người dùng.")
    except Exception as e:
        print(f"\nLỗi: {e}")

if __name__ == "__main__":
    main()
