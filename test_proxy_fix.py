#!/usr/bin/env python3
"""
Test proxy fix - fallback và validation
"""

import sys
import os
import logging

# Add src to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def test_proxy_validation():
    """Test proxy validation function"""
    print("=== TEST PROXY VALIDATION ===")
    
    try:
        from win13_registration_service import Win13RegistrationService
        from proxy_manager import ProxyInfo
        
        # Setup logging
        logging.basicConfig(level=logging.WARNING)
        
        # Create service
        service = Win13RegistrationService()
        
        # Test với proxy giả
        print("1. Test với proxy không hoạt động...")
        fake_proxy = ProxyInfo("192.168.1.999", 8080)
        result = service.quick_validate_proxy(fake_proxy)
        print(f"✓ Fake proxy validation result: {result} (should be False)")
        
        # Test với proxy có thể hoạt động
        print("2. Test với proxy có thể hoạt động...")
        real_proxy = ProxyInfo("8.8.8.8", 80)  # Google DNS, có thể không phải proxy
        result = service.quick_validate_proxy(real_proxy)
        print(f"✓ Real proxy validation result: {result}")
        
        # Test validation enable/disable
        print("3. Test validation enable/disable...")
        service.validate_proxy = False
        print(f"✓ Validation disabled: {not service.validate_proxy}")
        
        service.validate_proxy = True
        print(f"✓ Validation enabled: {service.validate_proxy}")
        
        print("\n🎉 PROXY VALIDATION TEST OK!")
        return True
        
    except Exception as e:
        print(f"❌ Lỗi test: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_error_handling():
    """Test error handling cho proxy"""
    print("\n=== TEST ERROR HANDLING ===")
    
    try:
        from win13_registration_service import Win13RegistrationService
        
        service = Win13RegistrationService()
        
        # Test error message detection
        print("1. Test ERR_TUNNEL_CONNECTION_FAILED detection...")
        error_msg = "Message: unknown error: net::ERR_TUNNEL_CONNECTION_FAILED"
        
        if "ERR_TUNNEL_CONNECTION_FAILED" in error_msg:
            print("✓ Error detection works")
        else:
            print("❌ Error detection failed")
            return False
        
        # Test ERR_PROXY detection
        print("2. Test ERR_PROXY detection...")
        error_msg2 = "net::ERR_PROXY_CONNECTION_FAILED"
        
        if "ERR_PROXY" in error_msg2:
            print("✓ Proxy error detection works")
        else:
            print("❌ Proxy error detection failed")
            return False
        
        print("\n🎉 ERROR HANDLING TEST OK!")
        return True
        
    except Exception as e:
        print(f"❌ Lỗi test: {e}")
        return False

def test_fallback_logic():
    """Test fallback logic"""
    print("\n=== TEST FALLBACK LOGIC ===")
    
    try:
        from win13_registration_service import Win13RegistrationService
        
        service = Win13RegistrationService()
        
        # Test URL setting
        print("1. Test URL setting...")
        original_url = service.registration_url
        print(f"✓ Original URL: {original_url}")
        
        # Test validate_proxy setting
        print("2. Test validate_proxy setting...")
        service.validate_proxy = False
        print(f"✓ Validation disabled: {not service.validate_proxy}")
        
        service.validate_proxy = True
        print(f"✓ Validation enabled: {service.validate_proxy}")
        
        print("\n🎉 FALLBACK LOGIC TEST OK!")
        return True
        
    except Exception as e:
        print(f"❌ Lỗi test: {e}")
        return False

def main():
    """Main test function"""
    print("🧪 TEST PROXY FIXES\n")
    
    results = []
    
    # Test proxy validation
    results.append(test_proxy_validation())
    
    # Test error handling
    results.append(test_error_handling())
    
    # Test fallback logic
    results.append(test_fallback_logic())
    
    # Summary
    print(f"\n{'='*50}")
    print("📊 KẾT QUẢ:")
    print(f"✓ Thành công: {sum(results)}/{len(results)}")
    print(f"❌ Thất bại: {len(results) - sum(results)}/{len(results)}")
    
    if all(results):
        print("\n🎉 TẤT CẢ PROXY FIXES HOẠT ĐỘNG!")
        print("Tool sẽ:")
        print("- Validate proxy trước khi dùng")
        print("- Fallback về direct connection nếu proxy lỗi")
        print("- Retry registration không proxy nếu cần")
        return True
    else:
        print("\n⚠️ CÓ LỖI CẦN FIX")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
