#!/usr/bin/env python3
"""
Chrome Stability Manager - Ultimate solution for Chrome crashes
"""

import os
import time
import random
import tempfile
import shutil
import psutil
import logging
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service

class ChromeStabilityManager:
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.active_drivers = []
        self.temp_dirs = []

    def kill_all_chrome_processes(self):
        """Kill tất cả Chrome processes để cleanup"""
        try:
            self.logger.info("🧹 Cleanup Chrome processes...")
            killed_count = 0

            for proc in psutil.process_iter(['pid', 'name']):
                try:
                    if 'chrome' in proc.info['name'].lower():
                        proc.kill()
                        killed_count += 1
                except (psutil.NoSuchProcess, psutil.AccessDenied):
                    pass

            if killed_count > 0:
                self.logger.info(f"✓ Killed {killed_count} Chrome processes")
                time.sleep(2)  # Ch<PERSON> processes cleanup

        except Exception as e:
            self.logger.warning(f"Lỗi cleanup Chrome: {e}")

    def create_isolated_profile(self):
        """Tạo profile Chrome hoàn toàn isolated"""
        try:
            # Tạo temp directory unique
            profile_dir = tempfile.mkdtemp(prefix="chrome_profile_", suffix=f"_{random.randint(10000, 99999)}")
            self.temp_dirs.append(profile_dir)

            # Tạo các subdirectories cần thiết
            os.makedirs(os.path.join(profile_dir, "Default"), exist_ok=True)
            os.makedirs(os.path.join(profile_dir, "ShaderCache"), exist_ok=True)

            self.logger.info(f"✓ Created isolated profile: {profile_dir}")
            return profile_dir

        except Exception as e:
            self.logger.error(f"Lỗi tạo profile: {e}")
            return None

    def get_ultra_stable_options(self, profile_path=None):
        """Chrome options cực kỳ ổn định"""
        options = Options()

        # Core stability
        options.add_argument("--no-sandbox")
        options.add_argument("--disable-dev-shm-usage")
        options.add_argument("--disable-gpu")
        options.add_argument("--disable-gpu-sandbox")
        options.add_argument("--disable-software-rasterizer")

        # Profile isolation
        if profile_path:
            options.add_argument(f"--user-data-dir={profile_path}")

        # Process management (KHÔNG dùng single-process để tránh disconnect)
        # options.add_argument("--single-process")  # Gây disconnect với 13win
        options.add_argument("--no-zygote")
        options.add_argument("--process-per-site")  # Thay thế single-process
        options.add_argument("--disable-background-networking")
        options.add_argument("--disable-background-timer-throttling")
        options.add_argument("--disable-renderer-backgrounding")
        options.add_argument("--disable-backgrounding-occluded-windows")

        # Memory optimization
        options.add_argument("--memory-pressure-off")
        options.add_argument("--max_old_space_size=1024")  # Giảm memory
        options.add_argument("--aggressive-cache-discard")

        # Disable features (KHÔNG disable JavaScript để form hoạt động)
        options.add_argument("--disable-extensions")
        options.add_argument("--disable-plugins")
        options.add_argument("--disable-images")
        # options.add_argument("--disable-javascript")  # KHÔNG disable JS cho 13win form
        options.add_argument("--disable-default-apps")
        options.add_argument("--disable-sync")
        options.add_argument("--disable-translate")
        options.add_argument("--disable-web-security")
        options.add_argument("--disable-features=TranslateUI")
        options.add_argument("--disable-features=VizDisplayCompositor")
        options.add_argument("--disable-ipc-flooding-protection")

        # DevTools fix
        options.add_argument("--remote-debugging-port=0")  # Random port
        options.add_argument("--disable-dev-tools")

        # Audio/Video
        options.add_argument("--mute-audio")
        options.add_argument("--disable-audio-output")

        # Network
        options.add_argument("--aggressive-cache-discard")
        options.add_argument("--disable-background-networking")

        # Security
        options.add_argument("--ignore-certificate-errors")
        options.add_argument("--ignore-ssl-errors")
        options.add_argument("--ignore-certificate-errors-spki-list")
        options.add_argument("--ignore-certificate-errors-ssl")
        options.add_argument("--allow-running-insecure-content")

        # Automation
        options.add_argument("--disable-blink-features=AutomationControlled")
        options.add_experimental_option("excludeSwitches", ["enable-automation"])
        options.add_experimental_option('useAutomationExtension', False)

        # Logging
        options.add_argument("--log-level=3")  # Suppress logs
        options.add_argument("--silent")

        return options

    def create_stable_driver(self, proxy=None, force_no_proxy=False):
        """Tạo Chrome driver cực kỳ ổn định"""
        driver = None
        profile_path = None

        try:
            # Cleanup trước khi tạo
            self.kill_all_chrome_processes()

            # Tạo isolated profile
            profile_path = self.create_isolated_profile()
            if not profile_path:
                raise Exception("Không thể tạo profile")

            # Chrome options
            options = self.get_ultra_stable_options(profile_path)

            # Force no proxy nếu được yêu cầu
            if force_no_proxy:
                proxy = None
                self.logger.info("🚫 Force no proxy mode")

            # Proxy config với validation
            if proxy:
                try:
                    proxy_str = f"{proxy['host']}:{proxy['port']}"

                    # Test proxy trước khi sử dụng
                    if self._test_proxy_connection(proxy):
                        options.add_argument(f"--proxy-server=http://{proxy_str}")
                        self.logger.info(f"✓ Configured working proxy: {proxy_str}")
                    else:
                        self.logger.warning(f"⚠️ Proxy {proxy_str} không hoạt động, bỏ qua proxy")
                        proxy = None  # Disable proxy nếu không hoạt động

                except Exception as proxy_error:
                    self.logger.warning(f"⚠️ Lỗi cấu hình proxy: {proxy_error}, bỏ qua proxy")
                    proxy = None

            # ChromeDriver service
            chromedriver_path = "chromedriver.exe"
            if not os.path.exists(chromedriver_path):
                raise Exception("ChromeDriver not found")

            service = Service(chromedriver_path)

            # Suppress service logs
            try:
                if os.name == 'nt':  # Windows
                    service.log_path = "NUL"
                else:  # Linux/Mac
                    service.log_path = "/dev/null"
            except:
                pass

            self.logger.info("🚀 Creating ultra-stable Chrome...")

            # Create driver với timeout
            driver = webdriver.Chrome(service=service, options=options)

            # Set timeouts
            driver.set_page_load_timeout(30)
            driver.implicitly_wait(10)

            # Test basic functionality
            driver.get("data:text/html,<html><body><h1>Test</h1></body></html>")

            self.active_drivers.append(driver)
            self.logger.info("✅ Chrome created successfully!")

            return driver

        except Exception as e:
            self.logger.error(f"❌ Failed to create Chrome: {e}")

            # Cleanup on failure
            if driver:
                try:
                    driver.quit()
                except:
                    pass

            if profile_path and os.path.exists(profile_path):
                try:
                    shutil.rmtree(profile_path)
                except:
                    pass

            return None

    def _test_proxy_connection(self, proxy):
        """Test proxy connection trước khi sử dụng"""
        try:
            import requests

            proxy_url = f"http://{proxy['host']}:{proxy['port']}"
            proxies = {
                'http': proxy_url,
                'https': proxy_url
            }

            # Test với timeout ngắn
            response = requests.get(
                "http://httpbin.org/ip",
                proxies=proxies,
                timeout=5
            )

            if response.status_code == 200:
                self.logger.info(f"✓ Proxy {proxy['host']}:{proxy['port']} hoạt động")
                return True
            else:
                self.logger.warning(f"⚠️ Proxy {proxy['host']}:{proxy['port']} trả về status {response.status_code}")
                return False

        except Exception as e:
            self.logger.warning(f"⚠️ Proxy {proxy['host']}:{proxy['port']} test failed: {e}")
            return False

    def cleanup_driver(self, driver):
        """Cleanup driver safely"""
        try:
            if driver in self.active_drivers:
                self.active_drivers.remove(driver)

            driver.quit()
            self.logger.info("✓ Driver cleaned up")

        except Exception as e:
            self.logger.warning(f"Lỗi cleanup driver: {e}")

    def cleanup_all(self):
        """Cleanup tất cả resources"""
        try:
            # Cleanup drivers
            for driver in self.active_drivers[:]:
                try:
                    driver.quit()
                except:
                    pass
            self.active_drivers.clear()

            # Cleanup temp directories
            for temp_dir in self.temp_dirs:
                try:
                    if os.path.exists(temp_dir):
                        shutil.rmtree(temp_dir)
                except:
                    pass
            self.temp_dirs.clear()

            # Kill remaining Chrome processes
            self.kill_all_chrome_processes()

            self.logger.info("✓ All resources cleaned up")

        except Exception as e:
            self.logger.error(f"Lỗi cleanup: {e}")

# Test function
if __name__ == "__main__":
    logging.basicConfig(level=logging.INFO)

    manager = ChromeStabilityManager()

    try:
        print("🧪 Testing Chrome Stability Manager...")

        # Test single driver
        driver = manager.create_stable_driver()
        if driver:
            print("✅ Single driver test: PASS")
            driver.get("https://www.google.com")
            print(f"✅ Navigation test: {driver.title}")
            manager.cleanup_driver(driver)
        else:
            print("❌ Single driver test: FAIL")

        # Test multiple drivers
        drivers = []
        for i in range(2):
            print(f"Creating driver {i+1}...")
            driver = manager.create_stable_driver()
            if driver:
                drivers.append(driver)
                time.sleep(3)  # Delay between drivers

        print(f"✅ Created {len(drivers)} drivers successfully")

        # Cleanup
        for driver in drivers:
            manager.cleanup_driver(driver)

    except Exception as e:
        print(f"❌ Test failed: {e}")

    finally:
        manager.cleanup_all()
        print("✓ Test completed")
